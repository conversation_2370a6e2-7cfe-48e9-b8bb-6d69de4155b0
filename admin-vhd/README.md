# 🥋 Hệ Thống Quản Lý Học Viên Võ Thuật 3 Cấp

## 📋 Tổng Quan

Hệ thống quản lý học viên võ thuật với cấu trúc 3 cấp độ: **Quốc gia → <PERSON><PERSON><PERSON><PERSON>hố → Phường/Xã → C<PERSON>u lạc bộ**. Hỗ trợ đầy đủ quy trình đăng ký, đ<PERSON><PERSON> tạo, thi cử, cấp chứng chỉ và quản lý học viên.

## 🏗️ Kiến Trúc Hệ Thống

### Technology Stack
- **Backend:** Java 17 + Spring Boot 3.2
- **Database:** MySQL 8.0 + Redis 7.0
- **Message Queue:** Apache Kafka 3.5
- **Security:** Spring Security 6 + JWT
- **Documentation:** OpenAPI 3 (Swagger)
- **Build Tool:** Maven 3.9

### Cấu Trú<PERSON> D<PERSON>n
```
admin-vhd/
├── src/main/java/vn/gov/martial/
│   ├── config/              # C<PERSON><PERSON> hình hệ thống
│   ├── controller/          # REST API Controllers
│   ├── service/             # Business Logic Services
│   ├── repository/          # Data Access Layer
│   ├── entity/              # JPA Entities
│   ├── dto/                 # Data Transfer Objects
│   ├── security/            # Security & Authentication
│   ├── exception/           # Exception Handling
│   ├── util/                # Utility Classes
│   └── AdminVhdApplication.java
├── src/main/resources/
│   ├── application.yml      # Configuration
│   └── db/migration/        # Database Scripts
└── pom.xml                  # Maven Dependencies
```

## 🚀 Khởi Động Nhanh

### 1. Yêu Cầu Hệ Thống
- Java 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 7.0+
- Apache Kafka 3.5+

### 2. Cài Đặt Dependencies

#### MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# macOS
brew install mysql

# Start MySQL
sudo systemctl start mysql
```

#### Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis

# Start Redis
redis-server
```

#### Kafka
```bash
# Download và giải nén Kafka
wget https://downloads.apache.org/kafka/2.13-3.5.0/kafka_2.13-3.5.0.tgz
tar -xzf kafka_2.13-3.5.0.tgz
cd kafka_2.13-3.5.0

# Start Zookeeper
bin/zookeeper-server-start.sh config/zookeeper.properties

# Start Kafka
bin/kafka-server-start.sh config/server.properties
```

### 3. Cấu Hình Database

```sql
-- Tạo database
CREATE DATABASE martial_arts_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tạo user
CREATE USER 'martial_user'@'localhost' IDENTIFIED BY 'martial_password';
GRANT ALL PRIVILEGES ON martial_arts_db.* TO 'martial_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. Cấu Hình Environment Variables

```bash
# Database
export DB_USERNAME=martial_user
export DB_PASSWORD=martial_password

# Redis
export REDIS_HOST=localhost
export REDIS_PORT=6379

# Kafka
export KAFKA_SERVERS=localhost:9092

# JWT
export JWT_SECRET=mySecretKey123456789012345678901234567890
```

### 5. Build và Chạy Ứng Dụng

```bash
# Clone repository
git clone <repository-url>
cd admin-vhd

# Build project
mvn clean compile

# Run application
mvn spring-boot:run

# Hoặc build JAR và chạy
mvn clean package
java -jar target/admin-vhd-1.0.0.jar
```

### 6. Kiểm Tra Ứng Dụng

- **Application:** http://localhost:8080/api/v1
- **Swagger UI:** http://localhost:8080/api/v1/swagger-ui.html
- **Health Check:** http://localhost:8080/api/v1/actuator/health

## 📊 Cấu Trúc Database

### Core Entities

#### Users (Người dùng)
```sql
- id (PK)
- username (unique)
- email (unique)
- password (encrypted)
- full_name
- role (SUPER_ADMIN, CITY_ADMIN, WARD_ADMIN, INSTRUCTOR, STUDENT, PARENT)
- status (ACTIVE, INACTIVE, SUSPENDED, PENDING_APPROVAL, LOCKED)
- organization_id (FK)
- organization_level (NATIONAL, CITY, WARD, CLUB)
```

#### Organizations (Tổ chức)
```sql
- id (PK)
- name
- type (NATIONAL_BUREAU, CITY_DEPARTMENT, WARD_OFFICE, MARTIAL_ARTS_CLUB)
- level (NATIONAL, CITY, WARD, CLUB)
- parent_id (FK - self reference)
- hierarchy_path
- status (PENDING_APPROVAL, ACTIVE, SUSPENDED, INACTIVE)
```

#### Students (Học viên)
```sql
- id (PK)
- student_code (unique)
- full_name
- date_of_birth
- organization_id (FK)
- instructor_id (FK)
- martial_art_id (FK)
- current_belt_level
- status (ACTIVE, GRADUATED, SUSPENDED, TRANSFERRED, DROPPED_OUT)
```

#### Exams (Kỳ thi)
```sql
- id (PK)
- exam_code (unique)
- title
- type (BELT_PROMOTION, SKILL_CERTIFICATION, INSTRUCTOR_CERTIFICATION)
- level (CLUB_LEVEL, WARD_LEVEL, CITY_LEVEL, NATIONAL_LEVEL)
- exam_date
- status (DRAFT, PENDING_APPROVAL, APPROVED, REGISTRATION_OPEN, COMPLETED)
```

## 🔐 Hệ Thống Phân Quyền

### Roles & Permissions

#### Super Admin (Cấp Quốc Gia)
- Quản lý toàn bộ hệ thống
- Tạo/sửa/xóa City Admin
- Xem báo cáo toàn quốc
- Cấu hình hệ thống toàn cục

#### City Admin (Cấp Thành Phố)
- Quản lý đơn vị trong thành phố
- Tạo/sửa/xóa Ward Admin
- Phê duyệt đơn vị cấp phường
- Tổ chức thi cử cấp thành phố

#### Ward Admin (Cấp Phường/Xã)
- Quản lý CLB trong phường
- Tạo/sửa tài khoản Instructor
- Phê duyệt đăng ký học viên
- Tổ chức thi cử cấp phường

#### Instructor (Huấn Luyện Viên)
- Quản lý học viên trong lớp
- Điểm danh và đánh giá
- Đề xuất thi cử
- Liên hệ phụ huynh

#### Student (Học Viên)
- Xem thông tin cá nhân
- Đăng ký thi cử
- Tải tài liệu học tập
- Xem lịch sử và chứng chỉ

## 📡 API Documentation

### Authentication Endpoints
```
POST /auth/login          # Đăng nhập
POST /auth/logout         # Đăng xuất
POST /auth/refresh        # Refresh token
POST /auth/forgot-password # Quên mật khẩu
```

### User Management
```
GET    /users             # Danh sách người dùng
POST   /users             # Tạo người dùng mới
GET    /users/{id}        # Chi tiết người dùng
PUT    /users/{id}        # Cập nhật người dùng
DELETE /users/{id}        # Xóa người dùng
POST   /users/{id}/approve # Phê duyệt người dùng
```

### Student Management
```
GET    /students          # Danh sách học viên
POST   /students          # Đăng ký học viên mới
GET    /students/{id}     # Chi tiết học viên
PUT    /students/{id}     # Cập nhật học viên
POST   /students/{id}/transfer # Chuyển đơn vị
```

### Exam Management
```
GET    /exams             # Danh sách kỳ thi
POST   /exams             # Tạo kỳ thi mới
GET    /exams/{id}        # Chi tiết kỳ thi
PUT    /exams/{id}        # Cập nhật kỳ thi
POST   /exams/{id}/register # Đăng ký thi
```

## 🔄 Event Streaming với Kafka

### Topics
- `student-events`: Sự kiện học viên (đăng ký, chuyển đơn vị, tốt nghiệp)
- `exam-events`: Sự kiện thi cử (tạo kỳ thi, đăng ký, kết quả)
- `transfer-events`: Sự kiện chuyển đơn vị
- `attendance-events`: Sự kiện điểm danh
- `notification-events`: Sự kiện thông báo
- `audit-events`: Sự kiện audit log

### Event Examples
```json
// Student Registration Event
{
  "eventType": "STUDENT_REGISTERED",
  "studentId": 123,
  "organizationId": 456,
  "timestamp": "2025-01-01T10:00:00Z",
  "data": {
    "studentCode": "HV001",
    "fullName": "Nguyễn Văn A",
    "martialArt": "Karate"
  }
}
```

## 💾 Caching với Redis

### Cache Strategies
- **Users**: 30 phút TTL
- **Students**: 1 giờ TTL
- **Organizations**: 2 giờ TTL
- **Martial Arts**: 4 giờ TTL
- **Statistics**: 15 phút TTL
- **Reports**: 5 phút TTL

### Cache Keys Pattern
```
martial:users:{userId}
martial:students:{studentId}
martial:organizations:{orgId}
martial:stats:daily:{date}
```

## 🧪 Testing

### Unit Tests
```bash
mvn test
```

### Integration Tests
```bash
mvn test -Dtest=**/*IntegrationTest
```

### Test Coverage
```bash
mvn jacoco:report
```

## 📦 Deployment

### Docker
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/admin-vhd-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
      - kafka
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - KAFKA_SERVERS=kafka:9092
```

## 📈 Monitoring & Logging

### Actuator Endpoints
- `/actuator/health` - Health check
- `/actuator/metrics` - Application metrics
- `/actuator/info` - Application info
- `/actuator/prometheus` - Prometheus metrics

### Logging Configuration
```yaml
logging:
  level:
    vn.gov.martial: DEBUG
    org.springframework.security: DEBUG
  file:
    name: logs/martial-arts.log
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Email:** <EMAIL>
- **Documentation:** https://docs.martial-arts.gov.vn
- **Issues:** https://github.com/martial-arts/admin-vhd/issues

---

**🥋 Hệ Thống Quản Lý Học Viên Võ Thuật - Phiên bản 1.0.0**
