package vn.gov.martial;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Hệ Thống Quản Lý Học Viên Võ Thuật 3 Cấp
 * Martial Arts Student Management System - 3 Level Hierarchy
 * 
 * Main Application Class
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-01
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableCaching
@EnableKafka
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class AdminVhdApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminVhdApplication.class, args);
        System.out.println("🥋 Hệ Thống Quản <PERSON>iê<PERSON>huật đã khởi động thành công!");
        System.out.println("📊 Swagger UI: http://localhost:8080/api/v1/swagger-ui.html");
        System.out.println("🔍 Actuator Health: http://localhost:8080/api/v1/actuator/health");
    }
}
