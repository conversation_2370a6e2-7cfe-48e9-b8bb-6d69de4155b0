package vn.gov.martial.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis Configuration - Cấu hình Redis cho caching và session
 * Hỗ trợ distributed caching và session management
 */
@Configuration
@EnableCaching
@RequiredArgsConstructor
public class RedisConfig {
    
    private final RedisConnectionFactory redisConnectionFactory;
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        // JSON serialization
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = 
                new Jackson2JsonRedisSerializer<>(Object.class);
        
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, 
                ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.registerModule(new JavaTimeModule());
        
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        
        // String serialization for keys
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        
        // Set serializers
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    @Bean
    public CacheManager cacheManager() {
        // JSON serializer for cache values
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = 
                new Jackson2JsonRedisSerializer<>(Object.class);
        
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, 
                ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.registerModule(new JavaTimeModule());
        
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        
        // Default cache configuration
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1)) // Default TTL: 1 hour
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(jackson2JsonRedisSerializer))
                .disableCachingNullValues();
        
        // Specific cache configurations
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // User cache - 30 minutes TTL
        cacheConfigurations.put("users", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(30))
                .prefixCacheNameWith("martial:users:"));
        
        // Student cache - 1 hour TTL
        cacheConfigurations.put("students", defaultCacheConfig
                .entryTtl(Duration.ofHours(1))
                .prefixCacheNameWith("martial:students:"));
        
        // Organization cache - 2 hours TTL (changes less frequently)
        cacheConfigurations.put("organizations", defaultCacheConfig
                .entryTtl(Duration.ofHours(2))
                .prefixCacheNameWith("martial:organizations:"));
        
        // Martial arts cache - 4 hours TTL (rarely changes)
        cacheConfigurations.put("martial-arts", defaultCacheConfig
                .entryTtl(Duration.ofHours(4))
                .prefixCacheNameWith("martial:martial-arts:"));
        
        // Exam cache - 30 minutes TTL
        cacheConfigurations.put("exams", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(30))
                .prefixCacheNameWith("martial:exams:"));
        
        // Statistics cache - 15 minutes TTL (frequently updated)
        cacheConfigurations.put("statistics", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(15))
                .prefixCacheNameWith("martial:stats:"));
        
        // Reports cache - 5 minutes TTL (real-time data)
        cacheConfigurations.put("reports", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(5))
                .prefixCacheNameWith("martial:reports:"));
        
        // Session cache - 24 hours TTL
        cacheConfigurations.put("sessions", defaultCacheConfig
                .entryTtl(Duration.ofHours(24))
                .prefixCacheNameWith("martial:sessions:"));
        
        // JWT blacklist cache - 24 hours TTL
        cacheConfigurations.put("jwt-blacklist", defaultCacheConfig
                .entryTtl(Duration.ofHours(24))
                .prefixCacheNameWith("martial:jwt-blacklist:"));
        
        // Attendance cache - 2 hours TTL
        cacheConfigurations.put("attendance", defaultCacheConfig
                .entryTtl(Duration.ofHours(2))
                .prefixCacheNameWith("martial:attendance:"));
        
        // Certificates cache - 4 hours TTL
        cacheConfigurations.put("certificates", defaultCacheConfig
                .entryTtl(Duration.ofHours(4))
                .prefixCacheNameWith("martial:certificates:"));
        
        // Lookup cache - 6 hours TTL (reference data)
        cacheConfigurations.put("lookups", defaultCacheConfig
                .entryTtl(Duration.ofHours(6))
                .prefixCacheNameWith("martial:lookups:"));
        
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultCacheConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
    
    /**
     * Redis template for session management
     */
    @Bean
    public RedisTemplate<String, String> sessionRedisTemplate() {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setHashValueSerializer(stringRedisSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * Redis template for JWT token blacklist
     */
    @Bean
    public RedisTemplate<String, String> jwtBlacklistRedisTemplate() {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * Redis template for rate limiting
     */
    @Bean
    public RedisTemplate<String, Long> rateLimitRedisTemplate() {
        RedisTemplate<String, Long> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setHashValueSerializer(stringRedisSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
}
