package vn.gov.martial.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import vn.gov.martial.security.JwtAuthenticationEntryPoint;
import vn.gov.martial.security.JwtAuthenticationFilter;
import vn.gov.martial.security.UserDetailsServiceImpl;

import java.util.Arrays;
import java.util.List;

/**
 * Security Configuration - Cấu hình bảo mật cho hệ thống
 * Hỗ trợ JWT authentication, role-based authorization và CORS
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {
    
    private final UserDetailsServiceImpl userDetailsService;
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
    
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }
    
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .exceptionHandling(exception -> exception.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers("/auth/**").permitAll()
                .requestMatchers("/public/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                
                // Super Admin endpoints
                .requestMatchers("/admin/super/**").hasRole("SUPER_ADMIN")
                
                // City Admin endpoints
                .requestMatchers("/admin/city/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN")
                
                // Ward Admin endpoints
                .requestMatchers("/admin/ward/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                
                // Organization management
                .requestMatchers("/organizations/create").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/organizations/*/approve").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/organizations/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN", "INSTRUCTOR")
                
                // User management
                .requestMatchers("/users/create").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/users/*/approve").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/users/*/lock").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/users/search").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/users/**").authenticated()
                
                // Student management
                .requestMatchers("/students/create").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN", "INSTRUCTOR")
                .requestMatchers("/students/*/approve").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/students/*/transfer").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN", "INSTRUCTOR")
                .requestMatchers("/students/**").authenticated()
                
                // Exam management
                .requestMatchers("/exams/create").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN", "INSTRUCTOR")
                .requestMatchers("/exams/*/approve").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/exams/**").authenticated()
                
                // Martial Arts management
                .requestMatchers("/martial-arts/create").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/martial-arts/*/approve").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/martial-arts/**").authenticated()
                
                // Attendance management
                .requestMatchers("/attendance/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN", "INSTRUCTOR", "STUDENT")
                
                // Reports - role-based access
                .requestMatchers("/reports/national/**").hasRole("SUPER_ADMIN")
                .requestMatchers("/reports/city/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN")
                .requestMatchers("/reports/ward/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN")
                .requestMatchers("/reports/club/**").hasAnyRole("SUPER_ADMIN", "CITY_ADMIN", "WARD_ADMIN", "INSTRUCTOR")
                .requestMatchers("/reports/**").authenticated()
                
                // Notifications
                .requestMatchers("/notifications/**").authenticated()
                
                // File uploads
                .requestMatchers("/files/upload").authenticated()
                .requestMatchers("/files/download/**").authenticated()
                
                // All other requests require authentication
                .anyRequest().authenticated()
            );
        
        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Allow specific origins in production
        configuration.setAllowedOriginPatterns(List.of(
            "http://localhost:3000",    // React development
            "http://localhost:3001",    // React admin panel
            "http://localhost:8080",    // Spring Boot
            "https://*.martial-arts.gov.vn",  // Production domains
            "https://*.admin.martial-arts.gov.vn"
        ));
        
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"
        ));
        
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers"
        ));
        
        configuration.setExposedHeaders(Arrays.asList(
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials",
            "Authorization",
            "Content-Disposition"
        ));
        
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}
