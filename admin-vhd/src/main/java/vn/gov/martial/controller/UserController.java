package vn.gov.martial.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.gov.martial.dto.request.ChangePasswordRequest;
import vn.gov.martial.dto.request.UserCreateRequest;
import vn.gov.martial.dto.request.UserUpdateRequest;
import vn.gov.martial.dto.response.ApiResponseWrapper;
import vn.gov.martial.dto.response.UserResponse;
import vn.gov.martial.entity.User;
import vn.gov.martial.service.UserService;
import vn.gov.martial.util.SecurityUtils;

import java.util.List;

/**
 * User Controller - REST API cho quản lý người dùng
 * Hỗ trợ CRUD, phân quyền và multi-tenant
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Management", description = "APIs for managing users in the martial arts system")
public class UserController {
    
    private final UserService userService;
    
    @Operation(summary = "Create new user", description = "Create a new user in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "User already exists"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> createUser(
            @Valid @RequestBody UserCreateRequest request) {
        
        log.info("Creating user with username: {}", request.getUsername());
        
        UserResponse response = userService.createUser(request);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseWrapper.success(response, "User created successfully"));
    }
    
    @Operation(summary = "Update user", description = "Update user information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User updated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN') or @securityUtils.isOwner(#id)")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> updateUser(
            @Parameter(description = "User ID") @PathVariable Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        
        log.info("Updating user with ID: {}", id);
        
        UserResponse response = userService.updateUser(id, request);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User updated successfully"));
    }
    
    @Operation(summary = "Get user by ID", description = "Retrieve user information by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN') or @securityUtils.isOwner(#id)")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> getUserById(
            @Parameter(description = "User ID") @PathVariable Long id) {
        
        log.info("Getting user with ID: {}", id);
        
        UserResponse response = userService.getUserById(id);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User retrieved successfully"));
    }
    
    @Operation(summary = "Get user by username", description = "Retrieve user information by username")
    @GetMapping("/username/{username}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> getUserByUsername(
            @Parameter(description = "Username") @PathVariable String username) {
        
        log.info("Getting user with username: {}", username);
        
        UserResponse response = userService.getUserByUsername(username);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User retrieved successfully"));
    }
    
    @Operation(summary = "Search users", description = "Search users with filters and pagination")
    @GetMapping("/search")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<Page<UserResponse>>> searchUsers(
            @Parameter(description = "Search keyword") @RequestParam(required = false) String keyword,
            @Parameter(description = "User role filter") @RequestParam(required = false) User.UserRole role,
            @Parameter(description = "User status filter") @RequestParam(required = false) User.UserStatus status,
            @Parameter(description = "Organization ID filter") @RequestParam(required = false) Long organizationId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Searching users with keyword: {}, role: {}, status: {}, organizationId: {}", 
                keyword, role, status, organizationId);
        
        Page<UserResponse> response = userService.searchUsers(keyword, role, status, organizationId, pageable);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "Users retrieved successfully"));
    }
    
    @Operation(summary = "Get users by organization", description = "Get all users in an organization")
    @GetMapping("/organization/{organizationId}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN') or @securityUtils.hasOrganizationAccess(#organizationId)")
    public ResponseEntity<ApiResponseWrapper<List<UserResponse>>> getUsersByOrganization(
            @Parameter(description = "Organization ID") @PathVariable Long organizationId) {
        
        log.info("Getting users for organization: {}", organizationId);
        
        List<UserResponse> response = userService.getUsersByOrganization(organizationId);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "Users retrieved successfully"));
    }
    
    @Operation(summary = "Get active instructors", description = "Get all active instructors in an organization")
    @GetMapping("/instructors/active")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<List<UserResponse>>> getActiveInstructors(
            @Parameter(description = "Organization ID") @RequestParam Long organizationId) {
        
        log.info("Getting active instructors for organization: {}", organizationId);
        
        List<UserResponse> response = userService.getActiveInstructors(organizationId);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "Active instructors retrieved successfully"));
    }
    
    @Operation(summary = "Approve user", description = "Approve a pending user")
    @PostMapping("/{id}/approve")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> approveUser(
            @Parameter(description = "User ID") @PathVariable Long id) {
        
        log.info("Approving user with ID: {}", id);
        
        Long approverId = SecurityUtils.getCurrentUserId();
        UserResponse response = userService.approveUser(id, approverId);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User approved successfully"));
    }
    
    @Operation(summary = "Reject user", description = "Reject a pending user")
    @PostMapping("/{id}/reject")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> rejectUser(
            @Parameter(description = "User ID") @PathVariable Long id,
            @Parameter(description = "Rejection reason") @RequestParam String reason) {
        
        log.info("Rejecting user with ID: {}", id);
        
        Long rejectorId = SecurityUtils.getCurrentUserId();
        UserResponse response = userService.rejectUser(id, reason, rejectorId);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User rejected successfully"));
    }
    
    @Operation(summary = "Lock user", description = "Lock a user account")
    @PostMapping("/{id}/lock")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> lockUser(
            @Parameter(description = "User ID") @PathVariable Long id,
            @Parameter(description = "Lock reason") @RequestParam String reason) {
        
        log.info("Locking user with ID: {}", id);
        
        Long lockerId = SecurityUtils.getCurrentUserId();
        UserResponse response = userService.lockUser(id, reason, lockerId);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User locked successfully"));
    }
    
    @Operation(summary = "Unlock user", description = "Unlock a user account")
    @PostMapping("/{id}/unlock")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<UserResponse>> unlockUser(
            @Parameter(description = "User ID") @PathVariable Long id) {
        
        log.info("Unlocking user with ID: {}", id);
        
        Long unlockerId = SecurityUtils.getCurrentUserId();
        UserResponse response = userService.unlockUser(id, unlockerId);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(response, "User unlocked successfully"));
    }
    
    @Operation(summary = "Change password", description = "Change user password")
    @PostMapping("/{id}/change-password")
    @PreAuthorize("@securityUtils.isOwner(#id)")
    public ResponseEntity<ApiResponseWrapper<Void>> changePassword(
            @Parameter(description = "User ID") @PathVariable Long id,
            @Valid @RequestBody ChangePasswordRequest request) {
        
        log.info("Changing password for user ID: {}", id);
        
        userService.changePassword(id, request.getOldPassword(), request.getNewPassword());
        
        return ResponseEntity.ok(ApiResponseWrapper.success(null, "Password changed successfully"));
    }
    
    @Operation(summary = "Reset password", description = "Reset user password (admin only)")
    @PostMapping("/{id}/reset-password")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('CITY_ADMIN') or hasRole('WARD_ADMIN')")
    public ResponseEntity<ApiResponseWrapper<String>> resetPassword(
            @Parameter(description = "User ID") @PathVariable Long id) {
        
        log.info("Resetting password for user ID: {}", id);
        
        Long resetById = SecurityUtils.getCurrentUserId();
        String newPassword = userService.resetPassword(id, resetById);
        
        return ResponseEntity.ok(ApiResponseWrapper.success(newPassword, "Password reset successfully"));
    }
}
