package vn.gov.martial.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Exam Entity - Quản lý kỳ thi lên đai và chứng chỉ
 * Hỗ trợ tổ chức thi theo 3 cấp độ
 */
@Entity
@Table(name = "exams")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class Exam {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "exam_code", unique = true, nullable = false, length = 20)
    private String examCode;
    
    @Column(nullable = false, length = 200)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ExamType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ExamLevel level;
    
    // Organization and martial art
    @Column(name = "organization_id", nullable = false)
    private Long organizationId;
    
    @Column(name = "martial_art_id", nullable = false)
    private Long martialArtId;
    
    // Target belt level for promotion exams
    @Enumerated(EnumType.STRING)
    @Column(name = "target_belt_level")
    private Student.BeltLevel targetBeltLevel;
    
    // Exam schedule
    @Column(name = "exam_date", nullable = false)
    private LocalDateTime examDate;
    
    @Column(name = "registration_start_date", nullable = false)
    private LocalDateTime registrationStartDate;
    
    @Column(name = "registration_end_date", nullable = false)
    private LocalDateTime registrationEndDate;
    
    @Column(name = "result_announcement_date")
    private LocalDateTime resultAnnouncementDate;
    
    // Location and logistics
    @Column(name = "venue", nullable = false, length = 200)
    private String venue;
    
    @Column(name = "venue_address", columnDefinition = "TEXT")
    private String venueAddress;
    
    @Column(name = "max_participants")
    private Integer maxParticipants;
    
    @Column(name = "current_participants")
    private Integer currentParticipants = 0;
    
    @Column(name = "exam_fee")
    private Double examFee;
    
    // Exam requirements and criteria
    @Column(name = "requirements", columnDefinition = "TEXT")
    private String requirements;
    
    @Column(name = "evaluation_criteria", columnDefinition = "TEXT")
    private String evaluationCriteria;
    
    @Column(name = "passing_score")
    private Double passingScore;
    
    @Column(name = "theory_weight")
    private Double theoryWeight = 0.3; // 30% theory, 70% practical
    
    @Column(name = "practical_weight")
    private Double practicalWeight = 0.7;
    
    // Status and approval
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ExamStatus status;
    
    @Column(name = "approved_by")
    private Long approvedBy;
    
    @Column(name = "approved_at")
    private LocalDateTime approvedAt;
    
    @Column(name = "rejection_reason", columnDefinition = "TEXT")
    private String rejectionReason;
    
    // Exam board information
    @Column(name = "chief_examiner_id")
    private Long chiefExaminerId;
    
    @Column(name = "exam_board_members", columnDefinition = "TEXT")
    private String examBoardMembers; // JSON array of examiner IDs
    
    // Results summary
    @Column(name = "total_registered")
    private Integer totalRegistered = 0;
    
    @Column(name = "total_attended")
    private Integer totalAttended = 0;
    
    @Column(name = "total_passed")
    private Integer totalPassed = 0;
    
    @Column(name = "total_failed")
    private Integer totalFailed = 0;
    
    @Column(name = "average_score")
    private Double averageScore;
    
    // Audit fields
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", insertable = false, updatable = false)
    private Organization organization;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "martial_art_id", insertable = false, updatable = false)
    private MartialArt martialArt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chief_examiner_id", insertable = false, updatable = false)
    private User chiefExaminer;
    
    @OneToMany(mappedBy = "exam", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<StudentExam> studentExams;
    
    // Enums
    public enum ExamType {
        BELT_PROMOTION("Thi lên đai"),
        SKILL_CERTIFICATION("Chứng chỉ kỹ năng"),
        INSTRUCTOR_CERTIFICATION("Chứng chỉ huấn luyện viên"),
        REFEREE_CERTIFICATION("Chứng chỉ trọng tài"),
        COMPETITION_QUALIFICATION("Thi đấu tuyển chọn"),
        ANNUAL_ASSESSMENT("Đánh giá thường niên");
        
        private final String description;
        
        ExamType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum ExamLevel {
        CLUB_LEVEL("Cấp CLB"),
        WARD_LEVEL("Cấp Phường/Xã"),
        CITY_LEVEL("Cấp Thành Phố"),
        NATIONAL_LEVEL("Cấp Quốc Gia");
        
        private final String description;
        
        ExamLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum ExamStatus {
        DRAFT("Bản nháp"),
        PENDING_APPROVAL("Chờ phê duyệt"),
        APPROVED("Đã phê duyệt"),
        REGISTRATION_OPEN("Mở đăng ký"),
        REGISTRATION_CLOSED("Đóng đăng ký"),
        IN_PROGRESS("Đang thi"),
        COMPLETED("Hoàn thành"),
        CANCELLED("Đã hủy"),
        POSTPONED("Hoãn lại");
        
        private final String description;
        
        ExamStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Helper methods
    public boolean isRegistrationOpen() {
        LocalDateTime now = LocalDateTime.now();
        return status == ExamStatus.REGISTRATION_OPEN &&
               now.isAfter(registrationStartDate) &&
               now.isBefore(registrationEndDate) &&
               (maxParticipants == null || currentParticipants < maxParticipants);
    }
    
    public boolean canRegister() {
        return isRegistrationOpen();
    }
    
    public boolean isCompleted() {
        return status == ExamStatus.COMPLETED;
    }
    
    public boolean hasCapacity() {
        return maxParticipants == null || currentParticipants < maxParticipants;
    }
    
    public void incrementParticipants() {
        this.currentParticipants = (this.currentParticipants == null ? 0 : this.currentParticipants) + 1;
        this.totalRegistered = (this.totalRegistered == null ? 0 : this.totalRegistered) + 1;
    }
    
    public void decrementParticipants() {
        this.currentParticipants = Math.max(0, (this.currentParticipants == null ? 0 : this.currentParticipants) - 1);
    }
    
    public double getPassRate() {
        if (totalAttended == null || totalAttended == 0) {
            return 0.0;
        }
        return (totalPassed == null ? 0.0 : totalPassed) * 100.0 / totalAttended;
    }
    
    public double getAttendanceRate() {
        if (totalRegistered == null || totalRegistered == 0) {
            return 0.0;
        }
        return (totalAttended == null ? 0.0 : totalAttended) * 100.0 / totalRegistered;
    }
    
    public String getDisplayName() {
        return title + " (" + examCode + ")";
    }
}
