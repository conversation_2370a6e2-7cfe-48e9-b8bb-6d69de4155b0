package vn.gov.martial.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * MartialArt Entity - Quản lý các môn võ thuật
 * Hỗ trợ đăng ký môn võ theo cấp độ tổ chức
 */
@Entity
@Table(name = "martial_arts")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class MartialArt {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(name = "english_name", length = 100)
    private String englishName;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MartialArtType type;
    
    @Column(name = "country_of_origin", length = 50)
    private String countryOfOrigin;
    
    @Column(name = "founding_year")
    private Integer foundingYear;
    
    // Organization and approval information
    @Column(name = "organization_id", nullable = false)
    private Long organizationId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "approval_level", nullable = false)
    private ApprovalLevel approvalLevel;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MartialArtStatus status;
    
    @Column(name = "approved_by")
    private Long approvedBy;
    
    @Column(name = "approved_at")
    private LocalDateTime approvedAt;
    
    @Column(name = "rejection_reason", columnDefinition = "TEXT")
    private String rejectionReason;
    
    // Training information
    @Column(name = "min_age")
    private Integer minAge;
    
    @Column(name = "max_age")
    private Integer maxAge;
    
    @Column(name = "training_duration_months")
    private Integer trainingDurationMonths;
    
    @Column(name = "belt_system_enabled")
    private Boolean beltSystemEnabled = true;
    
    @Column(name = "competition_enabled")
    private Boolean competitionEnabled = true;
    
    // Requirements and rules
    @Column(name = "physical_requirements", columnDefinition = "TEXT")
    private String physicalRequirements;
    
    @Column(name = "equipment_required", columnDefinition = "TEXT")
    private String equipmentRequired;
    
    @Column(name = "safety_rules", columnDefinition = "TEXT")
    private String safetyRules;
    
    @Column(name = "training_rules", columnDefinition = "TEXT")
    private String trainingRules;
    
    // Statistics
    @Column(name = "total_students")
    private Integer totalStudents = 0;
    
    @Column(name = "total_instructors")
    private Integer totalInstructors = 0;
    
    @Column(name = "total_organizations")
    private Integer totalOrganizations = 0;
    
    // Audit fields
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", insertable = false, updatable = false)
    private Organization organization;
    
    @OneToMany(mappedBy = "martialArt", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Student> students;
    
    @OneToMany(mappedBy = "martialArt", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Exam> exams;
    
    // Enums
    public enum MartialArtType {
        KARATE("Karate"),
        TAEKWONDO("Taekwondo"),
        VOVINAM("Vovinam"),
        JUDO("Judo"),
        KICKBOXING("Kickboxing"),
        MUAY_THAI("Muay Thai"),
        BOXING("Boxing"),
        WRESTLING("Wrestling"),
        AIKIDO("Aikido"),
        KUNG_FU("Kung Fu"),
        CAPOEIRA("Capoeira"),
        KENDO("Kendo"),
        TRADITIONAL_VIETNAMESE("Võ Cổ Truyền Việt Nam"),
        OTHER("Khác");
        
        private final String description;
        
        MartialArtType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum ApprovalLevel {
        CLUB_LEVEL("Cấp CLB"),
        WARD_LEVEL("Cấp Phường/Xã"),
        CITY_LEVEL("Cấp Thành Phố"),
        NATIONAL_LEVEL("Cấp Quốc Gia");
        
        private final String description;
        
        ApprovalLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum MartialArtStatus {
        PENDING_APPROVAL("Chờ phê duyệt"),
        ACTIVE("Hoạt động"),
        SUSPENDED("Tạm ngưng"),
        INACTIVE("Không hoạt động"),
        REJECTED("Bị từ chối");
        
        private final String description;
        
        MartialArtStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Helper methods
    public boolean isActive() {
        return status == MartialArtStatus.ACTIVE;
    }
    
    public boolean isApproved() {
        return status == MartialArtStatus.ACTIVE && approvedAt != null;
    }
    
    public boolean hasBeltSystem() {
        return beltSystemEnabled != null && beltSystemEnabled;
    }
    
    public boolean allowsCompetition() {
        return competitionEnabled != null && competitionEnabled;
    }
    
    public boolean isAgeAppropriate(int age) {
        boolean minAgeOk = minAge == null || age >= minAge;
        boolean maxAgeOk = maxAge == null || age <= maxAge;
        return minAgeOk && maxAgeOk;
    }
    
    public void incrementStudentCount() {
        this.totalStudents = (this.totalStudents == null ? 0 : this.totalStudents) + 1;
    }
    
    public void decrementStudentCount() {
        this.totalStudents = Math.max(0, (this.totalStudents == null ? 0 : this.totalStudents) - 1);
    }
    
    public void incrementInstructorCount() {
        this.totalInstructors = (this.totalInstructors == null ? 0 : this.totalInstructors) + 1;
    }
    
    public void decrementInstructorCount() {
        this.totalInstructors = Math.max(0, (this.totalInstructors == null ? 0 : this.totalInstructors) - 1);
    }
    
    public String getDisplayName() {
        return name + (englishName != null ? " (" + englishName + ")" : "");
    }
}
