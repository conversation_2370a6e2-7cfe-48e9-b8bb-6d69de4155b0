package vn.gov.martial.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Organization Entity - Quản lý đơn vị tổ chức 3 cấp
 * Hỗ trợ hierarchy: Quốc gia -> Thành phố -> Phường/Xã -> CLB
 */
@Entity
@Table(name = "organizations")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class Organization {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(length = 20)
    private String code;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrganizationType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrganizationLevel level;
    
    @Column(columnDefinition = "TEXT")
    private String address;
    
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
    
    @Column(length = 100)
    private String email;
    
    @Column(length = 100)
    private String website;
    
    // Hierarchy support
    @Column(name = "parent_id")
    private Long parentId;
    
    @Column(name = "hierarchy_path", length = 500)
    private String hierarchyPath; // e.g., "/1/2/3/" for easy querying
    
    @Column(name = "hierarchy_level")
    private Integer hierarchyLevel; // 0=National, 1=City, 2=Ward, 3=Club
    
    // Representative information
    @Column(name = "representative_name", length = 100)
    private String representativeName;
    
    @Column(name = "representative_phone", length = 20)
    private String representativePhone;
    
    @Column(name = "representative_email", length = 100)
    private String representativeEmail;
    
    // Status and approval
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrganizationStatus status;
    
    @Column(name = "approved_by")
    private Long approvedBy;
    
    @Column(name = "approved_at")
    private LocalDateTime approvedAt;
    
    @Column(name = "rejection_reason", columnDefinition = "TEXT")
    private String rejectionReason;
    
    // License and legal information
    @Column(name = "license_number", length = 50)
    private String licenseNumber;
    
    @Column(name = "license_issued_date")
    private LocalDateTime licenseIssuedDate;
    
    @Column(name = "license_expiry_date")
    private LocalDateTime licenseExpiryDate;
    
    @Column(name = "tax_code", length = 20)
    private String taxCode;
    
    // Operational information
    @Column(name = "established_date")
    private LocalDateTime establishedDate;
    
    @Column(name = "student_capacity")
    private Integer studentCapacity;
    
    @Column(name = "current_student_count")
    private Integer currentStudentCount = 0;
    
    @Column(name = "instructor_count")
    private Integer instructorCount = 0;
    
    // Audit fields
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Organization parent;
    
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Organization> children;
    
    @OneToMany(mappedBy = "organization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<User> users;
    
    @OneToMany(mappedBy = "organization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Student> students;
    
    @OneToMany(mappedBy = "organization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<MartialArt> martialArts;
    
    // Enums
    public enum OrganizationType {
        NATIONAL_BUREAU("Tổng Cục"),
        CITY_DEPARTMENT("Sở"),
        WARD_OFFICE("Phòng"),
        MARTIAL_ARTS_CLUB("Câu Lạc Bộ"),
        MARTIAL_ARTS_SCHOOL("Trường Võ"),
        COMMUNITY_CENTER("Trung Tâm Cộng Đồng"),
        SCHOOL_TEAM("Đội Tuyển Trường Học");
        
        private final String description;
        
        OrganizationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum OrganizationLevel {
        NATIONAL("Cấp Quốc Gia"),
        CITY("Cấp Thành Phố"),
        WARD("Cấp Phường/Xã"),
        CLUB("Câu Lạc Bộ");
        
        private final String description;
        
        OrganizationLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum OrganizationStatus {
        PENDING_APPROVAL("Chờ phê duyệt"),
        ACTIVE("Hoạt động"),
        SUSPENDED("Tạm ngưng"),
        INACTIVE("Không hoạt động"),
        REJECTED("Bị từ chối");
        
        private final String description;
        
        OrganizationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Helper methods
    public boolean isActive() {
        return status == OrganizationStatus.ACTIVE;
    }
    
    public boolean isClub() {
        return level == OrganizationLevel.CLUB;
    }
    
    public boolean canAcceptStudents() {
        return isActive() && (studentCapacity == null || currentStudentCount < studentCapacity);
    }
    
    public void incrementStudentCount() {
        this.currentStudentCount = (this.currentStudentCount == null ? 0 : this.currentStudentCount) + 1;
    }
    
    public void decrementStudentCount() {
        this.currentStudentCount = Math.max(0, (this.currentStudentCount == null ? 0 : this.currentStudentCount) - 1);
    }
    
    public String getFullHierarchyName() {
        if (parent != null) {
            return parent.getFullHierarchyName() + " > " + name;
        }
        return name;
    }
}
