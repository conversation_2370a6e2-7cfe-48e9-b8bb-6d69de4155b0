package vn.gov.martial.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * Student Entity - Quản lý học viên võ thuật
 * <PERSON>o gồm thông tin cá nhân, l<PERSON><PERSON> sử học tập, thi cử và chuyển đơn vị
 */
@Entity
@Table(name = "students")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class Student {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "student_code", unique = true, nullable = false, length = 20)
    private String studentCode; // Mã học viên duy nhất
    
    // Personal Information
    @Column(name = "full_name", nullable = false, length = 100)
    private String fullName;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    @Enumerated(EnumType.STRING)
    private Gender gender;
    
    @Column(columnDefinition = "TEXT")
    private String address;
    
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
    
    @Column(length = 100)
    private String email;
    
    @Column(length = 20)
    private String cccd;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    // Health and Emergency Information
    @Column(name = "health_status", columnDefinition = "TEXT")
    private String healthStatus;
    
    @Column(name = "emergency_contact_name", length = 100)
    private String emergencyContactName;
    
    @Column(name = "emergency_contact_phone", length = 20)
    private String emergencyContactPhone;
    
    @Column(name = "emergency_contact_relationship", length = 50)
    private String emergencyContactRelationship;
    
    // Academic Information
    @Column(name = "organization_id", nullable = false)
    private Long organizationId;
    
    @Column(name = "instructor_id")
    private Long instructorId;
    
    @Column(name = "martial_art_id", nullable = false)
    private Long martialArtId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "current_belt_level")
    private BeltLevel currentBeltLevel;
    
    @Column(name = "enrollment_date", nullable = false)
    private LocalDate enrollmentDate;
    
    @Column(name = "graduation_date")
    private LocalDate graduationDate;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StudentStatus status;
    
    // Progress Tracking
    @Column(name = "total_training_hours")
    private Integer totalTrainingHours = 0;
    
    @Column(name = "attendance_rate")
    private Double attendanceRate = 0.0;
    
    @Column(name = "exam_count")
    private Integer examCount = 0;
    
    @Column(name = "certificate_count")
    private Integer certificateCount = 0;
    
    @Column(name = "last_exam_date")
    private LocalDate lastExamDate;
    
    @Column(name = "next_exam_eligible_date")
    private LocalDate nextExamEligibleDate;
    
    // Transfer Information
    @Column(name = "transfer_count")
    private Integer transferCount = 0;
    
    @Column(name = "last_transfer_date")
    private LocalDate lastTransferDate;
    
    @Column(name = "original_organization_id")
    private Long originalOrganizationId;
    
    // Parent/Guardian Information (for minors)
    @Column(name = "parent_name", length = 100)
    private String parentName;
    
    @Column(name = "parent_phone", length = 20)
    private String parentPhone;
    
    @Column(name = "parent_email", length = 100)
    private String parentEmail;
    
    @Column(name = "parent_user_id")
    private Long parentUserId;
    
    // Audit fields
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", insertable = false, updatable = false)
    private Organization organization;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "instructor_id", insertable = false, updatable = false)
    private User instructor;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "martial_art_id", insertable = false, updatable = false)
    private MartialArt martialArt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_user_id", insertable = false, updatable = false)
    private User parentUser;
    
    @OneToMany(mappedBy = "student", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<StudentExam> exams;
    
    @OneToMany(mappedBy = "student", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<StudentCertificate> certificates;
    
    @OneToMany(mappedBy = "student", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<StudentTransfer> transfers;
    
    @OneToMany(mappedBy = "student", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Attendance> attendances;
    
    // Enums
    public enum Gender {
        MALE("Nam"),
        FEMALE("Nữ"),
        OTHER("Khác");
        
        private final String description;
        
        Gender(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum BeltLevel {
        WHITE_10("Đai Trắng 10 Kyu"),
        WHITE_9("Đai Trắng 9 Kyu"),
        WHITE_8("Đai Trắng 8 Kyu"),
        WHITE_7("Đai Trắng 7 Kyu"),
        WHITE_6("Đai Trắng 6 Kyu"),
        WHITE_5("Đai Trắng 5 Kyu"),
        WHITE_4("Đai Trắng 4 Kyu"),
        WHITE_3("Đai Trắng 3 Kyu"),
        WHITE_2("Đai Trắng 2 Kyu"),
        WHITE_1("Đai Trắng 1 Kyu"),
        YELLOW("Đai Vàng"),
        ORANGE("Đai Cam"),
        GREEN("Đai Xanh Lá"),
        BLUE("Đai Xanh Dương"),
        BROWN_3("Đai Nâu 3 Dan"),
        BROWN_2("Đai Nâu 2 Dan"),
        BROWN_1("Đai Nâu 1 Dan"),
        BLACK_1("Đai Đen 1 Dan"),
        BLACK_2("Đai Đen 2 Dan"),
        BLACK_3("Đai Đen 3 Dan"),
        BLACK_4("Đai Đen 4 Dan"),
        BLACK_5("Đai Đen 5 Dan"),
        BLACK_6("Đai Đen 6 Dan"),
        BLACK_7("Đai Đen 7 Dan"),
        BLACK_8("Đai Đen 8 Dan"),
        BLACK_9("Đai Đen 9 Dan"),
        BLACK_10("Đai Đen 10 Dan");
        
        private final String description;
        
        BeltLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
        
        public boolean isBlackBelt() {
            return name().startsWith("BLACK_");
        }
        
        public boolean isBrownBelt() {
            return name().startsWith("BROWN_");
        }
        
        public boolean isColoredBelt() {
            return this == YELLOW || this == ORANGE || this == GREEN || this == BLUE;
        }
        
        public boolean isWhiteBelt() {
            return name().startsWith("WHITE_");
        }
    }
    
    public enum StudentStatus {
        ACTIVE("Đang học"),
        GRADUATED("Đã tốt nghiệp"),
        SUSPENDED("Tạm ngưng"),
        TRANSFERRED("Đã chuyển đơn vị"),
        DROPPED_OUT("Đã nghỉ học"),
        PENDING_TRANSFER("Chờ chuyển đơn vị");
        
        private final String description;
        
        StudentStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Helper methods
    public boolean isActive() {
        return status == StudentStatus.ACTIVE;
    }
    
    public boolean isMinor() {
        return dateOfBirth != null && dateOfBirth.isAfter(LocalDate.now().minusYears(18));
    }
    
    public int getAge() {
        return dateOfBirth != null ? LocalDate.now().getYear() - dateOfBirth.getYear() : 0;
    }
    
    public boolean canTakeExam() {
        return isActive() && (nextExamEligibleDate == null || !nextExamEligibleDate.isAfter(LocalDate.now()));
    }
    
    public void incrementExamCount() {
        this.examCount = (this.examCount == null ? 0 : this.examCount) + 1;
    }
    
    public void incrementCertificateCount() {
        this.certificateCount = (this.certificateCount == null ? 0 : this.certificateCount) + 1;
    }
    
    public void incrementTransferCount() {
        this.transferCount = (this.transferCount == null ? 0 : this.transferCount) + 1;
    }

    public String getDisplayName() {
        return fullName + " (" + studentCode + ")";
    }

    public boolean hasParent() {
        return parentUserId != null;
    }
}
