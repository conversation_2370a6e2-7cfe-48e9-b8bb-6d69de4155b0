package vn.gov.martial.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * User Entity - Quản lý người dùng hệ thống
 * Hỗ trợ multi-tenant với phân quyền theo cấp độ
 */
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false, length = 50)
    private String username;
    
    @Column(nullable = false, length = 100)
    private String email;
    
    @Column(nullable = false)
    private String password;
    
    @Column(name = "full_name", nullable = false, length = 100)
    private String fullName;
    
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
    
    @Column(length = 20)
    private String cccd;
    
    @Column(columnDefinition = "TEXT")
    private String address;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole role;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserStatus status;
    
    // Multi-tenant support
    @Column(name = "organization_id")
    private Long organizationId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "organization_level")
    private OrganizationLevel organizationLevel;
    
    // Security fields
    @Column(name = "last_login")
    private LocalDateTime lastLogin;
    
    @Column(name = "failed_login_attempts")
    private Integer failedLoginAttempts = 0;
    
    @Column(name = "account_locked_until")
    private LocalDateTime accountLockedUntil;
    
    @Column(name = "password_changed_at")
    private LocalDateTime passwordChangedAt;
    
    @Column(name = "email_verified")
    private Boolean emailVerified = false;
    
    @Column(name = "phone_verified")
    private Boolean phoneVerified = false;
    
    // Audit fields
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", insertable = false, updatable = false)
    private Organization organization;
    
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<UserPermission> permissions;
    
    @OneToMany(mappedBy = "instructor", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Student> students;
    
    // Enums
    public enum UserRole {
        SUPER_ADMIN("Super Admin - Cấp Quốc Gia"),
        CITY_ADMIN("City Admin - Cấp Thành Phố"),
        WARD_ADMIN("Ward Admin - Cấp Phường/Xã"),
        INSTRUCTOR("Huấn Luyện Viên"),
        STUDENT("Học Viên"),
        PARENT("Phụ Huynh");
        
        private final String description;
        
        UserRole(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum UserStatus {
        ACTIVE("Hoạt động"),
        INACTIVE("Không hoạt động"),
        SUSPENDED("Tạm ngưng"),
        PENDING_APPROVAL("Chờ phê duyệt"),
        LOCKED("Bị khóa");
        
        private final String description;
        
        UserStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum OrganizationLevel {
        NATIONAL("Cấp Quốc Gia"),
        CITY("Cấp Thành Phố"),
        WARD("Cấp Phường/Xã"),
        CLUB("Câu Lạc Bộ");
        
        private final String description;
        
        OrganizationLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Helper methods
    public boolean isAccountNonLocked() {
        return accountLockedUntil == null || accountLockedUntil.isBefore(LocalDateTime.now());
    }
    
    public boolean isActive() {
        return status == UserStatus.ACTIVE;
    }
    
    public boolean hasRole(UserRole role) {
        return this.role == role;
    }
    
    public boolean isInstructor() {
        return role == UserRole.INSTRUCTOR;
    }
    
    public boolean isStudent() {
        return role == UserRole.STUDENT;
    }
    
    public boolean isAdmin() {
        return role == UserRole.SUPER_ADMIN || 
               role == UserRole.CITY_ADMIN || 
               role == UserRole.WARD_ADMIN;
    }
}
