package vn.gov.martial.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.gov.martial.entity.Student;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Student Repository - Quản lý truy vấn học viên
 * Hỗ trợ tìm kiếm, thống kê và báo cáo học viên
 */
@Repository
public interface StudentRepository extends JpaRepository<Student, Long> {
    
    // Basic queries
    Optional<Student> findByStudentCode(String studentCode);
    
    boolean existsByStudentCode(String studentCode);
    
    boolean existsByCccd(String cccd);
    
    // Organization-based queries
    List<Student> findByOrganizationId(Long organizationId);
    
    List<Student> findByOrganizationIdAndStatus(Long organizationId, Student.StudentStatus status);
    
    @Query("SELECT s FROM Student s WHERE s.organizationId = :organizationId AND s.status = 'ACTIVE'")
    List<Student> findActiveStudentsByOrganization(@Param("organizationId") Long organizationId);
    
    // Instructor-based queries
    List<Student> findByInstructorId(Long instructorId);
    
    List<Student> findByInstructorIdAndStatus(Long instructorId, Student.StudentStatus status);
    
    @Query("SELECT s FROM Student s WHERE s.instructorId = :instructorId AND s.status = 'ACTIVE'")
    List<Student> findActiveStudentsByInstructor(@Param("instructorId") Long instructorId);
    
    // Martial art queries
    List<Student> findByMartialArtId(Long martialArtId);
    
    List<Student> findByMartialArtIdAndStatus(Long martialArtId, Student.StudentStatus status);
    
    @Query("SELECT s FROM Student s WHERE s.martialArtId = :martialArtId AND s.organizationId = :organizationId")
    List<Student> findByMartialArtAndOrganization(@Param("martialArtId") Long martialArtId,
                                                 @Param("organizationId") Long organizationId);
    
    // Belt level queries
    List<Student> findByCurrentBeltLevel(Student.BeltLevel beltLevel);
    
    @Query("SELECT s FROM Student s WHERE s.currentBeltLevel = :beltLevel AND s.organizationId = :organizationId")
    List<Student> findByBeltLevelAndOrganization(@Param("beltLevel") Student.BeltLevel beltLevel,
                                                @Param("organizationId") Long organizationId);
    
    @Query("SELECT s FROM Student s WHERE s.currentBeltLevel IN :beltLevels AND s.status = 'ACTIVE'")
    List<Student> findByBeltLevelsAndActive(@Param("beltLevels") List<Student.BeltLevel> beltLevels);
    
    // Age-based queries
    @Query("SELECT s FROM Student s WHERE s.dateOfBirth BETWEEN :startDate AND :endDate")
    List<Student> findByAgeRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    @Query("SELECT s FROM Student s WHERE s.dateOfBirth > :cutoffDate") // Minors
    List<Student> findMinorStudents(@Param("cutoffDate") LocalDate cutoffDate);
    
    @Query("SELECT s FROM Student s WHERE s.dateOfBirth <= :cutoffDate") // Adults
    List<Student> findAdultStudents(@Param("cutoffDate") LocalDate cutoffDate);
    
    // Enrollment date queries
    @Query("SELECT s FROM Student s WHERE s.enrollmentDate BETWEEN :startDate AND :endDate")
    List<Student> findByEnrollmentDateRange(@Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate);
    
    @Query("SELECT s FROM Student s WHERE s.enrollmentDate >= :date ORDER BY s.enrollmentDate DESC")
    List<Student> findRecentlyEnrolledStudents(@Param("date") LocalDate date);
    
    // Exam eligibility queries
    @Query("SELECT s FROM Student s WHERE s.nextExamEligibleDate <= :date AND s.status = 'ACTIVE'")
    List<Student> findEligibleForExam(@Param("date") LocalDate date);
    
    @Query("SELECT s FROM Student s WHERE s.lastExamDate IS NULL AND s.status = 'ACTIVE' AND " +
           "s.enrollmentDate <= :cutoffDate")
    List<Student> findNeverTakenExam(@Param("cutoffDate") LocalDate cutoffDate);
    
    // Transfer queries
    @Query("SELECT s FROM Student s WHERE s.transferCount > 0 ORDER BY s.transferCount DESC")
    List<Student> findStudentsWithTransfers();
    
    @Query("SELECT s FROM Student s WHERE s.status = 'PENDING_TRANSFER'")
    List<Student> findPendingTransferStudents();
    
    @Query("SELECT s FROM Student s WHERE s.originalOrganizationId != s.organizationId")
    List<Student> findTransferredStudents();
    
    // Parent queries
    @Query("SELECT s FROM Student s WHERE s.parentUserId = :parentUserId")
    List<Student> findByParentUserId(@Param("parentUserId") Long parentUserId);
    
    @Query("SELECT s FROM Student s WHERE s.parentUserId IS NOT NULL")
    List<Student> findStudentsWithParents();
    
    @Query("SELECT s FROM Student s WHERE s.parentUserId IS NULL AND s.dateOfBirth > :cutoffDate")
    List<Student> findMinorsWithoutParents(@Param("cutoffDate") LocalDate cutoffDate);
    
    // Search queries
    @Query("SELECT s FROM Student s WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(s.fullName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(s.studentCode) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(s.email) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(s.phoneNumber) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:status IS NULL OR s.status = :status) AND " +
           "(:organizationId IS NULL OR s.organizationId = :organizationId) AND " +
           "(:instructorId IS NULL OR s.instructorId = :instructorId) AND " +
           "(:martialArtId IS NULL OR s.martialArtId = :martialArtId) AND " +
           "(:beltLevel IS NULL OR s.currentBeltLevel = :beltLevel)")
    Page<Student> searchStudents(@Param("keyword") String keyword,
                                @Param("status") Student.StudentStatus status,
                                @Param("organizationId") Long organizationId,
                                @Param("instructorId") Long instructorId,
                                @Param("martialArtId") Long martialArtId,
                                @Param("beltLevel") Student.BeltLevel beltLevel,
                                Pageable pageable);
    
    // Statistics queries
    @Query("SELECT COUNT(s) FROM Student s WHERE s.organizationId = :organizationId AND s.status = 'ACTIVE'")
    Long countActiveStudentsByOrganization(@Param("organizationId") Long organizationId);
    
    @Query("SELECT COUNT(s) FROM Student s WHERE s.instructorId = :instructorId AND s.status = 'ACTIVE'")
    Long countActiveStudentsByInstructor(@Param("instructorId") Long instructorId);
    
    @Query("SELECT COUNT(s) FROM Student s WHERE s.martialArtId = :martialArtId AND s.status = 'ACTIVE'")
    Long countActiveStudentsByMartialArt(@Param("martialArtId") Long martialArtId);
    
    @Query("SELECT s.status, COUNT(s) FROM Student s GROUP BY s.status")
    List<Object[]> getStudentStatusStatistics();
    
    @Query("SELECT s.currentBeltLevel, COUNT(s) FROM Student s WHERE s.status = 'ACTIVE' GROUP BY s.currentBeltLevel")
    List<Object[]> getBeltLevelStatistics();
    
    @Query("SELECT s.gender, COUNT(s) FROM Student s WHERE s.status = 'ACTIVE' GROUP BY s.gender")
    List<Object[]> getGenderStatistics();
    
    // Performance queries
    @Query("SELECT s FROM Student s WHERE s.attendanceRate >= :minRate AND s.status = 'ACTIVE' ORDER BY s.attendanceRate DESC")
    List<Student> findHighPerformingStudents(@Param("minRate") Double minRate);
    
    @Query("SELECT s FROM Student s WHERE s.attendanceRate < :maxRate AND s.status = 'ACTIVE' ORDER BY s.attendanceRate ASC")
    List<Student> findLowPerformingStudents(@Param("maxRate") Double maxRate);
    
    @Query("SELECT s FROM Student s WHERE s.examCount >= :minExams AND s.status = 'ACTIVE' ORDER BY s.examCount DESC")
    List<Student> findActiveExamTakers(@Param("minExams") Integer minExams);
    
    // Hierarchy queries
    @Query("SELECT s FROM Student s JOIN s.organization o WHERE o.hierarchyPath LIKE :hierarchyPath%")
    List<Student> findByHierarchyPath(@Param("hierarchyPath") String hierarchyPath);
    
    @Query("SELECT s FROM Student s WHERE s.organizationId IN " +
           "(SELECT o.id FROM Organization o WHERE o.parentId = :parentId)")
    List<Student> findByParentOrganization(@Param("parentId") Long parentId);
    
    // Bulk operations support
    @Query("SELECT s.id FROM Student s WHERE s.organizationId = :organizationId AND s.status = :status")
    List<Long> findStudentIdsByOrganizationAndStatus(@Param("organizationId") Long organizationId,
                                                    @Param("status") Student.StudentStatus status);
    
    // Custom business logic queries
    @Query("SELECT s FROM Student s WHERE s.status = 'ACTIVE' AND " +
           "s.totalTrainingHours >= :minHours AND " +
           "s.attendanceRate >= :minAttendanceRate AND " +
           "(s.nextExamEligibleDate IS NULL OR s.nextExamEligibleDate <= :currentDate)")
    List<Student> findQualifiedForPromotion(@Param("minHours") Integer minHours,
                                           @Param("minAttendanceRate") Double minAttendanceRate,
                                           @Param("currentDate") LocalDate currentDate);
    
    @Query("SELECT s FROM Student s WHERE s.status = 'ACTIVE' AND s.lastExamDate < :cutoffDate")
    List<Student> findStudentsNeedingExam(@Param("cutoffDate") LocalDate cutoffDate);
}
