package vn.gov.martial.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.gov.martial.entity.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * User Repository - Quản lý truy vấn người dùng
 * Hỗ trợ multi-tenant và phân quyền theo cấp độ
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    // Basic queries
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    Optional<User> findByUsernameOrEmail(String username, String email);
    
    boolean existsByUsername(String username);
    
    boolean existsByEmail(String email);
    
    boolean existsByCccd(String cccd);
    
    // Role-based queries
    List<User> findByRole(User.UserRole role);
    
    List<User> findByRoleAndStatus(User.UserRole role, User.UserStatus status);
    
    @Query("SELECT u FROM User u WHERE u.role = :role AND u.organizationId = :organizationId")
    List<User> findByRoleAndOrganizationId(@Param("role") User.UserRole role, 
                                          @Param("organizationId") Long organizationId);
    
    // Organization-based queries
    List<User> findByOrganizationId(Long organizationId);
    
    List<User> findByOrganizationIdAndStatus(Long organizationId, User.UserStatus status);
    
    @Query("SELECT u FROM User u WHERE u.organizationLevel = :level AND u.status = :status")
    List<User> findByOrganizationLevelAndStatus(@Param("level") User.OrganizationLevel level,
                                               @Param("status") User.UserStatus status);
    
    // Hierarchy queries
    @Query("SELECT u FROM User u JOIN u.organization o WHERE o.hierarchyPath LIKE :hierarchyPath%")
    List<User> findByHierarchyPath(@Param("hierarchyPath") String hierarchyPath);
    
    @Query("SELECT u FROM User u WHERE u.role = :role AND u.organizationId IN " +
           "(SELECT o.id FROM Organization o WHERE o.parentId = :parentId)")
    List<User> findByRoleAndParentOrganization(@Param("role") User.UserRole role,
                                              @Param("parentId") Long parentId);
    
    // Instructor queries
    @Query("SELECT u FROM User u WHERE u.role = 'INSTRUCTOR' AND u.organizationId = :organizationId AND u.status = 'ACTIVE'")
    List<User> findActiveInstructorsByOrganization(@Param("organizationId") Long organizationId);
    
    @Query("SELECT u FROM User u WHERE u.role = 'INSTRUCTOR' AND u.id IN " +
           "(SELECT s.instructorId FROM Student s WHERE s.organizationId = :organizationId)")
    List<User> findInstructorsWithStudentsInOrganization(@Param("organizationId") Long organizationId);
    
    // Admin queries
    @Query("SELECT u FROM User u WHERE u.role IN ('SUPER_ADMIN', 'CITY_ADMIN', 'WARD_ADMIN') AND u.status = 'ACTIVE'")
    List<User> findAllActiveAdmins();
    
    @Query("SELECT u FROM User u WHERE u.role = 'CITY_ADMIN' AND u.organizationLevel = 'CITY'")
    List<User> findAllCityAdmins();
    
    @Query("SELECT u FROM User u WHERE u.role = 'WARD_ADMIN' AND u.organizationLevel = 'WARD'")
    List<User> findAllWardAdmins();
    
    // Student and parent queries
    @Query("SELECT u FROM User u WHERE u.role = 'STUDENT' AND u.organizationId = :organizationId")
    List<User> findStudentsByOrganization(@Param("organizationId") Long organizationId);
    
    @Query("SELECT u FROM User u WHERE u.role = 'PARENT' AND u.id IN " +
           "(SELECT s.parentUserId FROM Student s WHERE s.organizationId = :organizationId)")
    List<User> findParentsByOrganization(@Param("organizationId") Long organizationId);
    
    // Security queries
    @Query("SELECT u FROM User u WHERE u.status = 'LOCKED' AND u.accountLockedUntil < :now")
    List<User> findUsersToUnlock(@Param("now") LocalDateTime now);
    
    @Query("SELECT u FROM User u WHERE u.lastLogin < :cutoffDate AND u.status = 'ACTIVE'")
    List<User> findInactiveUsers(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    @Query("SELECT u FROM User u WHERE u.passwordChangedAt < :cutoffDate AND u.status = 'ACTIVE'")
    List<User> findUsersWithExpiredPasswords(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    // Search queries
    @Query("SELECT u FROM User u WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(u.fullName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:role IS NULL OR u.role = :role) AND " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:organizationId IS NULL OR u.organizationId = :organizationId)")
    Page<User> searchUsers(@Param("keyword") String keyword,
                          @Param("role") User.UserRole role,
                          @Param("status") User.UserStatus status,
                          @Param("organizationId") Long organizationId,
                          Pageable pageable);
    
    // Statistics queries
    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role AND u.status = 'ACTIVE'")
    Long countActiveUsersByRole(@Param("role") User.UserRole role);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.organizationId = :organizationId AND u.status = 'ACTIVE'")
    Long countActiveUsersByOrganization(@Param("organizationId") Long organizationId);
    
    @Query("SELECT u.role, COUNT(u) FROM User u WHERE u.status = 'ACTIVE' GROUP BY u.role")
    List<Object[]> getUserRoleStatistics();
    
    @Query("SELECT u.organizationLevel, COUNT(u) FROM User u WHERE u.status = 'ACTIVE' GROUP BY u.organizationLevel")
    List<Object[]> getUserOrganizationLevelStatistics();
    
    // Recent activity queries
    @Query("SELECT u FROM User u WHERE u.lastLogin >= :since ORDER BY u.lastLogin DESC")
    List<User> findRecentlyActiveUsers(@Param("since") LocalDateTime since);
    
    @Query("SELECT u FROM User u WHERE u.createdAt >= :since ORDER BY u.createdAt DESC")
    List<User> findRecentlyCreatedUsers(@Param("since") LocalDateTime since);
    
    // Verification queries
    @Query("SELECT u FROM User u WHERE u.emailVerified = false AND u.status = 'ACTIVE'")
    List<User> findUsersWithUnverifiedEmail();
    
    @Query("SELECT u FROM User u WHERE u.phoneVerified = false AND u.status = 'ACTIVE'")
    List<User> findUsersWithUnverifiedPhone();
    
    // Custom queries for specific business logic
    @Query("SELECT u FROM User u WHERE u.role = 'INSTRUCTOR' AND u.organizationId = :organizationId AND " +
           "u.id NOT IN (SELECT s.instructorId FROM Student s WHERE s.instructorId IS NOT NULL)")
    List<User> findAvailableInstructors(@Param("organizationId") Long organizationId);
    
    @Query("SELECT DISTINCT u FROM User u JOIN u.students s WHERE s.status = 'ACTIVE' AND u.organizationId = :organizationId")
    List<User> findInstructorsWithActiveStudents(@Param("organizationId") Long organizationId);
    
    // Bulk operations support
    @Query("SELECT u.id FROM User u WHERE u.organizationId = :organizationId AND u.status = :status")
    List<Long> findUserIdsByOrganizationAndStatus(@Param("organizationId") Long organizationId,
                                                 @Param("status") User.UserStatus status);
}
