package vn.gov.martial.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.gov.martial.dto.request.UserCreateRequest;
import vn.gov.martial.dto.request.UserUpdateRequest;
import vn.gov.martial.dto.response.UserResponse;
import vn.gov.martial.entity.User;
import vn.gov.martial.exception.BusinessException;
import vn.gov.martial.exception.ResourceNotFoundException;
import vn.gov.martial.mapper.UserMapper;
import vn.gov.martial.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * User Service - Quản lý người dùng hệ thống
 * Hỗ trợ CRUD, authentication, authorization và multi-tenant
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserMapper userMapper;
    private final NotificationService notificationService;
    private final AuditService auditService;
    
    /**
     * Tạo người dùng mới
     */
    public UserResponse createUser(UserCreateRequest request) {
        log.info("Creating new user with username: {}", request.getUsername());
        
        // Validate unique constraints
        validateUniqueFields(request.getUsername(), request.getEmail(), request.getCccd(), null);
        
        // Validate organization access
        validateOrganizationAccess(request.getOrganizationId(), request.getRole());
        
        User user = User.builder()
                .username(request.getUsername())
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .fullName(request.getFullName())
                .phoneNumber(request.getPhoneNumber())
                .cccd(request.getCccd())
                .address(request.getAddress())
                .notes(request.getNotes())
                .role(request.getRole())
                .status(User.UserStatus.PENDING_APPROVAL)
                .organizationId(request.getOrganizationId())
                .organizationLevel(determineOrganizationLevel(request.getRole()))
                .passwordChangedAt(LocalDateTime.now())
                .emailVerified(false)
                .phoneVerified(false)
                .failedLoginAttempts(0)
                .build();
        
        User savedUser = userRepository.save(user);
        
        // Send notification
        notificationService.sendUserCreatedNotification(savedUser);
        
        // Audit log
        auditService.logUserCreated(savedUser);
        
        log.info("User created successfully with ID: {}", savedUser.getId());
        return userMapper.toResponse(savedUser);
    }
    
    /**
     * Cập nhật thông tin người dùng
     */
    @CacheEvict(value = "users", key = "#id")
    public UserResponse updateUser(Long id, UserUpdateRequest request) {
        log.info("Updating user with ID: {}", id);
        
        User user = findUserById(id);
        
        // Validate unique constraints (excluding current user)
        validateUniqueFields(request.getUsername(), request.getEmail(), request.getCccd(), id);
        
        // Update fields
        if (request.getUsername() != null) {
            user.setUsername(request.getUsername());
        }
        if (request.getEmail() != null) {
            user.setEmail(request.getEmail());
            user.setEmailVerified(false); // Reset verification if email changed
        }
        if (request.getFullName() != null) {
            user.setFullName(request.getFullName());
        }
        if (request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
            user.setPhoneVerified(false); // Reset verification if phone changed
        }
        if (request.getCccd() != null) {
            user.setCccd(request.getCccd());
        }
        if (request.getAddress() != null) {
            user.setAddress(request.getAddress());
        }
        if (request.getNotes() != null) {
            user.setNotes(request.getNotes());
        }
        
        User updatedUser = userRepository.save(user);
        
        // Audit log
        auditService.logUserUpdated(updatedUser);
        
        log.info("User updated successfully with ID: {}", updatedUser.getId());
        return userMapper.toResponse(updatedUser);
    }
    
    /**
     * Lấy thông tin người dùng theo ID
     */
    @Cacheable(value = "users", key = "#id")
    public UserResponse getUserById(Long id) {
        User user = findUserById(id);
        return userMapper.toResponse(user);
    }
    
    /**
     * Lấy thông tin người dùng theo username
     */
    @Cacheable(value = "users", key = "#username")
    public UserResponse getUserByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));
        return userMapper.toResponse(user);
    }
    
    /**
     * Tìm kiếm người dùng
     */
    public Page<UserResponse> searchUsers(String keyword, User.UserRole role, 
                                         User.UserStatus status, Long organizationId, 
                                         Pageable pageable) {
        log.info("Searching users with keyword: {}, role: {}, status: {}, organizationId: {}", 
                keyword, role, status, organizationId);
        
        Page<User> users = userRepository.searchUsers(keyword, role, status, organizationId, pageable);
        return users.map(userMapper::toResponse);
    }
    
    /**
     * Lấy danh sách người dùng theo tổ chức
     */
    public List<UserResponse> getUsersByOrganization(Long organizationId) {
        List<User> users = userRepository.findByOrganizationId(organizationId);
        return users.stream()
                .map(userMapper::toResponse)
                .toList();
    }
    
    /**
     * Lấy danh sách huấn luyện viên hoạt động
     */
    public List<UserResponse> getActiveInstructors(Long organizationId) {
        List<User> instructors = userRepository.findActiveInstructorsByOrganization(organizationId);
        return instructors.stream()
                .map(userMapper::toResponse)
                .toList();
    }
    
    /**
     * Phê duyệt người dùng
     */
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse approveUser(Long userId, Long approverId) {
        log.info("Approving user with ID: {} by approver: {}", userId, approverId);
        
        User user = findUserById(userId);
        
        if (user.getStatus() != User.UserStatus.PENDING_APPROVAL) {
            throw new BusinessException("User is not in pending approval status");
        }
        
        user.setStatus(User.UserStatus.ACTIVE);
        user.setUpdatedBy(approverId);
        
        User approvedUser = userRepository.save(user);
        
        // Send notification
        notificationService.sendUserApprovedNotification(approvedUser);
        
        // Audit log
        auditService.logUserApproved(approvedUser, approverId);
        
        log.info("User approved successfully with ID: {}", approvedUser.getId());
        return userMapper.toResponse(approvedUser);
    }
    
    /**
     * Từ chối người dùng
     */
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse rejectUser(Long userId, String reason, Long rejectorId) {
        log.info("Rejecting user with ID: {} by rejector: {}", userId, rejectorId);
        
        User user = findUserById(userId);
        
        if (user.getStatus() != User.UserStatus.PENDING_APPROVAL) {
            throw new BusinessException("User is not in pending approval status");
        }
        
        user.setStatus(User.UserStatus.INACTIVE);
        user.setNotes(user.getNotes() + "\nRejection reason: " + reason);
        user.setUpdatedBy(rejectorId);
        
        User rejectedUser = userRepository.save(user);
        
        // Send notification
        notificationService.sendUserRejectedNotification(rejectedUser, reason);
        
        // Audit log
        auditService.logUserRejected(rejectedUser, reason, rejectorId);
        
        log.info("User rejected successfully with ID: {}", rejectedUser.getId());
        return userMapper.toResponse(rejectedUser);
    }
    
    /**
     * Khóa tài khoản người dùng
     */
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse lockUser(Long userId, String reason, Long lockerId) {
        log.info("Locking user with ID: {} by locker: {}", userId, lockerId);
        
        User user = findUserById(userId);
        
        user.setStatus(User.UserStatus.LOCKED);
        user.setAccountLockedUntil(LocalDateTime.now().plusDays(30)); // Lock for 30 days
        user.setNotes(user.getNotes() + "\nLocked reason: " + reason);
        user.setUpdatedBy(lockerId);
        
        User lockedUser = userRepository.save(user);
        
        // Send notification
        notificationService.sendUserLockedNotification(lockedUser, reason);
        
        // Audit log
        auditService.logUserLocked(lockedUser, reason, lockerId);
        
        log.info("User locked successfully with ID: {}", lockedUser.getId());
        return userMapper.toResponse(lockedUser);
    }
    
    /**
     * Mở khóa tài khoản người dùng
     */
    @CacheEvict(value = "users", key = "#userId")
    public UserResponse unlockUser(Long userId, Long unlockerId) {
        log.info("Unlocking user with ID: {} by unlocker: {}", userId, unlockerId);
        
        User user = findUserById(userId);
        
        if (user.getStatus() != User.UserStatus.LOCKED) {
            throw new BusinessException("User is not locked");
        }
        
        user.setStatus(User.UserStatus.ACTIVE);
        user.setAccountLockedUntil(null);
        user.setFailedLoginAttempts(0);
        user.setUpdatedBy(unlockerId);
        
        User unlockedUser = userRepository.save(user);
        
        // Send notification
        notificationService.sendUserUnlockedNotification(unlockedUser);
        
        // Audit log
        auditService.logUserUnlocked(unlockedUser, unlockerId);
        
        log.info("User unlocked successfully with ID: {}", unlockedUser.getId());
        return userMapper.toResponse(unlockedUser);
    }
    
    /**
     * Đổi mật khẩu
     */
    @CacheEvict(value = "users", key = "#userId")
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        log.info("Changing password for user ID: {}", userId);
        
        User user = findUserById(userId);
        
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("Old password is incorrect");
        }
        
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setPasswordChangedAt(LocalDateTime.now());
        user.setFailedLoginAttempts(0);
        
        userRepository.save(user);
        
        // Send notification
        notificationService.sendPasswordChangedNotification(user);
        
        // Audit log
        auditService.logPasswordChanged(user);
        
        log.info("Password changed successfully for user ID: {}", userId);
    }
    
    /**
     * Reset mật khẩu
     */
    @CacheEvict(value = "users", key = "#userId")
    public String resetPassword(Long userId, Long resetById) {
        log.info("Resetting password for user ID: {} by admin: {}", userId, resetById);
        
        User user = findUserById(userId);
        
        String newPassword = generateTemporaryPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setPasswordChangedAt(LocalDateTime.now());
        user.setFailedLoginAttempts(0);
        
        userRepository.save(user);
        
        // Send notification with new password
        notificationService.sendPasswordResetNotification(user, newPassword);
        
        // Audit log
        auditService.logPasswordReset(user, resetById);
        
        log.info("Password reset successfully for user ID: {}", userId);
        return newPassword;
    }
    
    // Helper methods
    private User findUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));
    }
    
    private void validateUniqueFields(String username, String email, String cccd, Long excludeId) {
        if (username != null && userRepository.existsByUsername(username)) {
            Optional<User> existingUser = userRepository.findByUsername(username);
            if (existingUser.isPresent() && !existingUser.get().getId().equals(excludeId)) {
                throw new BusinessException("Username already exists: " + username);
            }
        }
        
        if (email != null && userRepository.existsByEmail(email)) {
            Optional<User> existingUser = userRepository.findByEmail(email);
            if (existingUser.isPresent() && !existingUser.get().getId().equals(excludeId)) {
                throw new BusinessException("Email already exists: " + email);
            }
        }
        
        if (cccd != null && userRepository.existsByCccd(cccd)) {
            // Implementation for CCCD validation
        }
    }
    
    private void validateOrganizationAccess(Long organizationId, User.UserRole role) {
        // Implementation for organization access validation
        // Check if the current user has permission to create users in this organization
    }
    
    private User.OrganizationLevel determineOrganizationLevel(User.UserRole role) {
        return switch (role) {
            case SUPER_ADMIN -> User.OrganizationLevel.NATIONAL;
            case CITY_ADMIN -> User.OrganizationLevel.CITY;
            case WARD_ADMIN -> User.OrganizationLevel.WARD;
            case INSTRUCTOR, STUDENT, PARENT -> User.OrganizationLevel.CLUB;
        };
    }
    
    private String generateTemporaryPassword() {
        // Generate a secure temporary password
        return "TempPass" + System.currentTimeMillis();
    }
}
