server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: admin-vhd
  
  # Database Configuration
  datasource:
    url: ********************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        
  # Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
          
  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      properties:
        enable.idempotence: true
    consumer:
      group-id: martial-arts-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "vn.gov.martial.dto.event"
      auto-offset-reset: earliest
      
  # Security Configuration
  security:
    jwt:
      secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
      expiration: 86400000 # 24 hours
      refresh-expiration: 604800000 # 7 days

# Logging Configuration
logging:
  level:
    vn.gov.martial: DEBUG
    org.springframework.security: DEBUG
    org.springframework.kafka: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/martial-arts.log

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Application Specific Configuration
app:
  name: "Hệ Thống Quản Lý Học Viên Võ Thuật"
  version: "1.0.0"
  description: "Martial Arts Student Management System - 3 Level Hierarchy"
  
  # File Upload Configuration
  upload:
    max-file-size: 10MB
    max-request-size: 50MB
    upload-dir: ${UPLOAD_DIR:uploads/}
    
  # Pagination Configuration
  pagination:
    default-page-size: 20
    max-page-size: 100
    
  # Cache Configuration
  cache:
    default-ttl: 3600 # 1 hour
    user-ttl: 1800 # 30 minutes
    
  # Notification Configuration
  notification:
    email:
      enabled: true
      from: <EMAIL>
    sms:
      enabled: true
      provider: viettel
    push:
      enabled: true
      
# Kafka Topics Configuration
kafka:
  topics:
    student-events: student-events
    exam-events: exam-events
    transfer-events: transfer-events
    attendance-events: attendance-events
    notification-events: notification-events

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ****************************************************************************
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop
logging:
  level:
    root: INFO
    vn.gov.martial: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}?useSSL=true&serverTimezone=UTC
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
logging:
  level:
    root: WARN
    vn.gov.martial: INFO
  file:
    name: /var/log/martial-arts/application.log

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
  h2:
    console:
      enabled: true
