#!/usr/bin/env python3
"""
Script to convert HTML to PDF using available libraries
"""
import os
import sys
import subprocess

def convert_with_browser():
    """Convert HTML to PDF using headless browser"""
    try:
        # Try using Chrome/Chromium headless
        html_file = "table-management-system-analysis.html"
        pdf_file = "table-management-system-analysis.pdf"
        
        # Get absolute path
        html_path = os.path.abspath(html_file)
        pdf_path = os.path.abspath(pdf_file)
        
        # Try different browser commands
        browsers = [
            "google-chrome",
            "chromium",
            "chromium-browser", 
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chromium.app/Contents/MacOS/Chromium"
        ]
        
        for browser in browsers:
            try:
                cmd = [
                    browser,
                    "--headless",
                    "--disable-gpu",
                    "--no-sandbox",
                    "--print-to-pdf=" + pdf_path,
                    "--print-to-pdf-no-header",
                    "file://" + html_path
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0 and os.path.exists(pdf_path):
                    print(f"✅ Successfully converted to PDF: {pdf_path}")
                    return True
                    
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
                
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def convert_with_pandoc():
    """Convert HTML to PDF using pandoc"""
    try:
        cmd = [
            "pandoc",
            "table-management-system-analysis.html",
            "-o", "table-management-system-analysis.pdf",
            "--pdf-engine=wkhtmltopdf"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Successfully converted to PDF using pandoc")
            return True
        else:
            print(f"❌ Pandoc error: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ Pandoc not found")
        return False

def main():
    print("🔄 Converting HTML to PDF...")
    
    # Check if HTML file exists
    if not os.path.exists("table-management-system-analysis.html"):
        print("❌ HTML file not found!")
        return False
    
    # Try different conversion methods
    methods = [
        ("Browser (Chrome/Chromium)", convert_with_browser),
        ("Pandoc", convert_with_pandoc)
    ]
    
    for method_name, method_func in methods:
        print(f"🔄 Trying {method_name}...")
        if method_func():
            return True
    
    print("❌ All conversion methods failed!")
    print("💡 You can manually open the HTML file in a browser and print to PDF")
    print(f"📁 HTML file location: {os.path.abspath('table-management-system-analysis.html')}")
    
    return False

if __name__ == "__main__":
    main()
