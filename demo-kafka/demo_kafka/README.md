# Kafka Demo Project

This project demonstrates a simple setup for a Spring Boot application that includes both a Kafka client and a Kafka server. The project is structured into two main components: the client and the server.

## Project Structure

```
demo_kafka
├── client
│   ├── src
│   │   ├── main
│   │   │   ├── java
│   │   │   │   └── com
│   │   │   │       └── example
│   │   │   │           └── client
│   │   │   │               └── DemoKafkaClientApplication.java
│   │   │   └── resources
│   │   │       └── application.properties
│   └── README.md
├── server
│   ├── src
│   │   ├── main
│   │   │   ├── java
│   │   │   │   └── com
│   │   │   │       └── example
│   │   │   │           └── server
│   │   │   │               └── DemoKafkaServerApplication.java
│   │   │   └── resources
│   │   │       └── application.properties
│   └── README.md
├── README.md
└── pom.xml
```

## Getting Started

### Prerequisites

- Java 17 or higher
- Apache Kafka
- Maven

### Setup Instructions

1. **Clone the repository:**
   ```
   git clone <repository-url>
   cd demo_kafka
   ```

2. **Build the project:**
   ```
   mvn clean install
   ```

3. **Start the Kafka server:**
   Follow the instructions provided in the Kafka documentation to start your Kafka server.

4. **Run the Server Application:**
   Navigate to the `server` directory and run the server application:
   ```
   mvn spring-boot:run
   ```

5. **Run the Client Application:**
   In a separate terminal, navigate to the `client` directory and run the client application:
   ```
   mvn spring-boot:run
   ```

## Usage

- The server application listens for messages on specified Kafka topics.
- The client application can send messages to these topics and consume messages from them.

## Additional Information

Refer to the individual `README.md` files in the `client` and `server` directories for more detailed documentation on each component.