# Kafka Client Application

This is the Kafka client application that interacts with the Kafka server. It is built using Spring Boot and provides functionality to produce and consume messages from Kafka topics.

## Setup Instructions

1. **Clone the Repository**: 
   Clone this repository to your local machine using the following command:
   ```
   git clone <repository-url>
   ```

2. **Navigate to the Client Directory**:
   Change your directory to the client folder:
   ```
   cd demo_kafka/client
   ```

3. **Build the Project**:
   Use Maven to build the project:
   ```
   mvn clean install
   ```

4. **Configure Application Properties**:
   Update the `application.properties` file located in `src/main/resources` with your Kafka server details and any other necessary configurations.

5. **Run the Application**:
   You can run the application using the following command:
   ```
   mvn spring-boot:run
   ```

## Usage

- The client application can be configured to produce messages to specific Kafka topics or consume messages from them.
- Ensure that the Kafka server is running before starting the client application.

## Dependencies

- Spring Boot
- Spring Kafka
- Other necessary dependencies as specified in the `pom.xml` file.

For more detailed information, refer to the documentation in the server application.