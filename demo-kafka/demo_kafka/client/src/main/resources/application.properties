# Kafka Client Application Properties

kafka.bootstrap-servers=localhost:9092
kafka.topic.name=my-topic
kafka.consumer.group-id=my-group
kafka.auto-offset-reset=earliest
kafka.key.serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.value.serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
kafka.value.deserializer=org.apache.kafka.common.serialization.StringDeserializer

# Logging Configuration
logging.level.org.apache.kafka=INFO
logging.level.com.example.client=DEBUG

# Application Info for Actuator
info.app.name=Kafka Client Application
info.app.version=1.0.0
info.app.description=Spring Boot Kafka Client Demo