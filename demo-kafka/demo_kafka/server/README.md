# Kafka Server Application

This is the server component of the Kafka project. It is built using Spring Boot and is responsible for handling Kafka messages.

## Setup Instructions

1. **Prerequisites**: Ensure you have Java 17 or higher and <PERSON><PERSON> installed on your machine.

2. **Clone the Repository**:
   ```bash
   git clone <repository-url>
   cd demo_kafka/server
   ```

3. **Build the Project**:
   Use Maven to build the project:
   ```bash
   mvn clean install
   ```

4. **Run the Application**:
   You can run the server application using the following command:
   ```bash
   mvn spring-boot:run
   ```

## Usage

- The server listens for messages from Kafka topics and processes them accordingly.
- Ensure that your Kafka broker is running and properly configured in the `application.properties` file.

## Dependencies

- Spring Boot
- Spring Kafka
- Other necessary dependencies as specified in the `pom.xml` file.

## Additional Information

Refer to the `application.properties` file for configuration details and adjust the settings as needed for your environment.