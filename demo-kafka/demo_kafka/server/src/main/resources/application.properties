# Kafka Server Application Properties

# Kafka Server Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=demo-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

# Application Info for Actuator
info.app.name=Demo Kafka Server
info.app.version=1.0.0
info.app.description=Kafka server application for handling messages

# Custom Properties for demonstration
demo.kafka.topic.name=my-topic
demo.kafka.listener.concurrency=3
demo.kafka.listener.ack-mode=manual