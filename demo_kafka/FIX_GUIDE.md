# 🔧 Fix Guide - Kafka Consumer Issue

## 🐛 Vấn đề hiện tại

Consumer đã nhận messages từ Kafka (LAG = 0) nhưng **không lưu vào database**.

## 🔍 Nguyên nhân có thể

1. **JSON Deserialization Error** - Consumer không parse được JSON từ Producer
2. **Database Transaction Error** - Lỗi khi lưu vào H2 database
3. **Configuration Mismatch** - C<PERSON>u hình không khớp giữa Producer và Consumer

## ✅ Cách fix

### Bước 1: Restart Consumer với debug logs

1. **Dừng Consumer application** (Ctrl+C)

2. **Restart Consumer** để áp dụng code changes:
   ```bash
   cd kafka-consumer
   mvn spring-boot:run
   ```

3. **<PERSON>uan sát logs** - sẽ thấy debug messages như:
   ```
   📥 [DEBUG] Received raw message from topic 'user-events'
   🔍 [DEBUG] Raw message content: {"id":123,"name":"Test"...}
   ```

### Bước 2: Test lại

```bash
cd demo_kafka
./debug-test.sh
```

### Bước 3: Kiểm tra logs

Nếu vẫn có lỗi, check Consumer logs để tìm:
- `❌ Error processing user message`
- `JsonParseException`
- `SQLException`
- `ConstraintViolationException`

## 🚀 Alternative: Simple Working Version

Nếu vẫn gặp vấn đề, có thể dùng version đơn giản:

### Consumer chỉ log messages (không lưu DB):

```java
@KafkaListener(topics = "user-events", groupId = "demo-group")
public void consume(String message) {
    logger.info("Received: {}", message);
}
```

### Test với curl:

```bash
# Send message
curl -X POST http://localhost:8081/api/messages/user \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "name": "Test", "email": "<EMAIL>", "action": "CREATE"}'

# Check Consumer logs - should see:
# Received: {"id":1,"name":"Test"...}
```

## 📊 Verification

Sau khi fix, test script sẽ show:
```
✅ Message found in database!
📊 Total processed messages: 1
```

## 🔗 Useful Commands

```bash
# Check Kafka topics
./check-kafka.sh

# Simple test
./simple-test.sh

# Debug test
./debug-test.sh

# Check H2 database
# URL: http://localhost:8082/h2-console
# JDBC: jdbc:h2:mem:consumerdb
```
