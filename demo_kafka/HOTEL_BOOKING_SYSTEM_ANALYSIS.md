# 🏨 Hotel Booking Management System - <PERSON><PERSON> tích hệ thống

## 📋 Tổng quan dự án

### 🎯 Mục tiêu
Xây dựng hệ thống quản lý đặt phòng khách sạn hoàn chỉnh với công nghệ hiện đại, hỗ trợ high availability, scalability và real-time features.

### 🛠️ Technology Stack
- **Backend**: Java 17 + Spring Boot 3.2
- **Database**: MySQL 8.0 (Primary) + Elasticsearch (Search)
- **Message Broker**: Apache Kafka 3.5
- **Caching**: Redis 7.0 Cluster
- **API Gateway**: Spring Cloud Gateway
- **Monitoring**: Prometheus + Grafana + ELK Stack

## 🏗️ Kiến trúc hệ thống

### 📦 Microservices Architecture

#### 1. **User Service** (Port 8081)
- **Chức năng**: Authentication, user management, profiles
- **Database**: users, user_profiles, user_preferences
- **Cache**: User sessions, profile data
- **Events**: user.registered, user.updated, user.login

#### 2. **Hotel Service** (Port 8082)
- **Chức năng**: Hotel management, room types, amenities
- **Database**: hotels, room_types, rooms, amenities
- **Cache**: Hotel details, room information
- **Events**: hotel.updated, room.added, pricing.updated

#### 3. **Booking Service** (Port 8083)
- **Chức năng**: Reservation management, availability checking
- **Database**: bookings, booking_items, booking_history
- **Cache**: Room availability, booking status
- **Events**: booking.created, booking.updated, booking.cancelled

#### 4. **Payment Service** (Port 8084)
- **Chức năng**: Payment processing, refunds, billing
- **Database**: payments, transactions, refunds
- **Events**: payment.initiated, payment.completed, payment.failed

#### 5. **Search Service** (Port 8085)
- **Chức năng**: Hotel search, filtering, recommendations
- **Database**: Elasticsearch index
- **Cache**: Search results, popular searches
- **Events**: search.performed, recommendation.generated

#### 6. **Notification Service** (Port 8086)
- **Chức năng**: Email, SMS, push notifications
- **Events**: notification.email, notification.sms, notification.push

## 🗄️ Database Design

### 📊 Core Tables

#### Users Table
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    user_type ENUM('CUSTOMER', 'HOTEL_STAFF', 'ADMIN') DEFAULT 'CUSTOMER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### Hotels Table
```sql
CREATE TABLE hotels (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address VARCHAR(500) NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    phone VARCHAR(20),
    email VARCHAR(255),
    amenities JSON,
    rating DECIMAL(3, 2) DEFAULT 0.00,
    total_rooms INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### Bookings Table
```sql
CREATE TABLE bookings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    hotel_id BIGINT NOT NULL,
    room_id BIGINT NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    guests_count INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('PENDING', 'CONFIRMED', 'CHECKED_IN', 'CHECKED_OUT', 'CANCELLED') DEFAULT 'PENDING',
    special_requests TEXT,
    booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (hotel_id) REFERENCES hotels(id),
    FOREIGN KEY (room_id) REFERENCES rooms(id)
);
```

### 🔍 Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX idx_hotels_location ON hotels(city, country);
CREATE INDEX idx_bookings_dates ON bookings(check_in_date, check_out_date);
CREATE INDEX idx_bookings_user_status ON bookings(user_id, status);
CREATE INDEX idx_availability_date_room ON availability(date, room_type_id);
CREATE INDEX idx_payments_booking ON payments(booking_id, status);
```

## ⚡ Kafka Event Streaming

### 📨 Topics Configuration

#### 1. **booking-events** (3 partitions, replication factor 3)
```json
{
  "eventType": "booking.created",
  "eventId": "booking-123-created-20241211",
  "timestamp": "2024-12-11T10:30:00Z",
  "data": {
    "bookingId": "BK-2024-001234",
    "userId": 12345,
    "hotelId": 67890,
    "checkInDate": "2024-12-15",
    "checkOutDate": "2024-12-18",
    "totalAmount": 450.00,
    "status": "CONFIRMED"
  }
}
```

#### 2. **payment-events** (3 partitions, replication factor 3)
```json
{
  "eventType": "payment.completed",
  "eventId": "payment-456-completed-20241211",
  "timestamp": "2024-12-11T10:35:00Z",
  "data": {
    "paymentId": "PAY-2024-001234",
    "bookingId": "BK-2024-001234",
    "amount": 450.00,
    "paymentMethod": "CREDIT_CARD",
    "status": "COMPLETED"
  }
}
```

#### 3. **notification-events** (2 partitions, replication factor 3)
```json
{
  "eventType": "notification.email",
  "eventId": "notif-789-email-20241211",
  "timestamp": "2024-12-11T10:40:00Z",
  "data": {
    "recipient": "<EMAIL>",
    "template": "booking_confirmation",
    "variables": {
      "bookingId": "BK-2024-001234",
      "hotelName": "Grand Hotel",
      "checkInDate": "2024-12-15"
    }
  }
}
```

### 🔄 Event Flow Patterns

#### Saga Pattern for Booking Process
1. **Booking Created** → Check room availability
2. **Room Reserved** → Process payment
3. **Payment Completed** → Confirm booking
4. **Booking Confirmed** → Send notifications
5. **Compensation**: If any step fails, rollback previous steps

## 🚀 Redis Caching Strategy

### 🔑 Cache Patterns

#### 1. **Session Management**
```java
// Key pattern: session:{sessionId}
// TTL: 24 hours
"session:abc123" → {
  "userId": 12345,
  "email": "<EMAIL>",
  "roles": ["CUSTOMER"],
  "lastActivity": "2024-12-11T10:30:00Z"
}
```

#### 2. **Room Availability Cache**
```java
// Key pattern: availability:{hotelId}:{date}
// TTL: 5 minutes
"availability:67890:2024-12-15" → {
  "roomType1": 5,
  "roomType2": 3,
  "roomType3": 0
}
```

#### 3. **Search Results Cache**
```java
// Key pattern: search:{criteriaHash}
// TTL: 15 minutes
"search:abc123def456" → [
  {
    "hotelId": 67890,
    "name": "Grand Hotel",
    "price": 150.00,
    "rating": 4.5
  }
]
```

#### 4. **Rate Limiting**
```java
// Key pattern: rate_limit:{userId}:{endpoint}
// TTL: 1 minute
"rate_limit:12345:search" → {
  "count": 10,
  "resetTime": "2024-12-11T10:31:00Z"
}
```

### 🌍 Geospatial Features
```java
// Hotel locations for proximity search
GEOADD hotels:locations 
  -74.0059 40.7128 "hotel:1"
  -73.9857 40.7484 "hotel:2"

// Find hotels within 5km radius
GEORADIUS hotels:locations -74.0059 40.7128 5 km
```

## 🌐 API Design

### 🔐 Authentication & Authorization
```java
// JWT Token structure
{
  "sub": "12345",
  "email": "<EMAIL>",
  "roles": ["CUSTOMER"],
  "iat": 1702291800,
  "exp": 1702378200
}
```

### 📱 Core API Endpoints

#### User Management
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile

#### Hotel Search
- `GET /api/search/hotels` - Search hotels
- `GET /api/hotels/{id}` - Get hotel details
- `GET /api/hotels/{id}/availability` - Check availability

#### Booking Management
- `POST /api/bookings` - Create booking
- `GET /api/bookings/{id}` - Get booking details
- `PUT /api/bookings/{id}` - Update booking
- `DELETE /api/bookings/{id}` - Cancel booking

#### Payment Processing
- `POST /api/payments/initiate` - Initiate payment
- `POST /api/payments/confirm` - Confirm payment
- `GET /api/payments/{id}` - Get payment status

## 📱 Real-time Features

### 🔄 WebSocket Channels
- `/topic/availability/{hotelId}` - Room availability updates
- `/topic/booking/{bookingId}` - Booking status updates
- `/topic/notifications/{userId}` - User notifications
- `/topic/admin/dashboard` - Admin dashboard updates

### 📊 Real-time Use Cases
1. **Live Availability Updates**: When rooms are booked/cancelled
2. **Booking Status Tracking**: Real-time booking progress
3. **Price Changes**: Dynamic pricing updates
4. **Admin Dashboard**: Live metrics and alerts

## 🔒 Security & Performance

### 🛡️ Security Measures
- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Redis-based rate limiting
- **Data Encryption**: AES-256 for sensitive data
- **API Security**: HTTPS, CORS, input validation
- **PCI DSS Compliance**: For payment processing

### ⚡ Performance Optimizations
- **Database**: Read replicas, connection pooling
- **Caching**: Multi-level caching strategy
- **CDN**: Static content delivery
- **Load Balancing**: Horizontal scaling
- **Database Partitioning**: By date ranges
- **Async Processing**: Non-blocking operations

## 📊 Monitoring & Observability

### 📈 Metrics Collection
- **Application Metrics**: Response times, error rates
- **Business Metrics**: Bookings per hour, revenue
- **Infrastructure Metrics**: CPU, memory, disk usage
- **Custom Metrics**: Cache hit rates, queue lengths

### 🔍 Logging Strategy
- **Structured Logging**: JSON format with correlation IDs
- **Centralized Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Log Levels**: ERROR, WARN, INFO, DEBUG
- **Audit Logging**: User actions, data changes

### 🚨 Alerting Rules
- **High Error Rate**: > 5% in 5 minutes
- **High Response Time**: > 2 seconds average
- **Database Connection Issues**: Connection pool exhaustion
- **Kafka Lag**: Consumer lag > 1000 messages
- **Cache Miss Rate**: > 50% for critical data
