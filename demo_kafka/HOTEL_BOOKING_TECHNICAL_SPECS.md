# 🔧 Hotel Booking System - Technical Specifications

## 🛠️ Technology Stack Details

### 🖥️ Backend Technologies
- **Java 17** - LTS version với modern features
- **Spring Boot 3.2** - Latest stable version
- **Spring Cloud 2023.0** - Microservices framework
- **Spring Security 6** - Authentication & authorization
- **Spring Data JPA** - Database abstraction
- **Spring Kafka** - Event streaming integration

### 🗄️ Database Technologies
- **MySQL 8.0** - Primary relational database
- **Redis 7.0** - Caching và session store
- **Elasticsearch 8.0** - Search và analytics
- **Apache Kafka 3.5** - Event streaming platform

### 🔧 Infrastructure & DevOps
- **Docker & Docker Compose** - Containerization
- **Kubernetes** - Container orchestration
- **Nginx** - Load balancer và reverse proxy
- **Prometheus & Grafana** - Monitoring
- **ELK Stack** - Centralized logging

## 📊 System Capacity Planning

### 🎯 Performance Requirements
- **Concurrent Users**: 10,000 active users
- **API Response Time**: < 200ms (95th percentile)
- **Database Queries**: < 100ms average
- **Search Response**: < 500ms
- **Booking Processing**: < 2 seconds end-to-end

### 💾 Storage Requirements
- **Database Storage**: 500GB initial, 1TB projected
- **Redis Memory**: 32GB cluster
- **Elasticsearch**: 200GB for search indexes
- **File Storage**: 1TB for images và documents

### 🌐 Network Requirements
- **Bandwidth**: 1Gbps minimum
- **CDN**: Global content delivery
- **Load Balancer**: 99.9% uptime SLA
- **SSL/TLS**: End-to-end encryption

## 🏗️ Microservices Detailed Design

### 🔐 User Service (Port 8081)
```yaml
Service: user-service
Database: user_db
Cache: user_sessions, user_profiles
Resources:
  CPU: 2 cores
  Memory: 4GB
  Storage: 50GB
Scaling: Horizontal (2-10 instances)
```

**APIs:**
- `POST /auth/login` - User authentication
- `POST /auth/register` - User registration
- `GET /users/{id}` - Get user profile
- `PUT /users/{id}` - Update user profile
- `GET /users/{id}/bookings` - User booking history

### 🏨 Hotel Service (Port 8082)
```yaml
Service: hotel-service
Database: hotel_db
Cache: hotel_details, room_availability
Resources:
  CPU: 4 cores
  Memory: 8GB
  Storage: 100GB
Scaling: Horizontal (3-15 instances)
```

**APIs:**
- `GET /hotels` - List hotels với pagination
- `GET /hotels/{id}` - Hotel details
- `GET /hotels/{id}/rooms` - Available rooms
- `POST /hotels` - Create hotel (admin)
- `PUT /hotels/{id}` - Update hotel (admin)

### 📅 Booking Service (Port 8083)
```yaml
Service: booking-service
Database: booking_db
Cache: booking_status, availability_cache
Resources:
  CPU: 4 cores
  Memory: 8GB
  Storage: 200GB
Scaling: Horizontal (5-20 instances)
```

**APIs:**
- `POST /bookings` - Create new booking
- `GET /bookings/{id}` - Booking details
- `PUT /bookings/{id}` - Modify booking
- `DELETE /bookings/{id}` - Cancel booking
- `GET /availability/check` - Check room availability

### 💳 Payment Service (Port 8084)
```yaml
Service: payment-service
Database: payment_db
Cache: payment_status
Resources:
  CPU: 2 cores
  Memory: 4GB
  Storage: 100GB
Scaling: Horizontal (3-10 instances)
Security: PCI DSS compliant
```

**APIs:**
- `POST /payments/initiate` - Start payment process
- `POST /payments/confirm` - Confirm payment
- `GET /payments/{id}` - Payment status
- `POST /payments/refund` - Process refund

## 🔄 Event Streaming Architecture

### 📨 Kafka Topics Configuration

#### booking-events
```yaml
Topic: booking-events
Partitions: 6
Replication Factor: 3
Retention: 7 days
Compression: snappy
```

**Event Types:**
- `booking.created` - New booking created
- `booking.confirmed` - Booking confirmed
- `booking.modified` - Booking details changed
- `booking.cancelled` - Booking cancelled
- `booking.checked_in` - Guest checked in
- `booking.checked_out` - Guest checked out

#### payment-events
```yaml
Topic: payment-events
Partitions: 3
Replication Factor: 3
Retention: 30 days
Compression: snappy
```

**Event Types:**
- `payment.initiated` - Payment process started
- `payment.processing` - Payment in progress
- `payment.completed` - Payment successful
- `payment.failed` - Payment failed
- `payment.refunded` - Refund processed

#### notification-events
```yaml
Topic: notification-events
Partitions: 4
Replication Factor: 3
Retention: 3 days
Compression: snappy
```

**Event Types:**
- `notification.email` - Email notification
- `notification.sms` - SMS notification
- `notification.push` - Push notification
- `notification.in_app` - In-app notification

### 🔄 Event Processing Patterns

#### Saga Pattern Implementation
```java
@Component
public class BookingSaga {
    
    @SagaOrchestrationStart
    public void handleBookingCreated(BookingCreatedEvent event) {
        // Step 1: Reserve room
        sagaManager.choreography()
            .step("reserve-room")
            .compensatedBy("release-room")
            .invoke(roomService::reserveRoom, event.getRoomId());
    }
    
    @SagaOrchestrationStep("reserve-room")
    public void handleRoomReserved(RoomReservedEvent event) {
        // Step 2: Process payment
        sagaManager.choreography()
            .step("process-payment")
            .compensatedBy("refund-payment")
            .invoke(paymentService::processPayment, event.getBookingId());
    }
}
```

## 🚀 Redis Caching Strategy

### 🔑 Cache Patterns và TTL

#### Session Management
```java
@Service
public class SessionCacheService {
    
    // Pattern: session:{sessionId}
    // TTL: 24 hours
    public void cacheUserSession(String sessionId, UserSession session) {
        redisTemplate.opsForValue().set(
            "session:" + sessionId, 
            session, 
            Duration.ofHours(24)
        );
    }
}
```

#### Hotel Data Caching
```java
@Service
public class HotelCacheService {
    
    // Pattern: hotel:{hotelId}
    // TTL: 1 hour
    public void cacheHotelDetails(Long hotelId, Hotel hotel) {
        redisTemplate.opsForValue().set(
            "hotel:" + hotelId, 
            hotel, 
            Duration.ofHours(1)
        );
    }
    
    // Pattern: availability:{hotelId}:{date}
    // TTL: 5 minutes
    public void cacheAvailability(Long hotelId, LocalDate date, 
                                 Map<Long, Integer> availability) {
        String key = String.format("availability:%d:%s", hotelId, date);
        redisTemplate.opsForHash().putAll(key, availability);
        redisTemplate.expire(key, Duration.ofMinutes(5));
    }
}
```

#### Search Results Caching
```java
@Service
public class SearchCacheService {
    
    // Pattern: search:{criteriaHash}
    // TTL: 15 minutes
    public void cacheSearchResults(SearchCriteria criteria, 
                                  List<HotelSearchResult> results) {
        String key = "search:" + criteria.hashCode();
        redisTemplate.opsForValue().set(key, results, Duration.ofMinutes(15));
    }
}
```

### 🌍 Geospatial Features
```java
@Service
public class GeospatialService {
    
    // Add hotel location
    public void addHotelLocation(Long hotelId, double lat, double lon) {
        redisTemplate.opsForGeo().add("hotels:locations", 
            new Point(lon, lat), hotelId.toString());
    }
    
    // Find nearby hotels
    public List<GeoResult<RedisGeoCommands.GeoLocation<String>>> 
           findNearbyHotels(double lat, double lon, double radiusKm) {
        
        Circle circle = new Circle(new Point(lon, lat), 
            new Distance(radiusKm, Metrics.KILOMETERS));
        
        return redisTemplate.opsForGeo()
            .radius("hotels:locations", circle);
    }
}
```

## 🔒 Security Implementation

### 🛡️ Authentication & Authorization
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .requestMatchers("/api/hotels/**").hasAnyRole("USER", "ADMIN")
                .anyRequest().authenticated())
            .oauth2ResourceServer(oauth2 -> oauth2.jwt(Customizer.withDefaults()))
            .build();
    }
}
```

### 🔐 JWT Token Configuration
```java
@Component
public class JwtTokenProvider {
    
    private final String secretKey = "mySecretKey";
    private final long validityInMilliseconds = 3600000; // 1h
    
    public String createToken(String username, List<String> roles) {
        Claims claims = Jwts.claims().setSubject(username);
        claims.put("roles", roles);
        
        Date now = new Date();
        Date validity = new Date(now.getTime() + validityInMilliseconds);
        
        return Jwts.builder()
            .setClaims(claims)
            .setIssuedAt(now)
            .setExpiration(validity)
            .signWith(SignatureAlgorithm.HS256, secretKey)
            .compact();
    }
}
```

## 📊 Database Optimization

### 🔍 Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX idx_hotels_location ON hotels(city, country);
CREATE INDEX idx_hotels_rating ON hotels(rating DESC);
CREATE INDEX idx_bookings_dates ON bookings(check_in_date, check_out_date);
CREATE INDEX idx_bookings_user_status ON bookings(user_id, status);
CREATE INDEX idx_availability_date_room ON availability(date, room_type_id);
CREATE INDEX idx_payments_booking_status ON payments(booking_id, status);

-- Composite indexes
CREATE INDEX idx_bookings_hotel_dates ON bookings(hotel_id, check_in_date, check_out_date);
CREATE INDEX idx_rooms_hotel_type ON rooms(hotel_id, room_type_id, status);
```

### 🔄 Database Partitioning
```sql
-- Partition bookings table by year
CREATE TABLE bookings (
    id BIGINT NOT NULL,
    booking_date DATE NOT NULL,
    -- other columns
    PRIMARY KEY (id, booking_date)
) PARTITION BY RANGE (YEAR(booking_date)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 📈 Connection Pool Configuration
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      leak-detection-threshold: 60000
```

## 📱 API Design Standards

### 🌐 RESTful API Guidelines
```java
@RestController
@RequestMapping("/api/v1/hotels")
@Validated
public class HotelController {
    
    @GetMapping
    public ResponseEntity<PagedResponse<HotelSummary>> getHotels(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String country) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Hotel> hotels = hotelService.findHotels(city, country, pageable);
        
        return ResponseEntity.ok(new PagedResponse<>(
            hotels.getContent().stream()
                .map(HotelSummary::from)
                .collect(Collectors.toList()),
            hotels.getNumber(),
            hotels.getSize(),
            hotels.getTotalElements(),
            hotels.getTotalPages()
        ));
    }
}
```

### 📝 API Response Format
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "name": "Grand Hotel",
    "rating": 4.5
  },
  "message": "Hotel retrieved successfully",
  "timestamp": "2024-12-11T10:30:00Z",
  "errors": null
}
```

### ⚠️ Error Response Format
```json
{
  "success": false,
  "data": null,
  "message": "Validation failed",
  "timestamp": "2024-12-11T10:30:00Z",
  "errors": [
    {
      "field": "email",
      "message": "Email format is invalid"
    }
  ]
}
```

## 📊 Monitoring & Observability

### 📈 Metrics Collection
```java
@Component
public class BookingMetrics {
    
    private final Counter bookingCounter;
    private final Timer bookingTimer;
    private final Gauge activeBookings;
    
    public BookingMetrics(MeterRegistry meterRegistry) {
        this.bookingCounter = Counter.builder("bookings.created")
            .description("Number of bookings created")
            .register(meterRegistry);
            
        this.bookingTimer = Timer.builder("booking.processing.time")
            .description("Booking processing time")
            .register(meterRegistry);
            
        this.activeBookings = Gauge.builder("bookings.active")
            .description("Number of active bookings")
            .register(meterRegistry, this, BookingMetrics::getActiveBookingCount);
    }
}
```

### 🔍 Distributed Tracing
```java
@Service
@Slf4j
public class BookingService {
    
    @NewSpan("booking-creation")
    public Booking createBooking(@SpanTag("userId") Long userId, 
                                @SpanTag("hotelId") Long hotelId,
                                BookingRequest request) {
        
        Span span = tracer.nextSpan()
            .name("booking-validation")
            .tag("booking.type", request.getType())
            .start();
            
        try (Tracer.SpanInScope ws = tracer.withSpanInScope(span)) {
            // Booking logic
            return processBooking(request);
        } finally {
            span.end();
        }
    }
}
```

## 🚀 Deployment Configuration

### 🐳 Docker Configuration
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY target/hotel-booking-service.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### ☸️ Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: booking-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: booking-service
  template:
    metadata:
      labels:
        app: booking-service
    spec:
      containers:
      - name: booking-service
        image: hotel-booking/booking-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
```
