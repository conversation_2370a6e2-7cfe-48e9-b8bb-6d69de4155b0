# 🔍 Partition vs Offset trong Apache Kafka

## 📋 Tóm tắt nhanh

| <PERSON>h<PERSON> cạnh | **Partition** | **Offset** |
|-----------|---------------|------------|
| **Định nghĩa** | Phân chia vật lý của Topic | Vị trí duy nhất của message trong partition |
| **Phạm vi** | Topic level | Partition level |
| **Mục đích** | Phân tán và song song hóa | Định danh và thứ tự message |
| **Tính chất** | Static (số lượng cố định) | Dynamic (tăng dần) |
| **Bắt đầu từ** | 0, 1, 2... (partition ID) | 0 (cho mỗi partition) |
| **Quản lý bởi** | Kafka Admin/Producer | Kafka Broker tự động |

## 🏗️ PARTITION - Phân chia vật lý

### Kh<PERSON><PERSON> niệm cơ bản
- **Partition** là cách Kafka chia nhỏ một Topic thành các phần độc lập
- Mỗi partition là một **ordered log** của messages
- Số lượng partitions được **cố định** khi tạo topic
- Partitions được **phân tán** trên nhiều Kafka brokers

### Đặc điểm chính

#### 1. **Phân chia dữ liệu**
```
Topic: user-events (3 partitions)
├── Partition 0: [msg0, msg3, msg6, msg9...]
├── Partition 1: [msg1, msg4, msg7, msg10...]
└── Partition 2: [msg2, msg5, msg8, msg11...]
```

#### 2. **Message Routing**
```java
// Trong Producer code
kafkaTemplate.send(topic, userMessage.getId().toString(), userMessage);
//                      ↑
//                 Message Key
//                      ↓
//            hash(key) % partition_count
//                      ↓
//               Target Partition
```

#### 3. **Scaling & Performance**
- **Horizontal scaling**: Thêm partitions = tăng throughput
- **Parallel processing**: Mỗi partition có thể được xử lý độc lập
- **Load balancing**: Messages phân tán đều across partitions

### Ví dụ trong Demo

#### Producer Configuration:
```properties
# application.properties
kafka.topic.user-events=user-events
```

#### Routing Logic:
```java
public void sendUserMessage(UserMessage userMessage) {
    // Key = userMessage.getId().toString()
    // Kafka sẽ hash key này để chọn partition
    kafkaTemplate.send(userEventsTopic, 
                      userMessage.getId().toString(), 
                      userMessage);
}
```

#### Partition Assignment:
```
Key: "user-1" → hash: 1001 → 1001 % 3 = 2 → Partition 2
Key: "user-2" → hash: 1002 → 1002 % 3 = 0 → Partition 0  
Key: "user-3" → hash: 1003 → 1003 % 3 = 1 → Partition 1
```

## 📍 OFFSET - Vị trí message

### Khái niệm cơ bản
- **Offset** là ID duy nhất của message trong một partition
- Bắt đầu từ **0** và tăng dần theo thứ tự
- **Immutable** - không thay đổi sau khi được assign
- **Per-partition** - mỗi partition có sequence offset riêng

### Đặc điểm chính

#### 1. **Sequential ID**
```
Partition 0: [offset:0] [offset:1] [offset:2] [offset:3]...
Partition 1: [offset:0] [offset:1] [offset:2] [offset:3]...
Partition 2: [offset:0] [offset:1] [offset:2] [offset:3]...
```

#### 2. **Consumer Tracking**
```java
@KafkaListener(topics = "user-events")
public void consume(
    @Payload UserMessage message,
    @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
    @Header(KafkaHeaders.OFFSET) long offset) {
    
    logger.info("Received from partition: {}, offset: {}", partition, offset);
}
```

#### 3. **Offset Management**
- **Current Offset**: Vị trí message tiếp theo sẽ đọc
- **Committed Offset**: Vị trí đã xử lý xong
- **LAG**: Hiệu số giữa latest và committed offset

### Consumer Offset States

#### Auto Commit (Default):
```properties
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=1000ms
```

#### Manual Commit:
```java
@KafkaListener(topics = "user-events")
public void consume(UserMessage message, Acknowledgment ack) {
    try {
        processMessage(message);
        ack.acknowledge(); // Manual commit
    } catch (Exception e) {
        // Don't commit on error - message will be reprocessed
    }
}
```

## 🔄 Workflow Example

### 1. Producer gửi message
```java
// Message với key "user-123"
UserMessage msg = new UserMessage(123L, "John", "<EMAIL>", "CREATE");
producer.sendUserMessage(msg);
```

### 2. Kafka routing
```
Key: "123" → hash(123) % 3 = 0 → Partition 0
Partition 0 current offset: 15
→ Message được lưu tại Partition 0, Offset 15
```

### 3. Consumer nhận message
```java
// Consumer nhận được:
// partition = 0
// offset = 15  
// message = UserMessage{id=123, name="John"...}
```

### 4. Offset commit
```
Consumer commit offset 16 cho Partition 0
→ Lần poll tiếp theo sẽ bắt đầu từ offset 16
```

## 🎯 Practical Commands

### Kiểm tra Partitions:
```bash
# Xem partition info của topic
docker exec kafka kafka-topics \
  --bootstrap-server localhost:9092 \
  --describe --topic user-events
```

### Kiểm tra Offsets:
```bash
# Xem consumer group offsets
docker exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --describe --group demo-consumer-group
```

### Output mẫu:
```
TOPIC       PARTITION  CURRENT-OFFSET  LOG-END-OFFSET  LAG
user-events 0          15              15              0
user-events 1          8               8               0  
user-events 2          12              12              0
```

**Giải thích:**
- **PARTITION**: ID của partition (0, 1, 2)
- **CURRENT-OFFSET**: Offset tiếp theo sẽ đọc
- **LOG-END-OFFSET**: Offset cao nhất trong partition
- **LAG**: Số messages chưa xử lý (0 = đã catch up)

## ⚙️ Configuration trong Demo

### Producer Settings:
```properties
# kafka-producer/application.properties
spring.kafka.producer.key-serializer=StringSerializer
spring.kafka.producer.value-serializer=JsonSerializer
kafka.topic.user-events=user-events
```

### Consumer Settings:
```properties
# kafka-consumer/application.properties
spring.kafka.consumer.group-id=demo-consumer-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=1000ms
```

### Docker Compose Settings:
```yaml
# docker-compose.yml
environment:
  KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
  KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
```

## 🚨 Common Issues & Solutions

### 1. **Partition Imbalance**
**Problem**: Một partition có quá nhiều messages
**Solution**: 
- Sử dụng better key distribution
- Tăng số partitions (chỉ cho messages mới)

### 2. **Consumer Lag**
**Problem**: LAG tăng cao
**Solution**:
- Scale up consumers (max = số partitions)
- Optimize processing logic
- Increase fetch settings

### 3. **Offset Reset**
**Problem**: Consumer bắt đầu từ đầu/cuối
**Solution**:
```properties
# Đọc từ đầu nếu chưa có committed offset
auto-offset-reset=earliest

# Chỉ đọc messages mới
auto-offset-reset=latest
```

## 💡 Best Practices

### Partition Design:
1. **Key Selection**: Chọn key có distribution tốt
2. **Partition Count**: Bắt đầu với 3-6, scale theo needs
3. **Ordering**: Cùng key → cùng partition → đảm bảo order

### Offset Management:
1. **Auto Commit**: OK cho most use cases
2. **Manual Commit**: Dùng khi cần exactly-once processing
3. **Monitoring**: Track LAG thường xuyên

### Performance Tuning:
```properties
# Producer
batch-size=16384
linger-ms=1
acks=all

# Consumer  
fetch-min-size=1
max-poll-records=500
session-timeout-ms=30000
```

## 🎯 Key Takeaways

### PARTITION:
- 🏗️ **Structural**: Cách tổ chức dữ liệu
- 🎯 **Purpose**: Scaling và parallelism  
- 🔑 **Routing**: Message key quyết định partition
- 👥 **Consumers**: 1 consumer per partition trong cùng group

### OFFSET:
- 📍 **Position**: Vị trí message trong partition
- ⏰ **Sequential**: Tăng dần theo thời gian
- 💾 **Persistent**: Lưu trong `__consumer_offsets`
- 🔄 **Resumable**: Consumer có thể resume từ last committed offset

### Relationship:
```
Message Address = Topic + Partition + Offset
Example: user-events + Partition 2 + Offset 15
```

Hiểu rõ Partition và Offset là foundation để master Kafka architecture!
