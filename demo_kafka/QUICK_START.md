# 🚀 Quick Start Guide - Kafka + Spring Boot 3 Demo

## ⚡ 5-Minute Setup

### 1. Khởi động Kafka Infrastructure
```bash
cd demo_kafka
docker-compose up -d
```
*Chờ 30 giây để Kafka khởi động hoàn toàn*

### 2. Khởi động Consumer (Terminal 1)
```bash
cd kafka-consumer
mvn spring-boot:run
```
*Consumer sẽ chạy trên port 8082*

### 3. Khởi động Producer (Terminal 2)
```bash
cd kafka-producer  
mvn spring-boot:run
```
*Producer sẽ chạy trên port 8081*

### 4. Test Demo (Terminal 3)
```bash
cd demo_kafka
./test-demo.sh
```

## 🎯 Test nhanh bằng curl

### Gửi User Message
```bash
curl -X POST http://localhost:8081/api/messages/user \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "action": "CREATE"}'
```

### Gửi Notification
```bash
curl -X POST http://localhost:8081/api/messages/notification \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello World!", "type": "INFO", "recipient": "<EMAIL>"}'
```

### Xem Processed Messages
```bash
curl http://localhost:8082/api/consumer/users
curl http://localhost:8082/api/consumer/notifications
curl http://localhost:8082/api/consumer/stats
```

## 🌐 Web Interfaces

- **Kafka UI**: http://localhost:8080
- **H2 Database Console**: http://localhost:8082/h2-console
  - JDBC URL: `jdbc:h2:mem:consumerdb`
  - Username: `sa`
  - Password: (empty)

## 📊 Monitoring URLs

- **Producer Health**: http://localhost:8081/api/messages/health
- **Consumer Health**: http://localhost:8082/api/consumer/health
- **Consumer Stats**: http://localhost:8082/api/consumer/stats

## 🛑 Dừng Demo

```bash
# Dừng Spring Boot apps (Ctrl+C trong terminals)
# Dừng Kafka infrastructure
docker-compose down
```

## 🔍 Logs để theo dõi

**Producer logs:**
```
📤 Sending user message to topic 'user-events'
✅ User message sent successfully with offset: 123
```

**Consumer logs:**
```
📥 Received user message from topic 'user-events'
🔄 Processing user message: UserMessage{id=1...}
✅ User message processed and saved: 1
```

## ⚠️ Troubleshooting

**Lỗi connection refused:**
- Đảm bảo Docker containers đang chạy: `docker-compose ps`
- Chờ Kafka khởi động hoàn toàn (30-60 giây)

**Port đã được sử dụng:**
- Kiểm tra ports: `lsof -i :8080,8081,8082,9092`
- Dừng processes đang sử dụng ports

**Messages không được consume:**
- Kiểm tra Consumer logs
- Verify topic names trong Kafka UI
- Restart Consumer application
