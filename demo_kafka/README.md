# Kafka + Spring Boot 3 Demo

Demo hoàn chỉnh về Apache Kafka với Spring Boot 3, bao gồm Producer (Server) và Consumer (Client).

## 🏗️ Kiến trúc

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Producer      │    │     Kafka       │    │   Consumer      │
│  (Port 8081)    │───▶│   + Zookeeper   │───▶│  (Port 8082)    │
│                 │    │   + Kafka UI    │    │                 │
│ - REST API      │    │  (Port 8080)    │    │ - Message       │
│ - Send Messages │    │                 │    │   Processing    │
│                 │    │                 │    │ - Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Cấu trúc dự án

```
demo_kafka/
├── docker-compose.yml              # Kaf<PERSON>, Zookeeper, Kafka UI
├── kafka-producer/                 # Producer Application (Server)
│   ├── src/main/java/com/example/kafka/
│   │   ├── KafkaProducerApplication.java
│   │   ├── config/KafkaProducerConfig.java
│   │   ├── controller/MessageController.java
│   │   ├── producer/MessageProducer.java
│   │   └── model/
│   │       ├── UserMessage.java
│   │       └── NotificationMessage.java
│   └── src/main/resources/application.properties
└── kafka-consumer/                 # Consumer Application (Client)
    ├── src/main/java/com/example/kafka/
    │   ├── KafkaConsumerApplication.java
    │   ├── config/KafkaConsumerConfig.java
    │   ├── consumer/MessageConsumer.java
    │   ├── controller/ConsumerController.java
    │   ├── service/MessageProcessingService.java
    │   └── model/
    │       ├── UserMessage.java (with JPA)
    │       └── NotificationMessage.java (with JPA)
    └── src/main/resources/application.properties
```

## 🚀 Chạy Demo

### 1. Khởi động Kafka Infrastructure

```bash
cd demo_kafka
docker-compose up -d
```

Điều này sẽ khởi động:
- **Zookeeper** (port 2181)
- **Kafka** (port 9092)
- **Kafka UI** (port 8080) - http://localhost:8080

### 2. Khởi động Consumer (Client)

```bash
cd kafka-consumer
mvn spring-boot:run
```

Consumer sẽ chạy trên port **8082**

### 3. Khởi động Producer (Server)

```bash
cd kafka-producer
mvn spring-boot:run
```

Producer sẽ chạy trên port **8081**

## 📡 API Endpoints

### Producer (Port 8081)

#### Gửi User Message
```bash
curl -X POST http://localhost:8081/api/messages/user \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "action": "CREATE"
  }'
```

#### Gửi Notification Message
```bash
curl -X POST http://localhost:8081/api/messages/notification \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Welcome to our platform!",
    "type": "INFO",
    "recipient": "<EMAIL>"
  }'
```

#### Gửi System Log
```bash
curl -X POST "http://localhost:8081/api/messages/log?level=INFO&message=Application started successfully"
```

#### Gửi Custom Message
```bash
curl -X POST "http://localhost:8081/api/messages/custom?topic=my-topic&key=my-key" \
  -H "Content-Type: application/json" \
  -d '{"customField": "customValue"}'
```

### Consumer (Port 8082)

#### Xem Processed User Messages
```bash
curl http://localhost:8082/api/consumer/users
```

#### Xem Processed Notification Messages
```bash
curl http://localhost:8082/api/consumer/notifications
```

#### Xem Consumer Statistics
```bash
curl http://localhost:8082/api/consumer/stats
```

## 🔧 Topics được sử dụng

1. **user-events** - User lifecycle messages (CREATE, UPDATE, DELETE)
2. **notifications** - Notification messages (INFO, WARNING, ERROR)
3. **system-logs** - System log messages

## 💾 Database (Consumer)

Consumer sử dụng H2 in-memory database để lưu processed messages:
- **URL**: http://localhost:8082/h2-console
- **JDBC URL**: `jdbc:h2:mem:consumerdb`
- **Username**: `sa`
- **Password**: (empty)

## 🎯 Tính năng Demo

### Producer Features:
- ✅ REST API để gửi messages
- ✅ JSON serialization
- ✅ Async message sending với callbacks
- ✅ Multiple topic support
- ✅ Message validation
- ✅ Health check endpoints

### Consumer Features:
- ✅ Multiple message type consumption
- ✅ JSON deserialization
- ✅ Message processing với business logic
- ✅ Database persistence (JPA/Hibernate)
- ✅ Idempotency handling
- ✅ Error handling và logging
- ✅ REST API để xem processed messages

### Infrastructure Features:
- ✅ Docker Compose setup
- ✅ Kafka UI for monitoring
- ✅ Configurable topics
- ✅ Consumer groups
- ✅ Offset management

## 🧪 Testing Flow

1. **Khởi động tất cả services**
2. **Gửi messages qua Producer API**
3. **Xem logs của Consumer** để thấy message processing
4. **Check Kafka UI** (http://localhost:8080) để monitor topics
5. **Query Consumer API** để xem processed messages
6. **Check H2 Console** để xem data trong database

## 📊 Monitoring

- **Kafka UI**: http://localhost:8080
- **Producer Health**: http://localhost:8081/api/messages/health
- **Consumer Health**: http://localhost:8082/api/consumer/health
- **Consumer Stats**: http://localhost:8082/api/consumer/stats
- **H2 Console**: http://localhost:8082/h2-console

## 🔍 Logs để theo dõi

Producer logs:
```
📤 Sending user message to topic 'user-events'
✅ User message sent successfully with offset: 123
```

Consumer logs:
```
📥 Received user message from topic 'user-events'
🔄 Processing user message: UserMessage{id=1, name='John Doe'...}
✅ User message processed and saved: 1
```

## 🛠️ Cấu hình

### Producer Configuration
- Bootstrap servers: `localhost:9092`
- Serializers: String (key), JSON (value)
- Acks: `all` (đảm bảo durability)
- Retries: 3

### Consumer Configuration
- Bootstrap servers: `localhost:9092`
- Group ID: `demo-consumer-group`
- Auto offset reset: `earliest`
- Deserializers: String (key), JSON (value)
- Auto commit: enabled

## 🚨 Troubleshooting

1. **Kafka connection issues**: Đảm bảo Docker containers đang chạy
2. **Port conflicts**: Kiểm tra ports 8080, 8081, 8082, 9092 không bị sử dụng
3. **Message serialization errors**: Kiểm tra JSON format và model classes
4. **Consumer không nhận messages**: Kiểm tra topic names và consumer group configuration
