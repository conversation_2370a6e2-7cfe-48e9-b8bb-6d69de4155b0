#!/bin/bash

echo "🔍 Checking Kafka Status"
echo "======================="

# Check if Kafka container is running
echo "📦 Docker containers:"
docker-compose ps

echo ""
echo "📋 Kafka topics:"
# List topics using kafka container
docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list

echo ""
echo "📊 Topic details for user-events:"
docker exec kafka kafka-topics --bootstrap-server localhost:9092 --describe --topic user-events

echo ""
echo "📨 Recent messages in user-events topic:"
docker exec kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic user-events --from-beginning --max-messages 5 --timeout-ms 5000

echo ""
echo "🔧 Consumer groups:"
docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --list

echo ""
echo "📈 Consumer group details:"
docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group demo-consumer-group
