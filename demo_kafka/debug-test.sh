#!/bin/bash

echo "🐛 Debug Kafka Consumer"
echo "======================"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}1. Current state before sending new message:${NC}"
echo "User messages in database:"
curl -s http://localhost:8082/api/consumer/users | jq '.' 2>/dev/null || echo "[]"

echo ""
echo "Consumer stats:"
curl -s http://localhost:8082/api/consumer/stats | jq '.' 2>/dev/null

echo ""
echo -e "${BLUE}2. Sending a new message with unique ID:${NC}"
unique_id=$(($(date +%s) % 10000))
echo "Using ID: $unique_id"

response=$(curl -s -X POST http://localhost:8081/api/messages/user \
  -H "Content-Type: application/json" \
  -d "{\"id\": $unique_id, \"name\": \"Debug User $unique_id\", \"email\": \"debug$<EMAIL>\", \"action\": \"CREATE\"}")

echo "Producer response: $response"

echo ""
echo -e "${BLUE}3. Waiting 10 seconds for processing...${NC}"
sleep 10

echo ""
echo -e "${BLUE}4. Checking if message was processed:${NC}"
echo "User messages in database:"
users=$(curl -s http://localhost:8082/api/consumer/users)
echo "$users" | jq '.' 2>/dev/null || echo "$users"

echo ""
echo "Looking for our message (ID: $unique_id):"
if echo "$users" | grep -q "$unique_id"; then
    echo -e "${GREEN}✅ Message found in database!${NC}"
else
    echo -e "${RED}❌ Message NOT found in database${NC}"
fi

echo ""
echo -e "${BLUE}5. Updated consumer stats:${NC}"
curl -s http://localhost:8082/api/consumer/stats | jq '.' 2>/dev/null

echo ""
echo -e "${BLUE}6. Checking Kafka offset again:${NC}"
docker exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --describe --group demo-consumer-group | grep user-events

echo ""
echo -e "${YELLOW}💡 If LAG is 0 but no messages in database, there might be a deserialization or processing error.${NC}"
echo -e "${YELLOW}💡 Check Consumer application logs for error messages.${NC}"
