# Kafka Architecture Overview

```mermaid
graph TB
    subgraph "Producer Application (Port 8081)"
        P[Producer Service]
        PC[Producer Config]
        REST[REST Controller]
    end
    
    subgraph "Kafka Cluster"
        Z[Zookeeper<br/>Port 2181]
        K[Kafka Broker<br/>Port 9092]
        T1[Topic: user-events]
        T2[Topic: notifications]
        T3[Topic: system-logs]
        
        Z -.-> K
        K --> T1
        K --> T2
        K --> T3
    end
    
    subgraph "Consumer Application (Port 8082)"
        C[Consumer Service]
        CC[Consumer Config]
        DB[(H2 Database)]
        API[REST API]
    end
    
    subgraph "Monitoring"
        UI[Kafka UI<br/>Port 8080]
    end
    
    REST --> P
    P --> PC
    PC --> K
    K --> CC
    CC --> C
    C --> DB
    C --> API
    UI --> K
    
    style P fill:#e1f5fe
    style C fill:#f3e5f5
    style K fill:#fff3e0
    style Z fill:#e8f5e8
```

## Thành phần chính:

### Producer Application (Port 8081)
- **REST Controller**: Nhận HTTP requests
- **Producer Service**: Xử lý business logic
- **Producer Config**: <PERSON><PERSON>u hình Kafka producer

### Kafka Cluster
- **Zookeeper (Port 2181)**: Quản lý metadata
- **Kafka Broker (Port 9092)**: Xử lý messages
- **Topics**: user-events, notifications, system-logs

### Consumer Application (Port 8082)
- **Consumer Service**: Nhận và xử lý messages
- **Consumer Config**: Cấu hình Kafka consumer
- **H2 Database**: Lưu trữ processed messages
- **REST API**: Xem processed data

### Monitoring
- **Kafka UI (Port 8080)**: Web interface để monitor Kafka
