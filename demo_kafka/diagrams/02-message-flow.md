# Kafka Message Flow Lifecycle

```mermaid
sequenceDiagram
    participant Client as REST Client
    participant Producer as Producer App
    participant <PERSON><PERSON><PERSON> as Kafka Broker
    participant Consumer as Consumer App
    participant DB as H2 Database
    
    Client->>Producer: POST /api/messages/user
    Producer->>Producer: Validate & Serialize to JSON
    Producer->>Kafka: Send to topic 'user-events'
    Kafka->>Kafka: Store in partition
    Kafka-->>Producer: Ack (offset)
    Producer-->>Client: Success response
    
    loop Consumer Polling
        Consumer->>Kafka: Poll for messages
        Kafka-->>Consumer: Return messages batch
        Consumer->>Consumer: Deserialize JSON to Object
        Consumer->>Consumer: Process business logic
        Consumer->>DB: Save to database
        Consumer->>Kafka: Commit offset (auto)
    end
```

## Chi tiết từng bước:

### 1. Client Request
- REST client gửi POST request đến Producer
- Payload: JSON với user data

### 2. Producer Processing
- Validate input data
- Serialize object thành JSON
- Gửi message đến Kafka topic

### 3. Kafka Storage
- Lưu message vào partition
- T<PERSON><PERSON> về acknowledgment với offset

### 4. Consumer Processing
- Poll messages từ Kafka
- Deserialize JSON thành object
- Xử lý business logic
- Lưu vào database
- Commit offset để đánh dấu đã xử lý

### 5. Response
- Producer trả về success response cho client
- Consumer tiếp tục polling cho messages mới
