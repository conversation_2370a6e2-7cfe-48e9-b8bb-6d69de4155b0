# Kafka Offset Management

```mermaid
graph LR
    subgraph "Topic: user-events (Partition 0)"
        M1[Message 1<br/>Offset: 0]
        M2[Message 2<br/>Offset: 1]
        M3[Message 3<br/>Offset: 2]
        M4[Message 4<br/>Offset: 3]
        M5[Message 5<br/>Offset: 4]
    end
    
    subgraph "Consumer Group: demo-consumer-group"
        C1[Consumer Instance]
        CO[Current Offset: 3]
        LO[Last Committed: 2]
    end
    
    subgraph "Kafka Broker"
        OS[Offset Storage<br/>__consumer_offsets]
    end
    
    M1 --> M2 --> M3 --> M4 --> M5
    C1 --> CO
    CO --> LO
    LO --> OS
    
    style M4 fill:#ffeb3b
    style CO fill:#4caf50
    style LO fill:#2196f3
```

## Offset Concepts:

### Current Offset (Màu xanh lá)
- Vị trí message tiếp theo sẽ đọc
- Trong ví dụ: offset 3 (Message 4)

### Last Committed Offset (Màu xanh dương)
- Vị trí đã xử lý xong và commit
- Trong ví dụ: offset 2 (Message 3)

### LAG Calculation
- LAG = Latest Offset - Committed Offset
- Trong ví dụ: LAG = 4 - 2 = 2 messages

### Offset Storage
- Kafka lưu offset trong topic `__consumer_offsets`
- Mỗi consumer group có offset riêng
- Auto-commit mỗi 1 giây (configurable)

### Recovery Behavior
- Nếu consumer restart: đọc từ committed offset
- Nếu `auto-offset-reset=earliest`: đọc từ đầu
- Nếu `auto-offset-reset=latest`: đọc từ cuối
