# Partition Distribution & Consumer Groups

```mermaid
graph TB
    subgraph "Topic: user-events"
        P0[Partition 0<br/>Messages: 0,3,6,9...]
        P1[Partition 1<br/>Messages: 1,4,7,10...]
        P2[Partition 2<br/>Messages: 2,5,8,11...]
    end
    
    subgraph "Consumer Group: demo-consumer-group"
        C1[Consumer 1<br/>Reads P0]
        C2[Consumer 2<br/>Reads P1]
        C3[Consumer 3<br/>Reads P2]
    end
    
    P0 --> C1
    P1 --> C2
    P2 --> C3
    
    subgraph "Message Routing"
        K1[Key: user-1<br/>→ Partition 0]
        K2[Key: user-2<br/>→ Partition 1]
        K3[Key: user-3<br/>→ Partition 2]
    end
    
    style P0 fill:#e3f2fd
    style P1 fill:#f3e5f5
    style P2 fill:#e8f5e8
```

## Partition Strategy:

### Message Key Routing
- **Key**: `userMessage.getId().toString()`
- **Hash Function**: `hash(key) % partition_count`
- **Guarantee**: Cùng key → cùng partition → order preserved

### Consumer Assignment
- **1 Consumer per Partition**: Optimal performance
- **More Consumers than Partitions**: Some consumers idle
- **Fewer Consumers than Partitions**: Some consumers handle multiple partitions

### Scaling Scenarios

#### Scale Up (Add Consumers)
```
Before: C1 → P0,P1,P2
After:  C1 → P0, C2 → P1, C3 → P2
```

#### Scale Down (Remove Consumers)
```
Before: C1 → P0, C2 → P1, C3 → P2
After:  C1 → P0,P1, C2 → P2
```

### Rebalancing
- Tự động khi consumer join/leave group
- Temporary pause trong quá trình rebalance
- Kafka coordinator quản lý assignment
