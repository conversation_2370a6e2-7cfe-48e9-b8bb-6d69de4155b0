# Producer Internals & Configuration

```mermaid
graph TB
    subgraph "Producer Application"
        API[REST API]
        PS[Producer Service]
        KT[KafkaTemplate]
        
        subgraph "Producer Internals"
            S[Serializer]
            P[Partitioner]
            B[Batch Buffer]
            C[Compressor]
        end
        
        subgraph "Network Layer"
            N1[Network Thread 1]
            N2[Network Thread 2]
        end
    end
    
    subgraph "Kafka Broker"
        L[Leader Partition]
        R1[Replica 1]
        R2[Replica 2]
    end
    
    API --> PS
    PS --> KT
    KT --> S
    S --> P
    P --> B
    B --> C
    C --> N1
    C --> N2
    N1 --> L
    N2 --> L
    L --> R1
    L --> R2
    
    style S fill:#ffeb3b
    style P fill:#4caf50
    style B fill:#2196f3
    style C fill:#ff9800
```

## Producer Configuration Deep Dive:

### 1. Serialization (Màu vàng)
```properties
spring.kafka.producer.key-serializer=StringSerializer
spring.kafka.producer.value-serializer=JsonSerializer
```
- **Key**: String → UTF-8 bytes
- **Value**: Object → JSON → UTF-8 bytes

### 2. Partitioning (Màu xanh lá)
```java
// Message key determines partition
kafkaTemplate.send(topic, userMessage.getId().toString(), userMessage);
```
- **Default Strategy**: `hash(key) % partition_count`
- **Custom Partitioner**: Implement `Partitioner` interface

### 3. Batching (Màu xanh dương)
```properties
spring.kafka.producer.batch-size=16384      # 16KB
spring.kafka.producer.linger-ms=1           # Wait 1ms
spring.kafka.producer.buffer-memory=33554432 # 32MB
```
- **Batch Size**: Gom messages đến 16KB
- **Linger Time**: Chờ 1ms để gom thêm messages
- **Buffer Memory**: Tổng memory cho batching

### 4. Compression (Màu cam)
```properties
spring.kafka.producer.compression-type=snappy
```
- **Options**: none, gzip, snappy, lz4, zstd
- **Trade-off**: CPU vs Network bandwidth

### 5. Reliability Settings
```properties
spring.kafka.producer.acks=all              # Wait for all replicas
spring.kafka.producer.retries=3             # Retry 3 times
spring.kafka.producer.enable-idempotence=true # Exactly-once
```

### 6. Performance Tuning

#### High Throughput
```properties
batch-size=65536
linger-ms=10
compression-type=snappy
acks=1
```

#### Low Latency
```properties
batch-size=1
linger-ms=0
acks=1
```

#### High Reliability
```properties
acks=all
retries=Integer.MAX_VALUE
enable-idempotence=true
```
