# Consumer Internals & Configuration

```mermaid
graph TB
    subgraph "Consumer Application"
        KL[KafkaListener]
        CC[Consumer Container]
        
        subgraph "Consumer Internals"
            P[Poller Thread]
            D[Deserializer]
            O[Offset Manager]
            H[Message Handler]
        end
        
        subgraph "Business Logic"
            S[Service Layer]
            DB[(Database)]
        end
    end
    
    subgraph "Kafka Broker"
        T[Topic Partitions]
        OS[Offset Storage]
    end
    
    KL --> CC
    CC --> P
    P --> T
    T --> D
    D --> H
    H --> S
    S --> DB
    H --> O
    O --> OS
    
    style P fill:#ffeb3b
    style D fill:#4caf50
    style O fill:#2196f3
    style H fill:#ff9800
```

## Consumer Configuration Deep Dive:

### 1. Polling (Màu vàng)
```properties
spring.kafka.consumer.fetch-min-size=1          # Min 1 byte
spring.kafka.consumer.fetch-max-wait=500ms      # Max wait 500ms
spring.kafka.consumer.max-poll-records=500      # Max 500 records/poll
```
- **Fetch Strategy**: Pull model, not push
- **Batch Processing**: Process multiple messages together

### 2. Deserialization (Màu xanh lá)
```properties
spring.kafka.consumer.key-deserializer=StringDeserializer
spring.kafka.consumer.value-deserializer=JsonDeserializer
spring.kafka.consumer.properties.spring.json.trusted.packages=*
```
- **Key**: UTF-8 bytes → String
- **Value**: JSON bytes → Java Object
- **Trusted Packages**: Security setting for deserialization

### 3. Offset Management (Màu xanh dương)
```properties
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=1000ms
spring.kafka.consumer.auto-offset-reset=earliest
```
- **Auto Commit**: Tự động commit offset mỗi 1 giây
- **Manual Commit**: `acknowledgment.acknowledge()`
- **Reset Strategy**: earliest/latest/none

### 4. Message Handling (Màu cam)
```java
@KafkaListener(
    topics = "user-events",
    groupId = "demo-consumer-group",
    containerFactory = "userMessageKafkaListenerContainerFactory"
)
public void consume(UserMessage message) {
    // Process message
}
```

### 5. Consumer Group Management
```properties
spring.kafka.consumer.group-id=demo-consumer-group
spring.kafka.consumer.session-timeout-ms=30000
spring.kafka.consumer.heartbeat-interval-ms=3000
```
- **Group Coordinator**: Quản lý group membership
- **Heartbeat**: Consumer gửi heartbeat để báo còn sống
- **Session Timeout**: Timeout để detect consumer failure

### 6. Error Handling Strategies

#### Retry with Backoff
```java
@RetryableTopic(
    attempts = "3",
    backoff = @Backoff(delay = 1000, multiplier = 2.0)
)
@KafkaListener(topics = "user-events")
public void consume(UserMessage message) {
    // Process with retry
}
```

#### Dead Letter Topic
```java
@DltHandler
public void handleDlt(UserMessage message) {
    // Handle failed messages
}
```

### 7. Performance Tuning

#### High Throughput
```properties
max-poll-records=1000
fetch-min-size=1048576  # 1MB
enable-auto-commit=true
```

#### Low Latency
```properties
max-poll-records=1
fetch-min-size=1
fetch-max-wait=1ms
```

#### Reliable Processing
```properties
enable-auto-commit=false
isolation-level=read_committed
```
