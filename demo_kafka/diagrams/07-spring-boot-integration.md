# Spring Boot Kafka Integration

```mermaid
graph TB
    subgraph "Spring Boot Auto-Configuration"
        AC[Auto Configuration]
        KAC[KafkaAutoConfiguration]
        KPAC[KafkaProducerAutoConfiguration]
        KCAC[KafkaConsumerAutoConfiguration]
    end
    
    subgraph "Configuration Classes"
        PC[ProducerConfig]
        CC[ConsumerConfig]
        KT[KafkaTemplate]
        LCF[ListenerContainerFactory]
    end
    
    subgraph "Application Beans"
        PS[Producer Service]
        CS[Consumer Service]
        KL[KafkaListener Methods]
    end
    
    subgraph "Kafka Infrastructure"
        KB[Kafka Broker]
        T1[Topic 1]
        T2[Topic 2]
    end
    
    AC --> KAC
    KAC --> KPAC
    KAC --> KCAC
    KPAC --> PC
    KCAC --> CC
    PC --> KT
    CC --> LCF
    KT --> PS
    LCF --> CS
    CS --> KL
    PS --> KB
    CS --> KB
    KB --> T1
    KB --> T2
    
    style AC fill:#e3f2fd
    style PC fill:#f3e5f5
    style CC fill:#e8f5e8
    style PS fill:#fff3e0
    style CS fill:#fce4ec
```

## Spring Boot Integration Components:

### 1. Auto-Configuration (Màu xanh nhạt)
```java
@EnableAutoConfiguration
public class KafkaProducerApplication {
    // Spring Boot tự động configure Kafka
}
```
- **KafkaAutoConfiguration**: Main auto-config class
- **Conditional Beans**: Chỉ tạo khi có Kafka dependencies
- **Property Binding**: Bind từ application.properties

### 2. Producer Configuration (Màu hồng nhạt)
```java
@Configuration
public class KafkaProducerConfig {
    
    @Bean
    public ProducerFactory<String, Object> producerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        return new DefaultKafkaProducerFactory<>(props);
    }
    
    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
```

### 3. Consumer Configuration (Màu xanh lá nhạt)
```java
@Configuration
@EnableKafka
public class KafkaConsumerConfig {
    
    @Bean
    public ConsumerFactory<String, UserMessage> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        return new DefaultKafkaConsumerFactory<>(props);
    }
    
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, UserMessage> 
           kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, UserMessage> factory = 
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        return factory;
    }
}
```

### 4. Producer Service (Màu cam nhạt)
```java
@Service
public class MessageProducer {
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public void sendUserMessage(UserMessage message) {
        kafkaTemplate.send("user-events", message.getId().toString(), message);
    }
}
```

### 5. Consumer Service (Màu tím nhạt)
```java
@Service
public class MessageConsumer {
    
    @KafkaListener(
        topics = "user-events",
        groupId = "demo-consumer-group"
    )
    public void consume(UserMessage message) {
        // Process message
    }
}
```

## Configuration Properties Mapping:

### Producer Properties
```properties
# application.properties
spring.kafka.producer.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.producer.acks=all
spring.kafka.producer.retries=3
```

### Consumer Properties
```properties
# application.properties
spring.kafka.consumer.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=demo-consumer-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
```

## Annotation-Driven Development:

### @EnableKafka
- Enables Kafka listener annotation processing
- Registers KafkaListenerAnnotationBeanPostProcessor

### @KafkaListener
- Method-level annotation for consuming messages
- Supports SpEL expressions for dynamic configuration
- Can handle multiple topics and partitions

### @Header
- Inject Kafka message headers
- Access metadata like topic, partition, offset

### @Payload
- Inject message payload (optional, default behavior)
