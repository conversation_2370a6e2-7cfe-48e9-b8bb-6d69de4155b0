# Kafka Error Handling Patterns

```mermaid
graph TB
    subgraph "Message Processing Flow"
        M[Incoming Message]
        P[Process Message]
        S[Success]
        E[Error Occurs]
    end
    
    subgraph "Error Handling Strategies"
        R[Retry Logic]
        DLT[Dead Letter Topic]
        L[Logging & Alerting]
        I[Ignore & Continue]
    end
    
    subgraph "Retry Mechanisms"
        IR[Immediate Retry]
        BR[Backoff Retry]
        ER[Exponential Retry]
    end
    
    subgraph "Recovery Actions"
        MR[Manual Recovery]
        AR[Automatic Recovery]
        C[Compensation]
    end
    
    M --> P
    P --> S
    P --> E
    E --> R
    E --> DLT
    E --> L
    E --> I
    R --> IR
    R --> BR
    R --> ER
    DLT --> MR
    DLT --> AR
    DLT --> C
    
    style E fill:#ffcdd2
    style R fill:#fff3e0
    style DLT fill:#e8f5e8
    style L fill:#e3f2fd
```

## Error Handling Strategies:

### 1. Retry Logic (Màu cam nhạt)

#### Simple Retry
```java
@KafkaListener(topics = "user-events")
public void consume(UserMessage message) {
    try {
        processMessage(message);
    } catch (Exception e) {
        // Log error and continue
        logger.error("Error processing message: {}", message.getId(), e);
    }
}
```

#### Retry with @Retryable
```java
@Retryable(
    value = {Exception.class},
    maxAttempts = 3,
    backoff = @Backoff(delay = 1000, multiplier = 2.0)
)
@KafkaListener(topics = "user-events")
public void consume(UserMessage message) {
    processMessage(message); // Will retry on exception
}

@Recover
public void recover(Exception ex, UserMessage message) {
    // Called after all retries failed
    logger.error("Failed to process after retries: {}", message.getId(), ex);
}
```

### 2. Dead Letter Topic (Màu xanh lá nhạt)

#### Configuration
```java
@RetryableTopic(
    attempts = "3",
    backoff = @Backoff(delay = 1000, multiplier = 2.0),
    dltStrategy = DltStrategy.FAIL_ON_ERROR,
    include = {Exception.class}
)
@KafkaListener(topics = "user-events")
public void consume(UserMessage message) {
    processMessage(message);
}

@DltHandler
public void handleDlt(UserMessage message, Exception exception) {
    // Handle messages that failed all retries
    logger.error("Message sent to DLT: {}", message.getId(), exception);
    // Could send alert, save to database, etc.
}
```

#### Manual DLT
```java
@KafkaListener(topics = "user-events")
public void consume(UserMessage message) {
    try {
        processMessage(message);
    } catch (Exception e) {
        // Send to DLT manually
        kafkaTemplate.send("user-events-dlt", message);
        logger.error("Sent to DLT: {}", message.getId(), e);
    }
}
```

### 3. Circuit Breaker Pattern

```java
@Component
public class MessageProcessor {
    
    @CircuitBreaker(name = "messageProcessor", fallbackMethod = "fallbackProcess")
    public void processMessage(UserMessage message) {
        // Process message
        externalService.call(message);
    }
    
    public void fallbackProcess(UserMessage message, Exception ex) {
        // Fallback when circuit is open
        logger.warn("Circuit breaker open, using fallback for: {}", message.getId());
        // Could save to local cache, queue for later, etc.
    }
}
```

### 4. Offset Management Strategies

#### Manual Commit
```java
@KafkaListener(topics = "user-events")
public void consume(
    UserMessage message,
    Acknowledgment acknowledgment) {
    
    try {
        processMessage(message);
        acknowledgment.acknowledge(); // Commit only on success
    } catch (Exception e) {
        // Don't commit, message will be reprocessed
        logger.error("Error processing, will retry: {}", message.getId(), e);
    }
}
```

#### Seek to Specific Offset
```java
@KafkaListener(topics = "user-events")
public void consume(
    UserMessage message,
    @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
    @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
    @Header(KafkaHeaders.OFFSET) long offset,
    Consumer<String, UserMessage> consumer) {
    
    try {
        processMessage(message);
    } catch (Exception e) {
        // Seek back to retry specific message
        TopicPartition topicPartition = new TopicPartition(topic, partition);
        consumer.seek(topicPartition, offset);
    }
}
```

## Error Handling Configuration:

### Container Error Handler
```java
@Bean
public ConcurrentKafkaListenerContainerFactory<String, UserMessage> 
       kafkaListenerContainerFactory() {
    
    ConcurrentKafkaListenerContainerFactory<String, UserMessage> factory = 
        new ConcurrentKafkaListenerContainerFactory<>();
    
    factory.setConsumerFactory(consumerFactory());
    
    // Set error handler
    factory.setCommonErrorHandler(new DefaultErrorHandler(
        new FixedBackOff(1000L, 3L) // 3 retries with 1 second delay
    ));
    
    return factory;
}
```

### Global Error Handler
```java
@Component
public class KafkaErrorHandler implements ConsumerAwareListenerErrorHandler {
    
    @Override
    public Object handleError(Message<?> message, ListenerExecutionFailedException exception,
                            Consumer<?, ?> consumer) {
        
        logger.error("Global error handler: {}", exception.getMessage(), exception);
        
        // Could implement custom logic:
        // - Send to DLT
        // - Send alert
        // - Save to database
        // - Seek to specific offset
        
        return null;
    }
}
```

## Monitoring & Alerting:

### Metrics Collection
```java
@Component
public class KafkaMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter errorCounter;
    
    public KafkaMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.errorCounter = Counter.builder("kafka.consumer.errors")
            .description("Number of consumer errors")
            .register(meterRegistry);
    }
    
    public void recordError(String topic, String error) {
        errorCounter.increment(
            Tags.of(
                Tag.of("topic", topic),
                Tag.of("error.type", error)
            )
        );
    }
}
```

### Health Checks
```java
@Component
public class KafkaHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // Check Kafka connectivity
            // Check consumer lag
            // Check error rates
            return Health.up()
                .withDetail("status", "Kafka is healthy")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```
