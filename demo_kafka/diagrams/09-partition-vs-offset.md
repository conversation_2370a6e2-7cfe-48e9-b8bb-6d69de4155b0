# Partition vs Offset Visual Guide

## 1. Concept Overview

```mermaid
graph TB
    subgraph "Topic: user-events"
        subgraph "Partition 0"
            P0M0[Message<br/>Offset: 0<br/>Key: user-1<br/>Data: John]
            P0M1[Message<br/>Offset: 1<br/>Key: user-4<br/>Data: Alice]
            P0M2[Message<br/>Offset: 2<br/>Key: user-7<br/>Data: Bob]
            P0M3[Message<br/>Offset: 3<br/>Key: user-10<br/>Data: Carol]
        end
        
        subgraph "Partition 1"
            P1M0[Message<br/>Offset: 0<br/>Key: user-2<br/>Data: David]
            P1M1[Message<br/>Offset: 1<br/>Key: user-5<br/>Data: Eve]
            P1M2[Message<br/>Offset: 2<br/>Key: user-8<br/>Data: Frank]
        end
        
        subgraph "Partition 2"
            P2M0[Message<br/>Offset: 0<br/>Key: user-3<br/>Data: Grace]
            P2M1[Message<br/>Offset: 1<br/>Key: user-6<br/>Data: Henry]
            P2M2[Message<br/>Offset: 2<br/>Key: user-9<br/>Data: Ivy]
        end
    end
    
    P0M0 --> P0M1 --> P0M2 --> P0M3
    P1M0 --> P1M1 --> P1M2
    P2M0 --> P2M1 --> P2M2
    
    style P0M0 fill:#e3f2fd
    style P1M0 fill:#f3e5f5
    style P2M0 fill:#e8f5e8
```

**Key Points:**
- **Partition**: Phân chia vật lý (0, 1, 2)
- **Offset**: Vị trí trong partition (0, 1, 2, 3...)
- **Message Address**: Topic + Partition + Offset

## 2. Message Routing Strategy

```mermaid
graph LR
    subgraph "Producer Side"
        M1[Message<br/>Key: user-1<br/>Hash: 1001]
        M2[Message<br/>Key: user-2<br/>Hash: 1002]
        M3[Message<br/>Key: user-3<br/>Hash: 1003]
        M4[Message<br/>Key: user-4<br/>Hash: 1004]
    end
    
    subgraph "Hash Calculation"
        H1[1001 % 3 = 2]
        H2[1002 % 3 = 0]
        H3[1003 % 3 = 1]
        H4[1004 % 3 = 2]
    end
    
    subgraph "Target Partitions"
        P0[Partition 0<br/>Messages: user-2]
        P1[Partition 1<br/>Messages: user-3]
        P2[Partition 2<br/>Messages: user-1, user-4]
    end
    
    M1 --> H1 --> P2
    M2 --> H2 --> P0
    M3 --> H3 --> P1
    M4 --> H4 --> P2
    
    style P0 fill:#e3f2fd
    style P1 fill:#f3e5f5
    style P2 fill:#e8f5e8
```

**Routing Rules:**
- Same key → Same partition → Guaranteed order
- Different keys → Distributed across partitions
- No key → Round-robin distribution

## 3. Consumer Offset Management

```mermaid
graph TB
    subgraph "Partition 0 Timeline"
        M0[Offset 0<br/>Processed ✓]
        M1[Offset 1<br/>Processed ✓]
        M2[Offset 2<br/>Processed ✓]
        M3[Offset 3<br/>Processing...]
        M4[Offset 4<br/>Waiting]
        M5[Offset 5<br/>Waiting]
    end
    
    subgraph "Consumer State"
        CO[Current Offset: 3<br/>Next to read: 3]
        LC[Last Committed: 2<br/>Safe restart point]
        LAG[LAG: 3<br/>Messages behind]
    end
    
    subgraph "Offset Storage"
        OS[__consumer_offsets<br/>Topic]
        CG[Consumer Group:<br/>demo-consumer-group]
    end
    
    M0 --> M1 --> M2 --> M3 --> M4 --> M5
    CO --> LC
    LC --> OS
    OS --> CG
    
    style M0 fill:#4caf50
    style M1 fill:#4caf50
    style M2 fill:#4caf50
    style M3 fill:#ffeb3b
    style M4 fill:#ffcdd2
    style M5 fill:#ffcdd2
```

**Offset States:**
- **Green**: Processed and committed
- **Yellow**: Currently processing
- **Red**: Waiting to be processed

## 4. Consumer Group Assignment

```mermaid
graph TB
    subgraph "Topic: user-events (3 partitions)"
        P0[Partition 0<br/>Latest Offset: 15]
        P1[Partition 1<br/>Latest Offset: 8]
        P2[Partition 2<br/>Latest Offset: 12]
    end
    
    subgraph "Consumer Group: demo-consumer-group"
        C1[Consumer 1<br/>Assigned: P0<br/>Current Offset: 15<br/>LAG: 0]
        C2[Consumer 2<br/>Assigned: P1<br/>Current Offset: 6<br/>LAG: 2]
        C3[Consumer 3<br/>Assigned: P2<br/>Current Offset: 12<br/>LAG: 0]
    end
    
    P0 --> C1
    P1 --> C2
    P2 --> C3
    
    style C1 fill:#4caf50
    style C2 fill:#ff9800
    style C3 fill:#4caf50
```

**Assignment Rules:**
- 1 partition = 1 consumer (within same group)
- Consumer 2 has LAG = 2 (behind by 2 messages)
- Rebalancing occurs when consumers join/leave

## 5. Scaling Scenarios

### Before Scaling (1 Consumer)
```mermaid
graph LR
    P0[Partition 0] --> C1[Consumer 1]
    P1[Partition 1] --> C1
    P2[Partition 2] --> C1
    
    style C1 fill:#ff9800
```

### After Scaling (3 Consumers)
```mermaid
graph LR
    P0[Partition 0] --> C1[Consumer 1]
    P1[Partition 1] --> C2[Consumer 2]
    P2[Partition 2] --> C3[Consumer 3]
    
    style C1 fill:#4caf50
    style C2 fill:#4caf50
    style C3 fill:#4caf50
```

### Over-scaling (5 Consumers)
```mermaid
graph LR
    P0[Partition 0] --> C1[Consumer 1]
    P1[Partition 1] --> C2[Consumer 2]
    P2[Partition 2] --> C3[Consumer 3]
    
    C4[Consumer 4<br/>IDLE]
    C5[Consumer 5<br/>IDLE]
    
    style C1 fill:#4caf50
    style C2 fill:#4caf50
    style C3 fill:#4caf50
    style C4 fill:#ffcdd2
    style C5 fill:#ffcdd2
```

## 6. Message Flow Timeline

```mermaid
sequenceDiagram
    participant Producer
    participant Kafka as Kafka Broker
    participant Consumer
    participant OffsetStore as __consumer_offsets
    
    Producer->>Kafka: Send message (key: "user-1")
    Kafka->>Kafka: Route to Partition 2
    Kafka->>Kafka: Assign Offset 5
    Kafka-->>Producer: Ack: Partition 2, Offset 5
    
    Consumer->>Kafka: Poll messages
    Kafka-->>Consumer: Batch: [P2:O5, P2:O6, P1:O3]
    Consumer->>Consumer: Process P2:O5
    Consumer->>Consumer: Process P2:O6  
    Consumer->>Consumer: Process P1:O3
    Consumer->>OffsetStore: Commit offsets
    
    Note over OffsetStore: P2: offset 7, P1: offset 4
```

## 7. Error Handling with Offsets

```mermaid
graph TB
    subgraph "Normal Flow"
        N1[Receive Offset 5] --> N2[Process Successfully] --> N3[Commit Offset 6]
    end
    
    subgraph "Error Flow"
        E1[Receive Offset 5] --> E2[Processing Error] --> E3[Don't Commit]
        E3 --> E4[Retry from Offset 5]
    end
    
    subgraph "Manual Commit Flow"
        M1[Receive Offset 5] --> M2[Process Successfully] --> M3[ack.acknowledge()]
        M3 --> M4[Commit Offset 6]
    end
    
    style N2 fill:#4caf50
    style E2 fill:#f44336
    style M3 fill:#2196f3
```

## 8. Monitoring Commands

### Check Topic Partitions:
```bash
docker exec kafka kafka-topics \
  --bootstrap-server localhost:9092 \
  --describe --topic user-events
```

### Check Consumer Offsets:
```bash
docker exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --describe --group demo-consumer-group
```

### Reset Consumer Offsets:
```bash
# Reset to earliest
docker exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --group demo-consumer-group \
  --reset-offsets --to-earliest \
  --topic user-events --execute

# Reset to specific offset
docker exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --group demo-consumer-group \
  --reset-offsets --to-offset 10 \
  --topic user-events:0 --execute
```

## Key Differences Summary

| Aspect | Partition | Offset |
|--------|-----------|--------|
| **Scope** | Topic-wide | Partition-specific |
| **Purpose** | Data distribution | Message ordering |
| **Count** | Fixed at topic creation | Incremental per message |
| **Routing** | Hash-based on key | Sequential assignment |
| **Consumer** | 1 per consumer in group | Tracked per partition |
| **Scaling** | Add partitions (new msgs only) | Automatic increment |
