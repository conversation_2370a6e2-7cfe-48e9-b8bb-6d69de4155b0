#!/bin/bash

# Export Kafka Diagrams to Images
# Requires: npm install -g @mermaid-js/mermaid-cli

echo "🖼️ Exporting Kafka Diagrams to Images"
echo "====================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Check if mermaid-cli is installed
if ! command -v mmdc &> /dev/null; then
    echo -e "${RED}❌ mermaid-cli not found${NC}"
    echo -e "${YELLOW}Install with: npm install -g @mermaid-js/mermaid-cli${NC}"
    exit 1
fi

# Create output directory
mkdir -p diagrams/images

echo -e "${BLUE}📁 Created output directory: diagrams/images${NC}"
echo ""

# Function to extract mermaid code and export
export_diagram() {
    local md_file=$1
    local output_name=$2
    local title=$3
    
    echo -e "${BLUE}Processing: $title${NC}"
    
    # Extract mermaid code between ```mermaid and ```
    sed -n '/```mermaid/,/```/p' "diagrams/$md_file" | sed '1d;$d' > "diagrams/temp_$output_name.mmd"
    
    if [ -s "diagrams/temp_$output_name.mmd" ]; then
        # Export to PNG
        mmdc -i "diagrams/temp_$output_name.mmd" -o "diagrams/images/$output_name.png" -w 1200 -H 800
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Exported: $output_name.png${NC}"
        else
            echo -e "${RED}❌ Failed to export: $output_name.png${NC}"
        fi
        
        # Export to SVG
        mmdc -i "diagrams/temp_$output_name.mmd" -o "diagrams/images/$output_name.svg" -w 1200 -H 800
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Exported: $output_name.svg${NC}"
        else
            echo -e "${RED}❌ Failed to export: $output_name.svg${NC}"
        fi
        
        # Clean up temp file
        rm "diagrams/temp_$output_name.mmd"
    else
        echo -e "${RED}❌ No mermaid code found in $md_file${NC}"
    fi
    
    echo ""
}

# Export all diagrams
export_diagram "01-kafka-architecture.md" "01-kafka-architecture" "Kafka Architecture Overview"
export_diagram "02-message-flow.md" "02-message-flow" "Message Flow Lifecycle"
export_diagram "03-offset-management.md" "03-offset-management" "Offset Management"
export_diagram "04-partition-distribution.md" "04-partition-distribution" "Partition Distribution"
export_diagram "05-producer-internals.md" "05-producer-internals" "Producer Internals"
export_diagram "06-consumer-internals.md" "06-consumer-internals" "Consumer Internals"
export_diagram "07-spring-boot-integration.md" "07-spring-boot-integration" "Spring Boot Integration"
export_diagram "08-error-handling-patterns.md" "08-error-handling-patterns" "Error Handling Patterns"
export_diagram "09-partition-vs-offset.md" "09-partition-vs-offset" "Partition vs Offset Comparison"

echo -e "${GREEN}🎉 Export completed!${NC}"
echo ""
echo -e "${YELLOW}📁 Images saved in: diagrams/images/${NC}"
echo -e "${YELLOW}📋 Available formats: PNG, SVG${NC}"
echo ""
echo -e "${BLUE}📊 Generated files:${NC}"
ls -la diagrams/images/ | grep -E '\.(png|svg)$'

echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo "- PNG files: Good for presentations, documents"
echo "- SVG files: Scalable, good for web, print"
echo "- Use in documentation, presentations, or share with team"
