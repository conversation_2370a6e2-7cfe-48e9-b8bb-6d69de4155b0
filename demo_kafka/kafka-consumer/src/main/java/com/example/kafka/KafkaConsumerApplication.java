package com.example.kafka;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Kafka Consumer Application
 * 
 * This application demonstrates how to consume messages from Kafka topics
 * using Spring Boot 3 and Spring Kafka
 */
@SpringBootApplication
@EnableKafka
public class KafkaConsumerApplication {

    private static final Logger logger = LoggerFactory.getLogger(KafkaConsumerApplication.class);

    public static void main(String[] args) {
        logger.info("🚀 Starting Kafka Consumer Application...");
        SpringApplication.run(KafkaConsumerApplication.class, args);
        logger.info("✅ Kafka Consumer Application started successfully!");
    }
}
