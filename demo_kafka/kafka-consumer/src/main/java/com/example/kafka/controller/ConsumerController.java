package com.example.kafka.controller;

import com.example.kafka.model.NotificationMessage;
import com.example.kafka.model.UserMessage;
import com.example.kafka.service.MessageProcessingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for Consumer to view processed messages
 */
@RestController
@RequestMapping("/api/consumer")
@CrossOrigin(origins = "*")
public class ConsumerController {

    private static final Logger logger = LoggerFactory.getLogger(ConsumerController.class);

    private final MessageProcessingService messageProcessingService;

    public ConsumerController(MessageProcessingService messageProcessingService) {
        this.messageProcessingService = messageProcessingService;
    }

    /**
     * Get all processed user messages
     * GET /api/consumer/users
     */
    @GetMapping("/users")
    public ResponseEntity<List<UserMessage>> getAllUserMessages() {
        logger.info("🌐 Fetching all processed user messages");
        
        try {
            List<UserMessage> userMessages = messageProcessingService.getAllUserMessages();
            logger.info("✅ Retrieved {} user messages", userMessages.size());
            return ResponseEntity.ok(userMessages);
        } catch (Exception e) {
            logger.error("❌ Error fetching user messages", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get all processed notification messages
     * GET /api/consumer/notifications
     */
    @GetMapping("/notifications")
    public ResponseEntity<List<NotificationMessage>> getAllNotificationMessages() {
        logger.info("🌐 Fetching all processed notification messages");
        
        try {
            List<NotificationMessage> notificationMessages = messageProcessingService.getAllNotificationMessages();
            logger.info("✅ Retrieved {} notification messages", notificationMessages.size());
            return ResponseEntity.ok(notificationMessages);
        } catch (Exception e) {
            logger.error("❌ Error fetching notification messages", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get consumer statistics
     * GET /api/consumer/stats
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getConsumerStats() {
        logger.info("🌐 Fetching consumer statistics");
        
        try {
            List<UserMessage> userMessages = messageProcessingService.getAllUserMessages();
            List<NotificationMessage> notificationMessages = messageProcessingService.getAllNotificationMessages();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUserMessages", userMessages.size());
            stats.put("totalNotificationMessages", notificationMessages.size());
            stats.put("totalProcessedMessages", userMessages.size() + notificationMessages.size());
            
            // Count by action for user messages
            Map<String, Long> userActionCounts = new HashMap<>();
            userMessages.forEach(msg -> {
                String action = msg.getAction();
                userActionCounts.put(action, userActionCounts.getOrDefault(action, 0L) + 1);
            });
            stats.put("userMessagesByAction", userActionCounts);
            
            // Count by type for notification messages
            Map<String, Long> notificationTypeCounts = new HashMap<>();
            notificationMessages.forEach(msg -> {
                String type = msg.getType();
                notificationTypeCounts.put(type, notificationTypeCounts.getOrDefault(type, 0L) + 1);
            });
            stats.put("notificationMessagesByType", notificationTypeCounts);
            
            stats.put("timestamp", System.currentTimeMillis());
            
            logger.info("✅ Consumer statistics retrieved successfully");
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("❌ Error fetching consumer statistics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint
     * GET /api/consumer/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Kafka Consumer");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
