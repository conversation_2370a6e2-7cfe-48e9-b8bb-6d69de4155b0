package com.example.kafka.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Notification message model for Kafka messaging and JPA persistence
 */
@Entity
@Table(name = "notification_messages")
public class NotificationMessage {

    @Id
    private String id;

    @Column(nullable = false, length = 1000)
    private String message;

    private String type; // INFO, WARNING, ERROR

    private String recipient;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "message_timestamp")
    private LocalDateTime timestamp;

    private String source;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    // Default constructor
    public NotificationMessage() {
        this.processedAt = LocalDateTime.now();
    }

    // Constructor with parameters
    public NotificationMessage(String message, String type, String recipient) {
        this();
        this.message = message;
        this.type = type;
        this.recipient = recipient;
        this.timestamp = LocalDateTime.now();
        this.source = "kafka-producer";
        this.id = generateId();
    }

    private String generateId() {
        return "notif_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public LocalDateTime getProcessedAt() {
        return processedAt;
    }

    public void setProcessedAt(LocalDateTime processedAt) {
        this.processedAt = processedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NotificationMessage that = (NotificationMessage) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "NotificationMessage{" +
                "id='" + id + '\'' +
                ", message='" + message + '\'' +
                ", type='" + type + '\'' +
                ", recipient='" + recipient + '\'' +
                ", timestamp=" + timestamp +
                ", source='" + source + '\'' +
                ", processedAt=" + processedAt +
                '}';
    }
}
