package com.example.kafka.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * User message model for Kafka messaging and JPA persistence
 */
@Entity
@Table(name = "user_messages")
public class UserMessage {

    @Id
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String email;

    private String action; // CREATE, UPDATE, DELETE

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "message_timestamp")
    private LocalDateTime timestamp;

    private String source;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    // Default constructor
    public UserMessage() {
        this.processedAt = LocalDateTime.now();
    }

    // Constructor with parameters
    public UserMessage(Long id, String name, String email, String action) {
        this();
        this.id = id;
        this.name = name;
        this.email = email;
        this.action = action;
        this.timestamp = LocalDateTime.now();
        this.source = "kafka-producer";
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public LocalDateTime getProcessedAt() {
        return processedAt;
    }

    public void setProcessedAt(LocalDateTime processedAt) {
        this.processedAt = processedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserMessage that = (UserMessage) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(email, that.email);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, email);
    }

    @Override
    public String toString() {
        return "UserMessage{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", action='" + action + '\'' +
                ", timestamp=" + timestamp +
                ", source='" + source + '\'' +
                ", processedAt=" + processedAt +
                '}';
    }
}
