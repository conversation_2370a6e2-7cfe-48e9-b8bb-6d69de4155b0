package com.example.kafka.service;

import com.example.kafka.model.NotificationMessage;
import com.example.kafka.model.UserMessage;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service to process consumed Kafka messages
 */
@Service
@Transactional
public class MessageProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(MessageProcessingService.class);

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Process user message and save to database
     */
    public void processUserMessage(UserMessage userMessage) {
        logger.info("🔄 Processing user message: {}", userMessage);
        
        try {
            // Set processed timestamp
            userMessage.setProcessedAt(LocalDateTime.now());
            
            // Check if message already exists (idempotency)
            UserMessage existingMessage = entityManager.find(UserMessage.class, userMessage.getId());
            if (existingMessage != null) {
                logger.warn("⚠️ User message with ID {} already processed, skipping", userMessage.getId());
                return;
            }
            
            // Save to database
            entityManager.persist(userMessage);
            entityManager.flush();
            
            // Simulate business logic based on action
            switch (userMessage.getAction().toUpperCase()) {
                case "CREATE":
                    handleUserCreation(userMessage);
                    break;
                case "UPDATE":
                    handleUserUpdate(userMessage);
                    break;
                case "DELETE":
                    handleUserDeletion(userMessage);
                    break;
                default:
                    logger.warn("⚠️ Unknown action: {}", userMessage.getAction());
            }
            
            logger.info("✅ User message processed and saved: {}", userMessage.getId());
        } catch (Exception e) {
            logger.error("❌ Error processing user message: {}", userMessage.getId(), e);
            throw e;
        }
    }

    /**
     * Process notification message and save to database
     */
    public void processNotificationMessage(NotificationMessage notificationMessage) {
        logger.info("🔄 Processing notification message: {}", notificationMessage);
        
        try {
            // Set processed timestamp
            notificationMessage.setProcessedAt(LocalDateTime.now());
            
            // Check if message already exists (idempotency)
            NotificationMessage existingMessage = entityManager.find(NotificationMessage.class, notificationMessage.getId());
            if (existingMessage != null) {
                logger.warn("⚠️ Notification message with ID {} already processed, skipping", notificationMessage.getId());
                return;
            }
            
            // Save to database
            entityManager.persist(notificationMessage);
            entityManager.flush();
            
            // Simulate notification processing based on type
            switch (notificationMessage.getType().toUpperCase()) {
                case "INFO":
                    handleInfoNotification(notificationMessage);
                    break;
                case "WARNING":
                    handleWarningNotification(notificationMessage);
                    break;
                case "ERROR":
                    handleErrorNotification(notificationMessage);
                    break;
                default:
                    logger.warn("⚠️ Unknown notification type: {}", notificationMessage.getType());
            }
            
            logger.info("✅ Notification message processed and saved: {}", notificationMessage.getId());
        } catch (Exception e) {
            logger.error("❌ Error processing notification message: {}", notificationMessage.getId(), e);
            throw e;
        }
    }

    /**
     * Process system log message
     */
    public void processSystemLog(String level, String logMessage) {
        logger.info("🔄 Processing system log [{}]: {}", level, logMessage);
        
        try {
            // In a real application, you might want to:
            // - Parse the log message
            // - Store in a logging system
            // - Trigger alerts based on log level
            // - Aggregate metrics
            
            switch (level.toUpperCase()) {
                case "ERROR":
                    logger.error("🚨 System Error Log: {}", logMessage);
                    // Could trigger alerts here
                    break;
                case "WARN":
                    logger.warn("⚠️ System Warning Log: {}", logMessage);
                    break;
                case "INFO":
                    logger.info("ℹ️ System Info Log: {}", logMessage);
                    break;
                default:
                    logger.debug("🔍 System Debug Log: {}", logMessage);
            }
            
            logger.info("✅ System log processed successfully");
        } catch (Exception e) {
            logger.error("❌ Error processing system log", e);
            throw e;
        }
    }

    /**
     * Get all processed user messages
     */
    @Transactional(readOnly = true)
    public List<UserMessage> getAllUserMessages() {
        return entityManager.createQuery("SELECT u FROM UserMessage u ORDER BY u.processedAt DESC", UserMessage.class)
                .getResultList();
    }

    /**
     * Get all processed notification messages
     */
    @Transactional(readOnly = true)
    public List<NotificationMessage> getAllNotificationMessages() {
        return entityManager.createQuery("SELECT n FROM NotificationMessage n ORDER BY n.processedAt DESC", NotificationMessage.class)
                .getResultList();
    }

    // Private helper methods for business logic simulation
    private void handleUserCreation(UserMessage userMessage) {
        logger.info("👤 Handling user creation: {}", userMessage.getName());
        // Simulate user creation logic
        // - Send welcome email
        // - Create user profile
        // - Set up default preferences
    }

    private void handleUserUpdate(UserMessage userMessage) {
        logger.info("✏️ Handling user update: {}", userMessage.getName());
        // Simulate user update logic
        // - Update user profile
        // - Notify related services
        // - Update search indexes
    }

    private void handleUserDeletion(UserMessage userMessage) {
        logger.info("🗑️ Handling user deletion: {}", userMessage.getName());
        // Simulate user deletion logic
        // - Archive user data
        // - Clean up related resources
        // - Send confirmation email
    }

    private void handleInfoNotification(NotificationMessage notification) {
        logger.info("📢 Handling info notification: {}", notification.getMessage());
        // Send info notification to user
    }

    private void handleWarningNotification(NotificationMessage notification) {
        logger.warn("⚠️ Handling warning notification: {}", notification.getMessage());
        // Send warning notification with higher priority
    }

    private void handleErrorNotification(NotificationMessage notification) {
        logger.error("🚨 Handling error notification: {}", notification.getMessage());
        // Send urgent error notification
        // Might trigger alerts or escalations
    }
}
