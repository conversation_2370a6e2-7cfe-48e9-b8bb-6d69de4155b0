package com.example.kafka;

import com.example.kafka.service.MessageProcessingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Kafka Consumer Application
 */
@SpringBootTest
@AutoConfigureWebMvc
class KafkaConsumerApplicationTests {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private MessageProcessingService messageProcessingService;

    @Test
    void contextLoads() {
        // Test that the application context loads successfully
    }

    @Test
    void testHealthEndpoint() throws Exception {
        mockMvc.perform(get("/api/consumer/health"))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.status").value("UP"))
                .andExpected(jsonPath("$.service").value("Kafka Consumer"));
    }

    @Test
    void testGetUserMessages() throws Exception {
        mockMvc.perform(get("/api/consumer/users"))
                .andExpected(status().isOk())
                .andExpected(content().contentType("application/json"));
    }

    @Test
    void testGetNotificationMessages() throws Exception {
        mockMvc.perform(get("/api/consumer/notifications"))
                .andExpected(status().isOk())
                .andExpected(content().contentType("application/json"));
    }

    @Test
    void testGetConsumerStats() throws Exception {
        mockMvc.perform(get("/api/consumer/stats"))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.totalUserMessages").exists())
                .andExpected(jsonPath("$.totalNotificationMessages").exists());
    }
}
