/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/consumer/MessageConsumer.java
/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/model/UserMessage.java
/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/KafkaConsumerApplication.java
/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/controller/ConsumerController.java
/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/service/MessageProcessingService.java
/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/config/KafkaConsumerConfig.java
/Users/<USER>/Documents/demo/demo_kafka/kafka-consumer/src/main/java/com/example/kafka/model/NotificationMessage.java
