package com.example.kafka;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Kafka Producer Application
 * 
 * This application demonstrates how to send messages to Kafka topics
 * using Spring Boot 3 and Spring Kafka
 */
@SpringBootApplication
@EnableKafka
public class KafkaProducerApplication {

    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerApplication.class);

    public static void main(String[] args) {
        logger.info("🚀 Starting Kafka Producer Application...");
        SpringApplication.run(KafkaProducerApplication.class, args);
        logger.info("✅ Kafka Producer Application started successfully!");
    }
}
