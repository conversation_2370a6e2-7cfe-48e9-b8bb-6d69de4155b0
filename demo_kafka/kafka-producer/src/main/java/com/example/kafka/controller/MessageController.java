package com.example.kafka.controller;

import com.example.kafka.model.NotificationMessage;
import com.example.kafka.model.UserMessage;
import com.example.kafka.producer.MessageProducer;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * REST Controller for sending messages to Kafka
 */
@RestController
@RequestMapping("/api/messages")
@CrossOrigin(origins = "*")
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);

    private final MessageProducer messageProducer;

    public MessageController(MessageProducer messageProducer) {
        this.messageProducer = messageProducer;
    }

    /**
     * Send user message
     * POST /api/messages/user
     */
    @PostMapping("/user")
    public ResponseEntity<Map<String, Object>> sendUserMessage(@Valid @RequestBody UserMessage userMessage) {
        logger.info("🌐 Received request to send user message: {}", userMessage);
        
        try {
            messageProducer.sendUserMessage(userMessage);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "User message sent successfully");
            response.put("userId", userMessage.getId());
            response.put("timestamp", userMessage.getTimestamp());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("❌ Error sending user message", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send user message: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Send notification message
     * POST /api/messages/notification
     */
    @PostMapping("/notification")
    public ResponseEntity<Map<String, Object>> sendNotificationMessage(@Valid @RequestBody NotificationMessage notificationMessage) {
        logger.info("🌐 Received request to send notification message: {}", notificationMessage);
        
        try {
            messageProducer.sendNotificationMessage(notificationMessage);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Notification message sent successfully");
            response.put("notificationId", notificationMessage.getId());
            response.put("timestamp", notificationMessage.getTimestamp());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("❌ Error sending notification message", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send notification message: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Send system log
     * POST /api/messages/log
     */
    @PostMapping("/log")
    public ResponseEntity<Map<String, Object>> sendSystemLog(
            @RequestParam String level,
            @RequestParam String message) {
        logger.info("🌐 Received request to send system log: {} - {}", level, message);
        
        try {
            messageProducer.sendSystemLog(level, message);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "System log sent successfully");
            response.put("level", level);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("❌ Error sending system log", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send system log: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Send custom message to any topic
     * POST /api/messages/custom
     */
    @PostMapping("/custom")
    public ResponseEntity<Map<String, Object>> sendCustomMessage(
            @RequestParam String topic,
            @RequestParam String key,
            @RequestBody Object message) {
        logger.info("🌐 Received request to send custom message to topic '{}' with key '{}': {}", topic, key, message);
        
        try {
            messageProducer.sendToTopic(topic, key, message);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Custom message sent successfully");
            response.put("topic", topic);
            response.put("key", key);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("❌ Error sending custom message", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to send custom message: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Health check endpoint
     * GET /api/messages/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Kafka Producer");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
