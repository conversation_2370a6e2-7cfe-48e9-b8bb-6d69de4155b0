package com.example.kafka.producer;

import com.example.kafka.model.NotificationMessage;
import com.example.kafka.model.UserMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * Kafka Message Producer Service
 */
@Service
public class MessageProducer {

    private static final Logger logger = LoggerFactory.getLogger(MessageProducer.class);

    private final KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${kafka.topic.user-events}")
    private String userEventsTopic;

    @Value("${kafka.topic.notifications}")
    private String notificationsTopic;

    @Value("${kafka.topic.system-logs}")
    private String systemLogsTopic;

    public MessageProducer(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    /**
     * Send user message to user-events topic
     */
    public void sendUserMessage(UserMessage userMessage) {
        logger.info("📤 Sending user message to topic '{}': {}", userEventsTopic, userMessage);
        
        CompletableFuture<SendResult<String, Object>> future = 
            kafkaTemplate.send(userEventsTopic, userMessage.getId().toString(), userMessage);
        
        future.whenComplete((result, exception) -> {
            if (exception == null) {
                logger.info("✅ User message sent successfully: {} with offset: {}", 
                           userMessage.getId(), result.getRecordMetadata().offset());
            } else {
                logger.error("❌ Failed to send user message: {}", userMessage.getId(), exception);
            }
        });
    }

    /**
     * Send notification message to notifications topic
     */
    public void sendNotificationMessage(NotificationMessage notificationMessage) {
        logger.info("📤 Sending notification message to topic '{}': {}", notificationsTopic, notificationMessage);
        
        CompletableFuture<SendResult<String, Object>> future = 
            kafkaTemplate.send(notificationsTopic, notificationMessage.getId(), notificationMessage);
        
        future.whenComplete((result, exception) -> {
            if (exception == null) {
                logger.info("✅ Notification message sent successfully: {} with offset: {}", 
                           notificationMessage.getId(), result.getRecordMetadata().offset());
            } else {
                logger.error("❌ Failed to send notification message: {}", notificationMessage.getId(), exception);
            }
        });
    }

    /**
     * Send system log message
     */
    public void sendSystemLog(String logLevel, String message) {
        String logMessage = String.format("[%s] %s - %s", logLevel, System.currentTimeMillis(), message);
        logger.info("📤 Sending system log to topic '{}': {}", systemLogsTopic, logMessage);
        
        CompletableFuture<SendResult<String, Object>> future = 
            kafkaTemplate.send(systemLogsTopic, logLevel, logMessage);
        
        future.whenComplete((result, exception) -> {
            if (exception == null) {
                logger.info("✅ System log sent successfully with offset: {}", result.getRecordMetadata().offset());
            } else {
                logger.error("❌ Failed to send system log", exception);
            }
        });
    }

    /**
     * Send message to custom topic
     */
    public void sendToTopic(String topic, String key, Object message) {
        logger.info("📤 Sending message to custom topic '{}' with key '{}': {}", topic, key, message);
        
        CompletableFuture<SendResult<String, Object>> future = kafkaTemplate.send(topic, key, message);
        
        future.whenComplete((result, exception) -> {
            if (exception == null) {
                logger.info("✅ Message sent successfully to topic '{}' with offset: {}", 
                           topic, result.getRecordMetadata().offset());
            } else {
                logger.error("❌ Failed to send message to topic '{}'", topic, exception);
            }
        });
    }
}
