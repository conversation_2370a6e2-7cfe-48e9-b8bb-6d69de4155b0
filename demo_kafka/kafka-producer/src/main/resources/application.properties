# Kafka Producer Configuration
spring.application.name=kafka-producer
server.port=8081

# Kafka Bootstrap Servers
spring.kafka.bootstrap-servers=localhost:9092

# Producer Configuration
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.producer.acks=all
spring.kafka.producer.retries=3
spring.kafka.producer.batch-size=16384
spring.kafka.producer.linger-ms=1
spring.kafka.producer.buffer-memory=33554432

# JSON Serializer Configuration
spring.kafka.producer.properties.spring.json.add.type.headers=false
spring.kafka.producer.properties.spring.json.trusted.packages=com.example.kafka.model

# Topic Configuration
kafka.topic.user-events=user-events
kafka.topic.notifications=notifications
kafka.topic.system-logs=system-logs

# Logging Configuration
logging.level.com.example.kafka=INFO
logging.level.org.springframework.kafka=INFO
logging.level.org.apache.kafka=WARN

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,kafka
management.endpoint.health.show-details=always

# Application Info
info.app.name=Kafka Producer Demo
info.app.description=Spring Boot 3 Kafka Producer Demo Application
info.app.version=1.0.0
