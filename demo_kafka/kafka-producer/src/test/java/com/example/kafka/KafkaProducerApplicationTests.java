package com.example.kafka;

import com.example.kafka.model.UserMessage;
import com.example.kafka.producer.MessageProducer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Kafka Producer Application
 */
@SpringBootTest
@AutoConfigureWebMvc
class KafkaProducerApplicationTests {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MessageProducer messageProducer;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void contextLoads() {
        // Test that the application context loads successfully
    }

    @Test
    void testHealthEndpoint() throws Exception {
        mockMvc.perform(get("/api/messages/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.service").value("Kafka Producer"));
    }

    @Test
    void testSendUserMessage() throws Exception {
        UserMessage userMessage = new UserMessage(1L, "Test User", "<EMAIL>", "CREATE");
        
        mockMvc.perform(post("/api/messages/user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(userMessage)))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.status").value("success"));
    }

    @Test
    void testSendSystemLog() throws Exception {
        mockMvc.perform(post("/api/messages/log")
                .param("level", "INFO")
                .param("message", "Test log message"))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.status").value("success"));
    }
}
