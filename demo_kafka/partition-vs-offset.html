<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kafka: Partition vs Offset</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .comparison-table tr:hover {
            background-color: #e3f2fd;
        }
        .highlight-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 20px;
            border-left: 5px solid #3498db;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: #fafafa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .column {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .column h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: transform 0.2s;
        }
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        @media print {
            .print-btn { display: none; }
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ Print PDF</button>
    
    <div class="container">
        <h1>🔍 Kafka: Partition vs Offset</h1>
        
        <div class="highlight-box">
            <strong>🎯 Mục tiêu:</strong> Hiểu rõ sự khác biệt giữa Partition và Offset - hai khái niệm cốt lõi trong Apache Kafka.
        </div>

        <h2>📊 So sánh tổng quan</h2>
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Khía cạnh</th>
                    <th>🏗️ Partition</th>
                    <th>📍 Offset</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Định nghĩa</strong></td>
                    <td>Phân chia vật lý của Topic</td>
                    <td>Vị trí duy nhất của message trong partition</td>
                </tr>
                <tr>
                    <td><strong>Phạm vi</strong></td>
                    <td>Topic level</td>
                    <td>Partition level</td>
                </tr>
                <tr>
                    <td><strong>Mục đích</strong></td>
                    <td>Phân tán và song song hóa</td>
                    <td>Định danh và thứ tự message</td>
                </tr>
                <tr>
                    <td><strong>Tính chất</strong></td>
                    <td>Static (số lượng cố định)</td>
                    <td>Dynamic (tăng dần)</td>
                </tr>
                <tr>
                    <td><strong>Bắt đầu từ</strong></td>
                    <td>0, 1, 2... (partition ID)</td>
                    <td>0 (cho mỗi partition)</td>
                </tr>
                <tr>
                    <td><strong>Quản lý bởi</strong></td>
                    <td>Kafka Admin/Producer</td>
                    <td>Kafka Broker tự động</td>
                </tr>
            </tbody>
        </table>

        <h2>🏗️ PARTITION - Phân chia vật lý</h2>
        
        <div class="two-column">
            <div class="column">
                <h3>📋 Đặc điểm chính</h3>
                <ul>
                    <li><strong>Phân chia Topic</strong> thành các phần nhỏ</li>
                    <li><strong>Số lượng cố định</strong> khi tạo topic</li>
                    <li><strong>Phân tán</strong> trên nhiều brokers</li>
                    <li><strong>Song song hóa</strong> processing</li>
                    <li><strong>Load balancing</strong> tự động</li>
                </ul>
            </div>
            <div class="column">
                <h3>🔑 Message Routing</h3>
                <div class="code-block">
hash(message_key) % partition_count
= target_partition

Example:
hash("user-123") % 3 = 2
→ Message goes to Partition 2
                </div>
            </div>
        </div>

        <div class="mermaid">
graph LR
    subgraph "Message Keys"
        K1[user-1<br/>hash: 1001]
        K2[user-2<br/>hash: 1002]
        K3[user-3<br/>hash: 1003]
    end
    
    subgraph "Hash Calculation"
        H1[1001 % 3 = 2]
        H2[1002 % 3 = 0]
        H3[1003 % 3 = 1]
    end
    
    subgraph "Target Partitions"
        P0[Partition 0<br/>user-2]
        P1[Partition 1<br/>user-3]
        P2[Partition 2<br/>user-1]
    end
    
    K1 --> H1 --> P2
    K2 --> H2 --> P0
    K3 --> H3 --> P1
    
    style P0 fill:#e3f2fd
    style P1 fill:#f3e5f5
    style P2 fill:#e8f5e8
        </div>

        <h2>📍 OFFSET - Vị trí message</h2>
        
        <div class="two-column">
            <div class="column">
                <h3>📋 Đặc điểm chính</h3>
                <ul>
                    <li><strong>ID duy nhất</strong> cho mỗi message</li>
                    <li><strong>Tăng dần</strong> theo thứ tự (0, 1, 2...)</li>
                    <li><strong>Immutable</strong> - không thay đổi</li>
                    <li><strong>Per-partition</strong> - mỗi partition riêng</li>
                    <li><strong>Consumer tracking</strong> progress</li>
                </ul>
            </div>
            <div class="column">
                <h3>🔄 Offset States</h3>
                <div class="code-block">
Current Offset: 15
→ Next message to read

Committed Offset: 12  
→ Last processed message

LAG: 15 - 12 = 3
→ Messages behind
                </div>
            </div>
        </div>

        <div class="mermaid">
graph LR
    subgraph "Partition 0 Timeline"
        M0[Offset 0<br/>✅ Processed]
        M1[Offset 1<br/>✅ Processed]
        M2[Offset 2<br/>✅ Processed]
        M3[Offset 3<br/>🔄 Processing]
        M4[Offset 4<br/>⏳ Waiting]
        M5[Offset 5<br/>⏳ Waiting]
    end
    
    subgraph "Consumer State"
        CO[Current: 3]
        LC[Committed: 2]
        LAG[LAG: 3]
    end
    
    M0 --> M1 --> M2 --> M3 --> M4 --> M5
    M3 --> CO
    M2 --> LC
    M5 --> LAG
    
    style M0 fill:#4caf50
    style M1 fill:#4caf50
    style M2 fill:#4caf50
    style M3 fill:#ffeb3b
    style M4 fill:#ffcdd2
    style M5 fill:#ffcdd2
        </div>

        <h2>🔄 Message Flow Example</h2>
        
        <div class="mermaid">
sequenceDiagram
    participant Producer
    participant Kafka as Kafka Broker
    participant Consumer
    participant OffsetStore as __consumer_offsets
    
    Producer->>Kafka: Send message (key: "user-123")
    Kafka->>Kafka: Route to Partition 2
    Kafka->>Kafka: Assign Offset 15
    Kafka-->>Producer: Ack: Partition 2, Offset 15
    
    Consumer->>Kafka: Poll messages
    Kafka-->>Consumer: Message: P2:O15
    Consumer->>Consumer: Process message
    Consumer->>OffsetStore: Commit offset 16 for P2
    
    Note over OffsetStore: Next poll starts from offset 16
        </div>

        <h2>⚙️ Configuration trong Demo</h2>
        
        <div class="two-column">
            <div class="column">
                <h3>📤 Producer Settings</h3>
                <div class="code-block">
# application.properties
spring.kafka.producer.key-serializer=
  StringSerializer
spring.kafka.producer.value-serializer=
  JsonSerializer

# Code
kafkaTemplate.send(topic, 
  userMessage.getId().toString(), 
  userMessage);
                </div>
            </div>
            <div class="column">
                <h3>📥 Consumer Settings</h3>
                <div class="code-block">
# application.properties
spring.kafka.consumer.group-id=
  demo-consumer-group
spring.kafka.consumer.auto-offset-reset=
  earliest
spring.kafka.consumer.enable-auto-commit=
  true
                </div>
            </div>
        </div>

        <h2>🎯 Practical Commands</h2>
        
        <div class="highlight-box">
            <h3>🔍 Kiểm tra Partitions:</h3>
            <div class="code-block">
docker exec kafka kafka-topics \
  --bootstrap-server localhost:9092 \
  --describe --topic user-events
            </div>
            
            <h3>📊 Kiểm tra Offsets:</h3>
            <div class="code-block">
docker exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --describe --group demo-consumer-group
            </div>
            
            <h3>📈 Output mẫu:</h3>
            <div class="code-block">
TOPIC       PARTITION  CURRENT-OFFSET  LOG-END-OFFSET  LAG
user-events 0          15              15              0
user-events 1          8               8               0  
user-events 2          12              12              0
            </div>
        </div>

        <h2>💡 Key Takeaways</h2>
        
        <div class="two-column">
            <div class="column">
                <h3>🏗️ PARTITION</h3>
                <ul>
                    <li><strong>Structural:</strong> Cách tổ chức dữ liệu</li>
                    <li><strong>Purpose:</strong> Scaling và parallelism</li>
                    <li><strong>Routing:</strong> Message key quyết định</li>
                    <li><strong>Consumers:</strong> 1 per partition trong group</li>
                </ul>
            </div>
            <div class="column">
                <h3>📍 OFFSET</h3>
                <ul>
                    <li><strong>Position:</strong> Vị trí message trong partition</li>
                    <li><strong>Sequential:</strong> Tăng dần theo thời gian</li>
                    <li><strong>Persistent:</strong> Lưu trong __consumer_offsets</li>
                    <li><strong>Resumable:</strong> Consumer resume từ last committed</li>
                </ul>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 Message Address Formula:</h3>
            <div class="code-block">
Message Address = Topic + Partition + Offset

Example: user-events + Partition 2 + Offset 15
→ Unique identifier for any message in Kafka
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">
            <strong>🎉 Hiểu rõ Partition và Offset là foundation để master Kafka architecture!</strong>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
