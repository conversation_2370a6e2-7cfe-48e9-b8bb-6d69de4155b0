#!/bin/bash

echo "🧪 Simple Kafka Test"
echo "==================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}1. Checking services...${NC}"

# Check Producer
if curl -s http://localhost:8081/api/messages/health > /dev/null; then
    echo -e "${GREEN}✅ Producer is running${NC}"
else
    echo -e "${RED}❌ Producer is not running${NC}"
    exit 1
fi

# Check Consumer
if curl -s http://localhost:8082/api/consumer/health > /dev/null; then
    echo -e "${GREEN}✅ Consumer is running${NC}"
else
    echo -e "${RED}❌ Consumer is not running${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}2. Sending a simple user message...${NC}"

response=$(curl -s -X POST http://localhost:8081/api/messages/user \
  -H "Content-Type: application/json" \
  -d '{"id": 123, "name": "Test User", "email": "<EMAIL>", "action": "CREATE"}')

echo "Response: $response"

if echo "$response" | grep -q "success"; then
    echo -e "${GREEN}✅ Message sent successfully${NC}"
else
    echo -e "${RED}❌ Failed to send message${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}3. Waiting for processing...${NC}"
sleep 5

echo ""
echo -e "${BLUE}4. Checking processed messages...${NC}"

users=$(curl -s http://localhost:8082/api/consumer/users)
echo "Processed users: $users"

count=$(echo "$users" | jq '. | length' 2>/dev/null || echo "0")
echo -e "${GREEN}📊 Total processed messages: $count${NC}"

echo ""
echo -e "${BLUE}5. Getting stats...${NC}"
stats=$(curl -s http://localhost:8082/api/consumer/stats)
echo "$stats" | jq '.' 2>/dev/null || echo "$stats"

echo ""
echo -e "${GREEN}🎉 Test completed!${NC}"
