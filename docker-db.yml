version: '3.8'

services:
  mysql:
    image: mysql:latest
    container_name: mysql_container
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: <PERSON><PERSON>@1
      MYSQL_DATABASE: vhd
      MYSQL_USER: vhd
      MYSQL_PASSWORD: <PERSON><PERSON>@1
    ports:  
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  # postgres:
  #   image: postgres:latest
  #   container_name: postgres_container  
  #   restart: always
  #   environment:
  #     POSTGRES_DB: vhd
  #     POSTGRES_USER: vhd
  #     POSTGRES_PASSWORD: <PERSON>han@1
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data

  # mongodb:
  #   image: mongo:latest
  #   container_name: mongodb_container
  #   restart: always
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: root
  #     MONGO_INITDB_ROOT_PASSWORD: <PERSON><PERSON>@1
  #     MONGO_INITDB_DATABASE: vhd
  #   ports:
  #     - "27017:27017"
  #   volumes:
  #     - mongo_data:/var/lib/mongo/data/db
      
  # keycloak:
  #   image: quay.io/keycloak/keycloak:latest
  #   container_name: keycloak_server
  #   restart: always
  #   environment:
  #     KEYCLOAK_ADMIN: admin
  #     KEYCLOAK_ADMIN_PASSWORD: admin
  #     KC_DB: postgres
  #     KC_DB_URL_HOST: postgres
  #     KC_DB_URL_DATABASE: microservices
  #     KC_DB_USERNAME: java
  #     KC_DB_PASSWORD: Shinhan@1
  #     KC_HOSTNAME: localhost
  #   command: ["start-dev"]
  #   ports:
  #     - "8180:8080"
  #   depends_on:
  #     - postgres

  redis:
    image: redis:latest
    container_name: redis_server
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    # command: redis-server --requirepass mysecretpassword # có mật khẩu 
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  # postgres_data:
  # mongo_data:
  redis_data:
# mongodb://localhost:27017/root