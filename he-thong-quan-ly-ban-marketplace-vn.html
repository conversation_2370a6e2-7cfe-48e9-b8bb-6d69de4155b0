<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Marketplace Coffee & Restaurant</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 4px solid #667eea;
            padding-bottom: 30px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: -40px -40px 40px -40px;
            padding: 40px;
            border-radius: 15px 15px 0 0;
        }
        .header h1 {
            font-size: 2.8em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header .subtitle {
            font-size: 1.3em;
            margin-top: 15px;
            opacity: 0.9;
        }
        .header .business-model {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 1.1em;
            font-weight: bold;
        }
        .section {
            margin-bottom: 50px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #667eea;
            border-left: 6px solid #667eea;
            padding-left: 20px;
            font-size: 2em;
            margin-bottom: 25px;
            background: linear-gradient(90deg, #f8f9ff, transparent);
            padding: 15px 20px;
            border-radius: 0 10px 10px 0;
        }
        .section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .section h4 {
            color: #667eea;
            font-size: 1.3em;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h4 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.2em;
        }
        .pricing-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .pricing-card h4 {
            color: white;
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        .pricing-card .price {
            font-size: 2.5em;
            font-weight: bold;
            margin: 15px 0;
        }
        .pricing-card .period {
            opacity: 0.8;
            font-size: 0.9em;
        }
        .workflow-container {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            position: relative;
        }
        .workflow-title {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            position: absolute;
            top: -15px;
            left: 30px;
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .step {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            position: relative;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
            transition: transform 0.3s ease;
        }
        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        .step-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 15px;
        }
        .step-title {
            color: #667eea;
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        .step-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.5;
        }
        .step-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .step-details ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        .step-details li {
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .decision-point {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border: 2px solid #e17055;
            color: #2d3436;
        }
        .decision-point .step-number {
            background: linear-gradient(135deg, #e17055, #d63031);
        }
        .success-step {
            background: linear-gradient(135deg, #a8e6cf, #88d8a3);
            border: 2px solid #00b894;
            color: #2d3436;
        }
        .success-step .step-number {
            background: linear-gradient(135deg, #00b894, #00a085);
        }
        .error-step {
            background: linear-gradient(135deg, #fab1a0, #e17055);
            border: 2px solid #d63031;
            color: #2d3436;
        }
        .error-step .step-number {
            background: linear-gradient(135deg, #d63031, #c0392b);
        }
        .system-interaction {
            background: linear-gradient(135deg, #e8f4fd, #c3e9ff);
            border: 2px solid #0984e3;
            color: #2d3436;
        }
        .system-interaction .step-number {
            background: linear-gradient(135deg, #0984e3, #0770c4);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        .comparison-table tr:hover {
            background-color: #f0f2ff;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
        }
        .highlight h4 {
            color: #856404;
            margin-top: 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .stat-number {
            font-size: 2.2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .timeline-flow {
            position: relative;
            padding-left: 40px;
        }
        .timeline-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        .timeline-step {
            position: relative;
            margin-bottom: 30px;
        }
        .timeline-step::before {
            content: '';
            position: absolute;
            left: -29px;
            top: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #667eea;
        }
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
            .section {
                page-break-inside: avoid;
            }
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header {
                margin: -20px -20px 30px -20px;
                padding: 30px 20px;
            }
            .workflow-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Hệ Thống Quản Lý Bàn Trống</h1>
            <div class="subtitle">Marketplace Kết Nối Cửa Hàng Coffee & Restaurant với Khách Hàng</div>
            <div class="business-model">
                💼 Mô Hình: Nền Tảng Trung Gian Thu Phí Dịch Vụ + Hoa Hồng Giao Dịch
            </div>
        </div>

        <div class="section">
            <h2>🎯 Tổng Quan Hệ Thống</h2>
            <div class="highlight">
                <h4><span style="font-size: 1.2em; margin-right: 8px;">🔄</span>Vai Trò Nền Tảng</h4>
                <p><strong>Marketplace B2B2C:</strong> Hệ thống trung gian kết nối các cửa hàng F&B với khách hàng, thu phí dịch vụ từ cửa hàng và hoa hồng từ giao dịch.</p>
                <p><strong>Giá trị cốt lõi:</strong> Cung cấp công nghệ, khách hàng và dịch vụ thanh toán cho cửa hàng mà không cần đầu tư phát triển.</p>
            </div>
            
            <h3>✨ Chức Năng Chính</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🪑 Quản Lý Bàn Trống</h4>
                    <p>Theo dõi trạng thái bàn real-time, đặt bàn trực tuyến, quản lý booking và check-in tự động</p>
                </div>
                <div class="feature-card">
                    <h4>🗺️ Tìm Kiếm & Chỉ Đường</h4>
                    <p>Tìm kiếm cửa hàng theo vị trí, filter đa dạng, chỉ đường GPS và navigation turn-by-turn</p>
                </div>
                <div class="feature-card">
                    <h4>💰 Hệ Thống Giá & Thuế</h4>
                    <p>Quản lý giá linh hoạt, tính thuế tự động, phụ thu theo thời gian và sự kiện đặc biệt</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Thanh Toán Đa Dạng</h4>
                    <p>Tích hợp multiple payment gateways, xử lý hoa hồng tự động và settlement hàng tuần</p>
                </div>
                <div class="feature-card">
                    <h4>🔐 Đăng Nhập Xã Hội</h4>
                    <p>OAuth integration với Google, Apple, Facebook cho trải nghiệm đăng nhập mượt mà</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Analytics & Báo Cáo</h4>
                    <p>Dashboard analytics cho cửa hàng và platform admin, insights kinh doanh chi tiết</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💰 Mô Hình Thu Phí</h2>
            
            <h3>📋 Gói Dịch Vụ Subscription</h3>
            <div class="feature-grid">
                <div class="pricing-card">
                    <h4>🥉 GÓI CƠ BẢN</h4>
                    <div class="price">$29<span class="period">/tháng</span></div>
                    <ul style="text-align: left; color: white; list-style: none; padding: 0;">
                        <li>✓ Tối đa 5 bàn</li>
                        <li>✓ Quản lý booking cơ bản</li>
                        <li>✓ Thanh toán tiêu chuẩn</li>
                        <li>✓ Hỗ trợ email</li>
                        <li>✓ Hoa hồng: 2.5%</li>
                    </ul>
                </div>
                <div class="pricing-card">
                    <h4>🥈 GÓI CHUYÊN NGHIỆP</h4>
                    <div class="price">$79<span class="period">/tháng</span></div>
                    <ul style="text-align: left; color: white; list-style: none; padding: 0;">
                        <li>✓ Tối đa 20 bàn</li>
                        <li>✓ Analytics nâng cao</li>
                        <li>✓ Tùy chỉnh thương hiệu</li>
                        <li>✓ Hỗ trợ ưu tiên</li>
                        <li>✓ Hoa hồng: 2.0%</li>
                    </ul>
                </div>
                <div class="pricing-card">
                    <h4>🥇 GÓI DOANH NGHIỆP</h4>
                    <div class="price">$199<span class="period">/tháng</span></div>
                    <ul style="text-align: left; color: white; list-style: none; padding: 0;">
                        <li>✓ Không giới hạn bàn</li>
                        <li>✓ Hỗ trợ đa chi nhánh</li>
                        <li>✓ API access</li>
                        <li>✓ Account manager riêng</li>
                        <li>✓ Hoa hồng: 1.5%</li>
                    </ul>
                </div>
            </div>

            <h3>💳 Cơ Cấu Hoa Hồng</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Loại Giao Dịch</th>
                        <th>Tỷ Lệ Hoa Hồng</th>
                        <th>Mô Tả</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Đặt Món Ăn</strong></td>
                        <td>1.5% - 2.5%</td>
                        <td>Tùy theo gói subscription, tối thiểu $0.50/giao dịch</td>
                    </tr>
                    <tr>
                        <td><strong>Phí Đặt Bàn</strong></td>
                        <td>3.0%</td>
                        <td>Áp dụng cho tất cả gói, tối thiểu $0.50</td>
                    </tr>
                    <tr>
                        <td><strong>Giảm Giá Theo Volume</strong></td>
                        <td>-0.2% đến -0.5%</td>
                        <td>Doanh thu $5K-25K+/tháng</td>
                    </tr>
                    <tr>
                        <td><strong>Dịch Vụ Bổ Sung</strong></td>
                        <td>$10-25/tháng</td>
                        <td>SMS, POS integration, loyalty program</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>👥 Phân Quyền Người Dùng</h2>

            <h3>🔐 Hệ Thống Role & Permissions</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏢 Platform Admin</h4>
                    <ul>
                        <li>Quản lý toàn bộ marketplace</li>
                        <li>Duyệt đăng ký cửa hàng mới</li>
                        <li>Theo dõi revenue và commission</li>
                        <li>Xử lý disputes và support</li>
                        <li>Cấu hình subscription plans</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏪 Chủ Cửa Hàng</h4>
                    <ul>
                        <li>Đăng ký và setup cửa hàng</li>
                        <li>Quản lý subscription và billing</li>
                        <li>Theo dõi analytics và reports</li>
                        <li>Quản lý nhân viên và phân quyền</li>
                        <li>Cấu hình menu và pricing</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👨‍💼 Quản Lý Cửa Hàng</h4>
                    <ul>
                        <li>Quản lý booking hàng ngày</li>
                        <li>Cập nhật trạng thái bàn</li>
                        <li>Xử lý orders và customer service</li>
                        <li>Xem reports và analytics</li>
                        <li>Quản lý lịch làm việc nhân viên</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👤 Khách Hàng</h4>
                    <ul>
                        <li>Tìm kiếm và khám phá cửa hàng</li>
                        <li>Đặt bàn và pre-order món ăn</li>
                        <li>Thanh toán qua nhiều phương thức</li>
                        <li>Đánh giá và review cửa hàng</li>
                        <li>Tham gia chương trình loyalty</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🗺️ Chức Năng Tìm Kiếm & Chỉ Đường</h2>

            <h3>📍 Location-Based Services</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔍 Tìm Kiếm Thông Minh</h4>
                    <ul>
                        <li>Tìm kiếm theo vị trí GPS</li>
                        <li>Filter theo khoảng cách (1km, 5km, 10km+)</li>
                        <li>Lọc theo loại món ăn, giá cả, rating</li>
                        <li>Tìm kiếm theo tính năng (WiFi, parking, outdoor)</li>
                        <li>Kiểm tra availability real-time</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🗺️ Maps Integration</h4>
                    <ul>
                        <li>Google Maps cho Android</li>
                        <li>Apple Maps cho iOS</li>
                        <li>Interactive map với restaurant markers</li>
                        <li>Street view cho restaurant locations</li>
                        <li>Real-time traffic conditions</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🧭 Navigation Features</h4>
                    <ul>
                        <li>Turn-by-turn directions</li>
                        <li>Multiple route options</li>
                        <li>Walking/driving/public transport</li>
                        <li>ETA calculation với traffic</li>
                        <li>Arrival notifications</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🎯 Smart Recommendations</h4>
                    <ul>
                        <li>AI-powered suggestions</li>
                        <li>Based on location history</li>
                        <li>Weather-aware recommendations</li>
                        <li>Time-context suggestions</li>
                        <li>Social recommendations</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 Workflow 1: Quy Trình Đăng Ký Cửa Hàng</h2>

            <div class="workflow-container">
                <div class="workflow-title">Restaurant Onboarding Process (7-10 ngày)</div>

                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-title">Đăng Ký Ban Đầu</div>
                        <div class="step-description">Chủ cửa hàng tạo tài khoản và điền thông tin cơ bản</div>
                        <div class="step-details">
                            <ul>
                                <li>Tên cửa hàng & loại hình</li>
                                <li>Thông tin liên hệ chủ sở hữu</li>
                                <li>Địa chỉ kinh doanh</li>
                                <li>Ước tính doanh thu hàng tháng</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">Xác Thực Email</div>
                        <div class="step-description">Xác thực email và kích hoạt tài khoản</div>
                        <div class="step-details">
                            <ul>
                                <li>Email xác thực tự động</li>
                                <li>Click link xác nhận</li>
                                <li>Kích hoạt tài khoản</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">Upload Giấy Tờ</div>
                        <div class="step-description">Tải lên các giấy tờ pháp lý cần thiết</div>
                        <div class="step-details">
                            <ul>
                                <li>Giấy phép kinh doanh</li>
                                <li>Giấy phép vệ sinh an toàn thực phẩm</li>
                                <li>Mã số thuế</li>
                                <li>CMND/CCCD chủ sở hữu</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step decision-point">
                        <div class="step-number">4</div>
                        <div class="step-title">Duyệt Hồ Sơ</div>
                        <div class="step-description">Platform admin xem xét và phê duyệt</div>
                        <div class="step-details">
                            <ul>
                                <li>Kiểm tra tính hợp lệ giấy tờ</li>
                                <li>Xác minh thông tin kinh doanh</li>
                                <li>Đánh giá thủ công</li>
                                <li>Thời gian: 1-2 ngày làm việc</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step success-step">
                        <div class="step-number">5</div>
                        <div class="step-title">Chọn Gói Dịch Vụ</div>
                        <div class="step-description">Lựa chọn subscription plan và thiết lập thanh toán</div>
                        <div class="step-details">
                            <ul>
                                <li>So sánh các gói dịch vụ</li>
                                <li>Thiết lập phương thức thanh toán</li>
                                <li>Thanh toán tháng đầu tiên</li>
                                <li>Cấu hình auto-renewal</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">6</div>
                        <div class="step-title">Tạo Profile Cửa Hàng</div>
                        <div class="step-description">Thiết lập profile công khai cho khách hàng</div>
                        <div class="step-details">
                            <ul>
                                <li>Mô tả cửa hàng và câu chuyện</li>
                                <li>Upload gallery hình ảnh</li>
                                <li>Giờ mở cửa và thông tin liên hệ</li>
                                <li>Liên kết mạng xã hội</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">7</div>
                        <div class="step-title">Cấu Hình Menu</div>
                        <div class="step-description">Setup menu items và pricing</div>
                        <div class="step-details">
                            <ul>
                                <li>Tạo danh mục menu</li>
                                <li>Chi tiết món ăn và hình ảnh</li>
                                <li>Cấu hình giá cả</li>
                                <li>Thông tin allergen</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">8</div>
                        <div class="step-title">Thiết Lập Bàn</div>
                        <div class="step-description">Cấu hình bàn và layout cửa hàng</div>
                        <div class="step-details">
                            <ul>
                                <li>Thiết kế layout bàn</li>
                                <li>Cấu hình sức chứa</li>
                                <li>Chính sách đặt bàn</li>
                                <li>Tính năng đặc biệt</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">9</div>
                        <div class="step-title">Quản Lý Nhân Viên</div>
                        <div class="step-description">Thêm nhân viên và phân quyền</div>
                        <div class="step-details">
                            <ul>
                                <li>Thêm quản lý và nhân viên</li>
                                <li>Phân công vai trò</li>
                                <li>Cấu hình quyền hạn</li>
                                <li>Truy cập tài liệu đào tạo</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">10</div>
                        <div class="step-title">Đào Tạo & Test</div>
                        <div class="step-description">Test hệ thống và đào tạo nhân viên</div>
                        <div class="step-details">
                            <ul>
                                <li>Mock bookings và orders</li>
                                <li>Test thanh toán</li>
                                <li>Đào tạo nhân viên</li>
                                <li>Hướng dẫn sử dụng hệ thống</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step success-step">
                        <div class="step-number">11</div>
                        <div class="step-title">Go Live</div>
                        <div class="step-description">Kích hoạt cửa hàng trên platform</div>
                        <div class="step-details">
                            <ul>
                                <li>Hoàn thành checklist cuối cùng</li>
                                <li>Kích hoạt profile công khai</li>
                                <li>Thông báo cho khách hàng</li>
                                <li>Marketing chào mừng</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">12</div>
                        <div class="step-title">Hỗ Trợ Sau Launch</div>
                        <div class="step-description">Theo dõi và hỗ trợ sau khi go-live</div>
                        <div class="step-details">
                            <ul>
                                <li>Cuộc gọi check-in 30 ngày</li>
                                <li>Theo dõi hiệu suất</li>
                                <li>Đề xuất tối ưu hóa</li>
                                <li>Thiết lập hỗ trợ liên tục</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 Workflow 2: Quy Trình Đặt Bàn Khách Hàng</h2>

            <div class="workflow-container">
                <div class="workflow-title">Customer Booking Journey</div>

                <div class="timeline-flow">
                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-title">Khám Phá Cửa Hàng</div>
                            <div class="step-description">Khách hàng tìm kiếm và chọn cửa hàng</div>
                            <div class="step-details">
                                <ul>
                                    <li>Browse cửa hàng gần đây</li>
                                    <li>Filter theo món ăn, giá cả, rating</li>
                                    <li>Xem chi tiết cửa hàng</li>
                                    <li>Kiểm tra availability</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step decision-point">
                            <div class="step-number">2</div>
                            <div class="step-title">Kiểm Tra Đăng Nhập</div>
                            <div class="step-description">Xác thực hoặc tạo tài khoản</div>
                            <div class="step-details">
                                <ul>
                                    <li>Kiểm tra session hiện tại</li>
                                    <li>Tùy chọn đăng nhập xã hội</li>
                                    <li>Đặt bàn guest (tùy chọn)</li>
                                    <li>Hoàn thiện profile</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-title">Chi Tiết Booking</div>
                            <div class="step-description">Chọn thời gian, số người và yêu cầu đặc biệt</div>
                            <div class="step-details">
                                <ul>
                                    <li>Chọn ngày và giờ</li>
                                    <li>Xác định số người</li>
                                    <li>Preferences về bàn</li>
                                    <li>Yêu cầu đặc biệt</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">4</div>
                            <div class="step-title">Kiểm Tra Availability</div>
                            <div class="step-description">Hệ thống kiểm tra bàn trống real-time</div>
                            <div class="step-details">
                                <ul>
                                    <li>Trạng thái bàn real-time</li>
                                    <li>Matching sức chứa</li>
                                    <li>Validation time slot</li>
                                    <li>Đề xuất thay thế</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">5</div>
                            <div class="step-title">Pre-order (Tùy Chọn)</div>
                            <div class="step-description">Đặt món trước nếu muốn</div>
                            <div class="step-details">
                                <ul>
                                    <li>Browse menu items</li>
                                    <li>Thêm món vào giỏ hàng</li>
                                    <li>Tùy chỉnh orders</li>
                                    <li>Tính tổng tiền</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">6</div>
                            <div class="step-title">Xử Lý Thanh Toán</div>
                            <div class="step-description">Thanh toán booking fee và order (nếu có)</div>
                            <div class="step-details">
                                <ul>
                                    <li>Hiển thị breakdown giá</li>
                                    <li>Chọn phương thức thanh toán</li>
                                    <li>Tính toán commission</li>
                                    <li>Xử lý thanh toán bảo mật</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step success-step">
                            <div class="step-number">7</div>
                            <div class="step-title">Xác Nhận Booking</div>
                            <div class="step-description">Xác nhận booking và gửi thông báo</div>
                            <div class="step-details">
                                <ul>
                                    <li>Email xác nhận booking</li>
                                    <li>Tạo QR code</li>
                                    <li>Tích hợp calendar</li>
                                    <li>SMS notification</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">8</div>
                            <div class="step-title">Navigation & Check-in</div>
                            <div class="step-description">Chỉ đường và check-in tại cửa hàng</div>
                            <div class="step-details">
                                <ul>
                                    <li>GPS navigation đến cửa hàng</li>
                                    <li>Real-time traffic updates</li>
                                    <li>Arrival notification</li>
                                    <li>QR code check-in</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 Workflow 3: Quy Trình Thanh Toán & Hoa Hồng</h2>

            <div class="workflow-container">
                <div class="workflow-title">Payment Processing với Platform Commission</div>

                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-title">Hoàn Tất Order</div>
                        <div class="step-description">Khách hàng hoàn tất order với tổng tiền</div>
                        <div class="step-details">
                            <ul>
                                <li>Tính subtotal</li>
                                <li>Cộng thuế và phụ thu</li>
                                <li>Hiển thị tổng tiền cuối</li>
                                <li>Xác nhận chi tiết order</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">2</div>
                        <div class="step-title">Tính Hoa Hồng</div>
                        <div class="step-description">Hệ thống tính commission dựa trên gói subscription</div>
                        <div class="step-details">
                            <ul>
                                <li>Lấy thông tin gói của cửa hàng</li>
                                <li>Áp dụng tỷ lệ commission</li>
                                <li>Kiểm tra volume discounts</li>
                                <li>Tính số tiền ròng cho cửa hàng</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">Payment Gateway</div>
                        <div class="step-description">Charge khách hàng toàn bộ số tiền</div>
                        <div class="step-details">
                            <ul>
                                <li>Xử lý thanh toán bảo mật</li>
                                <li>Xác thực thẻ/ví điện tử</li>
                                <li>Phát hiện gian lận</li>
                                <li>Xác nhận giao dịch</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step decision-point">
                        <div class="step-number">4</div>
                        <div class="step-title">Trạng Thái Thanh Toán</div>
                        <div class="step-description">Kiểm tra kết quả thanh toán</div>
                        <div class="step-details">
                            <ul>
                                <li>Thành công: Tiếp tục chia tiền</li>
                                <li>Thất bại: Retry hoặc cancel</li>
                                <li>Pending: Chờ xác nhận</li>
                                <li>Cập nhật trạng thái giao dịch</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">5</div>
                        <div class="step-title">Chia Tiền</div>
                        <div class="step-description">Phân chia tiền giữa platform và cửa hàng</div>
                        <div class="step-details">
                            <ul>
                                <li>Platform commission: X%</li>
                                <li>Cửa hàng nhận: (100-X)%</li>
                                <li>Ghi nhận commission transaction</li>
                                <li>Cập nhật payout queue</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step success-step">
                        <div class="step-number">6</div>
                        <div class="step-title">Settlement</div>
                        <div class="step-description">Chuyển tiền cho cửa hàng theo lịch trình</div>
                        <div class="step-details">
                            <ul>
                                <li>Lịch payout hàng tuần</li>
                                <li>Ngưỡng payout tối thiểu</li>
                                <li>Xử lý chuyển khoản ngân hàng</li>
                                <li>Xác nhận payout</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">7</div>
                        <div class="step-title">Báo Cáo</div>
                        <div class="step-description">Cập nhật reports và analytics</div>
                        <div class="step-details">
                            <ul>
                                <li>Ghi nhận giao dịch</li>
                                <li>Theo dõi commission</li>
                                <li>Revenue analytics</li>
                                <li>Báo cáo hàng tháng</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 Workflow 4: Quy Trình Tìm Kiếm & Chỉ Đường</h2>

            <div class="workflow-container">
                <div class="workflow-title">Location-Based Search & Navigation</div>

                <div class="timeline-flow">
                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-title">Phát Hiện Vị Trí</div>
                            <div class="step-description">Xác định vị trí hiện tại của người dùng</div>
                            <div class="step-details">
                                <ul>
                                    <li>GPS coordinates detection</li>
                                    <li>Network-based location</li>
                                    <li>Manual location input</li>
                                    <li>Saved locations (home/work)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-title">Tìm Kiếm Cửa Hàng</div>
                            <div class="step-description">Search cửa hàng theo location và filters</div>
                            <div class="step-details">
                                <ul>
                                    <li>Proximity search (1km, 5km, 10km+)</li>
                                    <li>Filter theo cuisine, price, rating</li>
                                    <li>Availability filtering</li>
                                    <li>Feature filtering (WiFi, parking)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">3</div>
                            <div class="step-title">Tính Khoảng Cách</div>
                            <div class="step-description">Calculate distance và ETA cho mỗi cửa hàng</div>
                            <div class="step-details">
                                <ul>
                                    <li>Haversine formula cho straight-line</li>
                                    <li>Google Directions API cho actual routes</li>
                                    <li>Walking/driving/public transport ETA</li>
                                    <li>Real-time traffic consideration</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-title">Hiển Thị Kết Quả</div>
                            <div class="step-description">Show search results với multiple view modes</div>
                            <div class="step-details">
                                <ul>
                                    <li>List view với distance sorting</li>
                                    <li>Map view với restaurant markers</li>
                                    <li>Card view với photos và details</li>
                                    <li>Filter và sort options</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">5</div>
                            <div class="step-title">Chọn Cửa Hàng</div>
                            <div class="step-description">User chọn cửa hàng và xem details</div>
                            <div class="step-details">
                                <ul>
                                    <li>Restaurant details page</li>
                                    <li>Menu, photos, reviews</li>
                                    <li>Operating hours và contact</li>
                                    <li>Real-time availability</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step success-step">
                            <div class="step-number">6</div>
                            <div class="step-title">Navigation</div>
                            <div class="step-description">Cung cấp directions đến cửa hàng</div>
                            <div class="step-details">
                                <ul>
                                    <li>Multiple route options</li>
                                    <li>Turn-by-turn directions</li>
                                    <li>Real-time traffic updates</li>
                                    <li>Voice navigation</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">7</div>
                            <div class="step-title">Arrival & Check-in</div>
                            <div class="step-description">Arrival notification và check-in process</div>
                            <div class="step-details">
                                <ul>
                                    <li>Geofence-based arrival detection</li>
                                    <li>"You've arrived" notification</li>
                                    <li>QR code check-in display</li>
                                    <li>Table assignment confirmation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏗️ Kiến Trúc Hệ Thống</h2>

            <h3>🔧 Technology Stack</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Technology</th>
                        <th>Mô Tả</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Backend Framework</strong></td>
                        <td>Java Spring Boot 3</td>
                        <td>Microservices architecture với Spring Cloud</td>
                    </tr>
                    <tr>
                        <td><strong>Database</strong></td>
                        <td>MySQL + PostGIS</td>
                        <td>Primary database với spatial extensions</td>
                    </tr>
                    <tr>
                        <td><strong>Cache</strong></td>
                        <td>Redis</td>
                        <td>Session storage, caching, geospatial queries</td>
                    </tr>
                    <tr>
                        <td><strong>Message Queue</strong></td>
                        <td>Apache Kafka</td>
                        <td>Event streaming, real-time updates</td>
                    </tr>
                    <tr>
                        <td><strong>Search Engine</strong></td>
                        <td>Elasticsearch</td>
                        <td>Full-text search, location-based queries</td>
                    </tr>
                    <tr>
                        <td><strong>Maps & Navigation</strong></td>
                        <td>Google Maps API</td>
                        <td>Maps, directions, places, geocoding</td>
                    </tr>
                    <tr>
                        <td><strong>Mobile Apps</strong></td>
                        <td>React Native</td>
                        <td>Cross-platform iOS/Android apps</td>
                    </tr>
                    <tr>
                        <td><strong>Web Frontend</strong></td>
                        <td>React.js</td>
                        <td>Admin dashboard và web application</td>
                    </tr>
                    <tr>
                        <td><strong>Payment Processing</strong></td>
                        <td>Stripe + VNPay</td>
                        <td>International và local payment methods</td>
                    </tr>
                    <tr>
                        <td><strong>Cloud Infrastructure</strong></td>
                        <td>AWS/Azure</td>
                        <td>Scalable cloud hosting và services</td>
                    </tr>
                </tbody>
            </table>

            <h3>📊 Core Services</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 Authentication Service</h4>
                    <ul>
                        <li>Multi-tenant OAuth 2.0</li>
                        <li>Social login integration</li>
                        <li>JWT token management</li>
                        <li>Role-based access control</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏪 Restaurant Service</h4>
                    <ul>
                        <li>Restaurant profile management</li>
                        <li>Menu và pricing configuration</li>
                        <li>Table layout management</li>
                        <li>Staff và permissions</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>📍 Location Service</h4>
                    <ul>
                        <li>Geolocation processing</li>
                        <li>Proximity calculations</li>
                        <li>Maps integration</li>
                        <li>Navigation routing</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🔍 Search Service</h4>
                    <ul>
                        <li>Full-text search</li>
                        <li>Location-based filtering</li>
                        <li>Advanced filters</li>
                        <li>Recommendation engine</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>📅 Booking Service</h4>
                    <ul>
                        <li>Real-time availability</li>
                        <li>Reservation management</li>
                        <li>Waitlist handling</li>
                        <li>Check-in processing</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>💳 Payment Service</h4>
                    <ul>
                        <li>Multiple payment gateways</li>
                        <li>Commission calculation</li>
                        <li>Payout processing</li>
                        <li>Financial reporting</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>💰 Billing Service</h4>
                    <ul>
                        <li>Subscription management</li>
                        <li>Invoice generation</li>
                        <li>Auto-renewal processing</li>
                        <li>Failed payment handling</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>📊 Analytics Service</h4>
                    <ul>
                        <li>Platform-wide analytics</li>
                        <li>Restaurant performance</li>
                        <li>Customer behavior tracking</li>
                        <li>Business intelligence</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 Dự Báo Tài Chính</h2>

            <h3>💰 Revenue Projections</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">500</div>
                    <div class="stat-label">Cửa hàng mục tiêu năm 1</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$79</div>
                    <div class="stat-label">Subscription trung bình/tháng</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2.0%</div>
                    <div class="stat-label">Commission trung bình</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$2.5M</div>
                    <div class="stat-label">Doanh thu năm 1</div>
                </div>
            </div>

            <h3>💸 Chi Phí Phát Triển</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Hạng Mục</th>
                        <th>Chi Phí</th>
                        <th>Ghi Chú</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Phát Triển Hệ Thống</strong></td>
                        <td>$500K - $700K</td>
                        <td>10-12 tháng development với location features</td>
                    </tr>
                    <tr>
                        <td><strong>Infrastructure (Hàng Tháng)</strong></td>
                        <td>$3K - $5K</td>
                        <td>AWS/Azure, scales theo usage</td>
                    </tr>
                    <tr>
                        <td><strong>Third-party APIs</strong></td>
                        <td>$500 - $1K/tháng</td>
                        <td>Google Maps, payment gateways, SMS</td>
                    </tr>
                    <tr>
                        <td><strong>Team Size</strong></td>
                        <td>10-12 người</td>
                        <td>Developers, DevOps, QA, Product Manager</td>
                    </tr>
                    <tr>
                        <td><strong>Break-even Point</strong></td>
                        <td>75-100 cửa hàng</td>
                        <td>Khoảng 8-12 tháng sau launch</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎯 Lợi Ích Cho Các Bên</h2>

            <h3>🏪 Cho Cửa Hàng</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💻 Nền Tảng Công Nghệ</h4>
                    <p>Không cần đầu tư phát triển, sử dụng ngay solution chuyên nghiệp với đầy đủ tính năng</p>
                </div>
                <div class="feature-card">
                    <h4>👥 Thu Hút Khách Hàng</h4>
                    <p>Tiếp cận customer base của platform, marketing reach và discovery tự nhiên</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Xử Lý Thanh Toán</h4>
                    <p>Payment processing bảo mật, PCI-compliant với multiple payment methods</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Business Intelligence</h4>
                    <p>Analytics nâng cao và insights để optimize operations và tăng doanh thu</p>
                </div>
            </div>

            <h3>👤 Cho Khách Hàng</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔍 Khám Phá Dễ Dàng</h4>
                    <p>Single platform để discover và book nhiều cửa hàng với search thông minh</p>
                </div>
                <div class="feature-card">
                    <h4>🗺️ Navigation Tích Hợp</h4>
                    <p>Chỉ đường GPS, real-time traffic, multiple transport modes</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ Trải Nghiệm Mượt Mà</h4>
                    <p>Booking process nhất quán, interface thống nhất across restaurants</p>
                </div>
                <div class="feature-card">
                    <h4>🎁 Loyalty Benefits</h4>
                    <p>Cross-restaurant loyalty program và rewards, saved preferences</p>
                </div>
            </div>

            <h3>🏢 Cho Platform Owner</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💰 Doanh Thu Ổn Định</h4>
                    <p>Predictable monthly income từ subscriptions + commission từ transactions</p>
                </div>
                <div class="feature-card">
                    <h4>📈 Mô Hình Scalable</h4>
                    <p>Network effects, commission tăng theo volume, data advantages</p>
                </div>
                <div class="feature-card">
                    <h4>🌐 Mở Rộng Địa Lý</h4>
                    <p>Dễ dàng expand sang cities/countries mới với existing platform</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Monetize Data</h4>
                    <p>Valuable insights từ aggregated data, advertising opportunities</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📝 Kết Luận</h2>

            <div class="highlight">
                <h4>🚀 Hệ Thống Marketplace Hoàn Chỉnh</h4>
                <p>Đây là một comprehensive marketplace platform kết nối cửa hàng F&B với khách hàng, tích hợp đầy đủ location services, navigation, payment processing và business intelligence.</p>
            </div>

            <h3>✅ Điểm Mạnh Cạnh Tranh</h3>
            <ul>
                <li><strong>🗺️ Location Intelligence:</strong> Advanced search, navigation và location-based recommendations</li>
                <li><strong>💰 Multiple Revenue Streams:</strong> Subscription + Commission + Advertising tạo stable income</li>
                <li><strong>🔧 Complete Solution:</strong> End-to-end platform từ discovery đến payment và analytics</li>
                <li><strong>📱 Mobile-First:</strong> Native mobile experience với maps integration</li>
                <li><strong>🌐 Network Effects:</strong> More restaurants attract more customers và ngược lại</li>
                <li><strong>📊 Data Advantages:</strong> Rich data insights tạo competitive moat</li>
            </ul>

            <h3>🎯 Roadmap Triển Khai</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">10-12</div>
                    <div class="stat-label">Tháng Development</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$500K-700K</div>
                    <div class="stat-label">Investment Cần Thiết</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">75-100</div>
                    <div class="stat-label">Cửa hàng Break-even</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$2.5M+</div>
                    <div class="stat-label">Revenue Potential Năm 1</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px;">
                <h3 style="color: white; margin-top: 0;">🌟 Sẵn Sàng Chuyển Đổi Ngành F&B</h3>
                <p style="font-size: 1.1em; margin-bottom: 0;">Marketplace Table Management System - Kết Nối Cửa Hàng với Khách Hàng</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; color: #666;">
            <p><strong>📄 Tài liệu được tạo bởi Augment Agent</strong></p>
            <p>🗓️ Ngày tạo: 14/07/2025 | 📋 Version: 3.0 - Complete Vietnamese Documentation với Workflows</p>
        </div>
    </div>
</body>
</html>
