<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H<PERSON> 3 Cấp - <PERSON><PERSON> Chỉnh</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 4px solid #667eea;
            padding-bottom: 30px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: -40px -40px 40px -40px;
            padding: 40px;
            border-radius: 15px 15px 0 0;
        }
        .header h1 {
            font-size: 2.8em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header .subtitle {
            font-size: 1.3em;
            margin-top: 15px;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 60px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #667eea;
            border-left: 6px solid #667eea;
            padding-left: 20px;
            font-size: 2em;
            margin-bottom: 25px;
            background: linear-gradient(90deg, #f8f9ff, transparent);
            padding: 15px 20px;
            border-radius: 0 10px 10px 0;
        }
        .section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .section h4 {
            color: #667eea;
            font-size: 1.3em;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h4 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.2em;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
        }
        .highlight h4 {
            color: #856404;
            margin-top: 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        .comparison-table tr:hover {
            background-color: #f0f2ff;
        }
        .timeline {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 30px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
        }
        .phase {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-left: 4px solid #764ba2;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        .phase h4 {
            margin-top: 0;
            color: #764ba2;
            font-size: 1.2em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .stat-number {
            font-size: 2.2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .diagram-container {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            position: relative;
            overflow-x: auto;
        }
        .diagram-title {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            position: absolute;
            top: -15px;
            left: 30px;
            z-index: 10;
        }
        .mermaid {
            margin-top: 30px;
            text-align: center;
        }
        .toc {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc h3 {
            color: #667eea;
            margin-top: 0;
        }
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        .toc li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .toc a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .toc a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        .role-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .role-card {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            border-top: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .admin-card {
            border-top-color: #764ba2;
            background: linear-gradient(135deg, #fff8f8, #fff);
        }
        .instructor-card {
            border-top-color: #28a745;
            background: linear-gradient(135deg, #f8fff8, #fff);
        }
        .student-card {
            border-top-color: #17a2b8;
            background: linear-gradient(135deg, #f8feff, #fff);
        }
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
            .section {
                page-break-inside: avoid;
            }
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header {
                margin: -20px -20px 30px -20px;
                padding: 30px 20px;
            }
            .feature-grid,
            .role-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🥋 Hệ Thống Quản Lý Học Viên 3 Cấp</h1>
            <div class="subtitle">Phân Tích Hoàn Chỉnh - Võ Thuật Quốc Gia</div>
        </div>

        <div class="toc">
            <h3>📋 Mục Lục</h3>
            <ul>
                <li><a href="#overview">1. Tổng Quan Hệ Thống</a></li>
                <li><a href="#organization">2. Cấu Trúc Tổ Chức 3 Cấp</a></li>
                <li><a href="#workflows">3. Quy Trình Chính</a></li>
                <li><a href="#roles">4. Phân Quyền & Vai Trò</a></li>
                <li><a href="#features">5. Chức Năng Hệ Thống</a></li>
                <li><a href="#technology">6. Kiến Trúc Kỹ Thuật</a></li>
                <li><a href="#timeline">7. Kế Hoạch Triển Khai</a></li>
                <li><a href="#budget">8. Ngân Sách & Tài Nguyên</a></li>
                <li><a href="#benefits">9. Lợi Ích & ROI</a></li>
            </ul>
        </div>

        <div class="section" id="overview">
            <h2>1. Tổng Quan Hệ Thống</h2>
            
            <div class="highlight">
                <h4>🎯 Mục Tiêu Chính</h4>
                <p>Xây dựng hệ thống quản lý học viên võ thuật toàn quốc với 3 cấp độ quản lý: <strong>Quốc gia → Thành phố → Phường/Xã</strong>, hỗ trợ đầy đủ quy trình đào tạo, thi cử và cấp chứng chỉ.</p>
            </div>

            <h3>✨ Tính Năng Cốt Lõi</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏛️ Quản Lý 3 Cấp</h4>
                    <p>Hệ thống phân cấp rõ ràng từ Quốc gia xuống Phường/Xã với các ban ngành chuyên môn</p>
                </div>
                <div class="feature-card">
                    <h4>👤 Quản Lý Học Viên</h4>
                    <p>Hồ sơ chi tiết, lịch sử học tập, thi cử, chuyển đơn vị và theo dõi tiến độ</p>
                </div>
                <div class="feature-card">
                    <h4>🏆 Hệ Thống Thi Cử</h4>
                    <p>Tổ chức thi, chấm điểm, cấp chứng chỉ và quản lý đẳng cấp võ thuật</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Báo Cáo Đa Cấp</h4>
                    <p>Dashboard và báo cáo theo từng cấp độ quản lý với analytics chi tiết</p>
                </div>
                <div class="feature-card">
                    <h4>📱 Mobile & Web</h4>
                    <p>Ứng dụng mobile cho học viên, HLV và website quản lý cho admin</p>
                </div>
                <div class="feature-card">
                    <h4>🔐 Bảo Mật & Phân Quyền</h4>
                    <p>Hệ thống phân quyền chi tiết theo vai trò và cấp độ quản lý</p>
                </div>
            </div>

            <h3>📋 Thông Tin Học Viên</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Loại Thông Tin</th>
                        <th>Chi Tiết</th>
                        <th>Mục Đích Sử Dụng</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Thông Tin Cá Nhân</strong></td>
                        <td>Họ tên, ngày sinh, giới tính, địa chỉ, CCCD</td>
                        <td>Định danh và liên lạc</td>
                    </tr>
                    <tr>
                        <td><strong>Liên Hệ</strong></td>
                        <td>Số điện thoại, email, liên hệ khẩn cấp</td>
                        <td>Thông báo và hỗ trợ</td>
                    </tr>
                    <tr>
                        <td><strong>Học Tập</strong></td>
                        <td>Đơn vị, môn võ, đẳng cấp, HLV phụ trách</td>
                        <td>Quản lý đào tạo</td>
                    </tr>
                    <tr>
                        <td><strong>Lịch Sử</strong></td>
                        <td>Thi cử, chứng chỉ, chuyển đơn vị, điểm danh</td>
                        <td>Theo dõi tiến độ</td>
                    </tr>
                    <tr>
                        <td><strong>Ghi Chú</strong></td>
                        <td>Tình trạng sức khỏe, yêu cầu đặc biệt</td>
                        <td>Hỗ trợ cá nhân hóa</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section" id="organization">
            <h2>2. Cấu Trúc Tổ Chức 3 Cấp</h2>

            <div class="diagram-container">
                <div class="diagram-title">Hierarchy Tổ Chức Võ Thuật Quốc Gia</div>
                <div class="mermaid">
graph TD
    subgraph "CẤP QUỐC GIA"
        NATIONAL[Tổng Cục Võ Thuật<br/>Quốc Gia]
        NATIONAL_DEPT[Ban Ngành Quốc Gia<br/>- Ban Thi Đấu<br/>- Ban Đào Tạo<br/>- Ban Kỹ Thuật]
    end

    subgraph "CẤP THÀNH PHỐ/TỈNH"
        CITY1[Sở Võ Thuật<br/>TP.HCM]
        CITY2[Sở Võ Thuật<br/>Hà Nội]
        CITY3[Sở Võ Thuật<br/>Đà Nẵng]
        CITY_DEPT[Ban Ngành Thành Phố<br/>- Ban Thi Đấu TP<br/>- Ban Đào Tạo TP<br/>- Ban Kỹ Thuật TP]
    end

    subgraph "CẤP PHƯỜNG/XÃ"
        WARD1[Phòng Võ Thuật<br/>Quận 1]
        WARD2[Phòng Võ Thuật<br/>Quận 2]
        WARD3[Phòng Võ Thuật<br/>Huyện A]
        WARD_DEPT[Ban Ngành Phường/Xã<br/>- Đội Thi Đấu<br/>- Nhóm Đào Tạo<br/>- Nhóm Kỹ Thuật]
    end

    subgraph "ĐƠN VỊ CƠ SỞ"
        CLUB1[Câu Lạc Bộ A]
        CLUB2[Câu Lạc Bộ B]
        SCHOOL1[Trường Võ A]
        SCHOOL2[Trường Võ B]
    end

    NATIONAL --> NATIONAL_DEPT
    NATIONAL --> CITY1
    NATIONAL --> CITY2
    NATIONAL --> CITY3

    CITY1 --> CITY_DEPT
    CITY1 --> WARD1
    CITY1 --> WARD2

    CITY2 --> WARD3

    WARD1 --> WARD_DEPT
    WARD1 --> CLUB1
    WARD1 --> SCHOOL1

    WARD2 --> CLUB2
    WARD2 --> SCHOOL2

    style NATIONAL fill:#e1f5fe
    style CITY1 fill:#f3e5f5
    style WARD1 fill:#e8f5e8
    style CLUB1 fill:#fff3e0
                </div>
            </div>

            <h3>🏛️ Mô Tả Chi Tiết Các Cấp</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🇻🇳 Cấp Quốc Gia</h4>
                    <ul>
                        <li>Tổng Cục Võ Thuật Quốc Gia</li>
                        <li>Ban Thi Đấu: Tổ chức giải quốc gia</li>
                        <li>Ban Đào Tạo: Chương trình chuẩn</li>
                        <li>Ban Kỹ Thuật: Nghiên cứu phát triển</li>
                        <li>Chính sách và quy định chung</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏙️ Cấp Thành Phố/Tỉnh</h4>
                    <ul>
                        <li>Sở Võ Thuật các tỉnh/thành</li>
                        <li>Ban Thi Đấu TP: Giải cấp tỉnh</li>
                        <li>Ban Đào Tạo TP: Triển khai địa phương</li>
                        <li>Ban Kỹ Thuật TP: Hỗ trợ kỹ thuật</li>
                        <li>Quản lý các phường/xã</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏘️ Cấp Phường/Xã</h4>
                    <ul>
                        <li>Phòng Võ Thuật địa phương</li>
                        <li>Đội Thi Đấu: Giải cấp quận/huyện</li>
                        <li>Nhóm Đào Tạo: Giám sát CLB</li>
                        <li>Nhóm Kỹ Thuật: Hỗ trợ trực tiếp</li>
                        <li>Quản lý câu lạc bộ</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🥋 Đơn Vị Cơ Sở</h4>
                    <ul>
                        <li>Câu lạc bộ võ thuật</li>
                        <li>Trường võ tư nhân</li>
                        <li>Lớp học cộng đồng</li>
                        <li>Đội tuyển trường học</li>
                        <li>Đào tạo trực tiếp học viên</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="workflows">
            <h2>3. Quy Trình Chính</h2>

            <h3>📝 1. Quy Trình Đăng Ký Đơn Vị</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Bước 1: Xác Định Cấp Độ</h4>
                    <p>Đơn vị xác định cấp độ đăng ký (Thành phố, Phường/Xã, hoặc Câu lạc bộ)</p>
                </div>
                <div class="phase">
                    <h4>Bước 2: Chuẩn Bị Hồ Sơ</h4>
                    <p>Điền form đăng ký, chuẩn bị giấy tờ pháp lý, CV người phụ trách</p>
                </div>
                <div class="phase">
                    <h4>Bước 3: Nộp Đơn & Duyệt</h4>
                    <p>Nộp hồ sơ lên cấp trên tương ứng để xem xét và phê duyệt</p>
                </div>
                <div class="phase">
                    <h4>Bước 4: Kích Hoạt</h4>
                    <p>Sau khi được duyệt, đơn vị được kích hoạt và cấp tài khoản quản lý</p>
                </div>
            </div>

            <h3>👤 2. Quy Trình Đăng Ký Học Viên</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Bước 1: Chọn Đơn Vị</h4>
                    <p>Học viên chọn câu lạc bộ/trường võ phù hợp với địa điểm và môn võ</p>
                </div>
                <div class="phase">
                    <h4>Bước 2: Điền Thông Tin</h4>
                    <p>Hoàn thiện thông tin cá nhân: họ tên, địa chỉ, CCCD, liên hệ</p>
                </div>
                <div class="phase">
                    <h4>Bước 3: Chọn Môn Võ</h4>
                    <p>Lựa chọn môn võ muốn học: Karate, Taekwondo, Vovinam, Judo...</p>
                </div>
                <div class="phase">
                    <h4>Bước 4: Phê Duyệt 2 Cấp</h4>
                    <p>HLV xem xét → Ward Admin phê duyệt → Tạo hồ sơ học viên</p>
                </div>
            </div>

            <h3>🔄 3. Quy Trình Chuyển Đơn Vị (2 Cấp Duyệt)</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Cấp Duyệt 1: Đơn Vị Cũ</h4>
                    <p>HLV hiện tại xác nhận → Admin đơn vị cũ phê duyệt chuyển đi</p>
                </div>
                <div class="phase">
                    <h4>Cấp Duyệt 2: Đơn Vị Mới</h4>
                    <p>HLV đơn vị mới xác nhận nhận → Admin đơn vị mới phê duyệt</p>
                </div>
                <div class="phase">
                    <h4>Xử Lý Chuyển</h4>
                    <p>Cập nhật hồ sơ, chuyển lịch sử học tập, thông báo các bên</p>
                </div>
                <div class="phase">
                    <h4>Hỗ Trợ Hòa Nhập</h4>
                    <p>Welcome tại đơn vị mới, theo dõi thích ứng, hỗ trợ cần thiết</p>
                </div>
            </div>

            <h3>🏆 4. Quy Trình Tổ Chức Kì Thi</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Lập Kế Hoạch</h4>
                    <p>Xác định cấp độ thi, thời gian, địa điểm, hội đồng thi</p>
                </div>
                <div class="phase">
                    <h4>Xin Phê Duyệt</h4>
                    <p>Nộp kế hoạch lên cấp có thẩm quyền để phê duyệt tổ chức</p>
                </div>
                <div class="phase">
                    <h4>Tiến Hành Thi</h4>
                    <p>Kiểm tra danh tính, thi lý thuyết, thực hành, đánh giá kỹ năng</p>
                </div>
                <div class="phase">
                    <h4>Cấp Chứng Chỉ</h4>
                    <p>Chấm thi, công bố kết quả, cấp chứng chỉ, tổ chức lễ trao</p>
                </div>
            </div>
        </div>

        <div class="section" id="roles">
            <h2>4. Phân Quyền & Vai Trò</h2>

            <div class="role-section">
                <div class="role-card admin-card">
                    <h4>👑 Super Admin (Cấp Quốc Gia)</h4>
                    <ul>
                        <li>Quản lý toàn bộ hệ thống</li>
                        <li>Tạo/sửa/xóa City Admin</li>
                        <li>Xem báo cáo toàn quốc</li>
                        <li>Cấu hình hệ thống toàn cục</li>
                        <li>Phê duyệt chính sách mới</li>
                        <li>Quản lý backup và bảo mật</li>
                    </ul>
                </div>
                <div class="role-card admin-card">
                    <h4>🏙️ City Admin (Cấp Thành Phố)</h4>
                    <ul>
                        <li>Quản lý đơn vị trong thành phố</li>
                        <li>Tạo/sửa/xóa Ward Admin</li>
                        <li>Phê duyệt đơn vị cấp phường</li>
                        <li>Tổ chức thi cử cấp thành phố</li>
                        <li>Báo cáo lên cấp quốc gia</li>
                        <li>Quản lý ngân sách thành phố</li>
                    </ul>
                </div>
                <div class="role-card admin-card">
                    <h4>🏘️ Ward Admin (Cấp Phường/Xã)</h4>
                    <ul>
                        <li>Quản lý CLB trong phường</li>
                        <li>Tạo/sửa tài khoản Instructor</li>
                        <li>Phê duyệt đăng ký học viên</li>
                        <li>Tổ chức thi cử cấp phường</li>
                        <li>Báo cáo lên cấp thành phố</li>
                        <li>Giám sát hoạt động CLB</li>
                    </ul>
                </div>
                <div class="role-card instructor-card">
                    <h4>👨‍🏫 Instructor (Huấn Luyện Viên)</h4>
                    <ul>
                        <li>Quản lý học viên trong lớp</li>
                        <li>Điểm danh và đánh giá</li>
                        <li>Đề xuất thi cử</li>
                        <li>Cập nhật tiến độ học tập</li>
                        <li>Liên hệ phụ huynh</li>
                        <li>Báo cáo lên Ward Admin</li>
                    </ul>
                </div>
                <div class="role-card student-card">
                    <h4>👤 Student (Học Viên)</h4>
                    <ul>
                        <li>Xem thông tin cá nhân</li>
                        <li>Đăng ký thi cử</li>
                        <li>Tải tài liệu học tập</li>
                        <li>Xem lịch sử và chứng chỉ</li>
                        <li>Đăng ký chuyển đơn vị</li>
                        <li>Phản hồi và đánh giá</li>
                    </ul>
                </div>
                <div class="role-card student-card">
                    <h4>👨‍👩‍👧‍👦 Parent (Phụ Huynh)</h4>
                    <ul>
                        <li>Theo dõi tiến độ con</li>
                        <li>Nhận thông báo điểm danh</li>
                        <li>Thanh toán học phí</li>
                        <li>Liên hệ với HLV</li>
                        <li>Xem lịch thi và sự kiện</li>
                        <li>Emergency contact</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="features">
            <h2>5. Chức Năng Hệ Thống</h2>

            <h3>📚 Quản Lý Lịch Sử Học Viên</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📋 Hồ Sơ Cá Nhân</h4>
                    <ul>
                        <li>Thông tin cơ bản và liên hệ</li>
                        <li>Ảnh đại diện và documents</li>
                        <li>Tình trạng sức khỏe</li>
                        <li>Liên hệ khẩn cấp</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏆 Lịch Sử Thi Cử</h4>
                    <ul>
                        <li>Danh sách các kỳ thi đã tham gia</li>
                        <li>Kết quả và điểm số chi tiết</li>
                        <li>Nhận xét của hội đồng thi</li>
                        <li>Timeline tiến độ lên đai</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>📜 Chứng Chỉ & Đẳng Cấp</h4>
                    <ul>
                        <li>Chứng chỉ đai các cấp độ</li>
                        <li>Chứng chỉ kỹ năng đặc biệt</li>
                        <li>Thành tích thi đấu</li>
                        <li>QR code xác thực</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🔄 Lịch Sử Chuyển Đơn Vị</h4>
                    <ul>
                        <li>Nhật ký chuyển đơn vị</li>
                        <li>Lý do và thời gian chuyển</li>
                        <li>Đánh giá thích ứng</li>
                        <li>Hỗ trợ hòa nhập</li>
                    </ul>
                </div>
            </div>

            <h3>✅ Hệ Thống Điểm Danh</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📱 QR Code Scanning</h4>
                    <p>Học viên scan QR code để điểm danh nhanh chóng, xác nhận vị trí GPS</p>
                </div>
                <div class="feature-card">
                    <h4>📝 Manual Check-in</h4>
                    <p>HLV điểm danh thủ công với danh sách lớp, ghi chú lý do vắng mặt</p>
                </div>
                <div class="feature-card">
                    <h4>💳 NFC/RFID Cards</h4>
                    <p>Thẻ học viên NFC để điểm danh tự động, hiển thị thông tin ngay lập tức</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Analytics & Reports</h4>
                    <p>Phân tích tỷ lệ tham gia, xu hướng vắng mặt, cảnh báo rủi ro</p>
                </div>
            </div>

            <h3>📚 Quản Lý Tài Liệu Học Tập</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Đẳng Cấp</th>
                        <th>Loại Tài Liệu</th>
                        <th>Nội Dung</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>🟡 Đai Trắng</strong></td>
                        <td>Video, PDF, Quiz</td>
                        <td>Tư thế cơ bản, lịch sử môn võ, thuật ngữ</td>
                    </tr>
                    <tr>
                        <td><strong>🟠 Đai Màu</strong></td>
                        <td>Video, PDF, Simulation</td>
                        <td>Kỹ thuật nâng cao, chiến thuật, tình huống</td>
                    </tr>
                    <tr>
                        <td><strong>🟤 Đai Nâu</strong></td>
                        <td>Video, PDF, Case Study</td>
                        <td>Kỹ thuật chuyên sâu, phương pháp dạy học</td>
                    </tr>
                    <tr>
                        <td><strong>⚫ Đai Đen</strong></td>
                        <td>Research, Workshop</td>
                        <td>Nghiên cứu, triết lý võ thuật, lãnh đạo</td>
                    </tr>
                </tbody>
            </table>

            <h3>📊 Hệ Thống Báo Cáo</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏛️ Dashboard Quốc Gia</h4>
                    <ul>
                        <li>Tổng số học viên toàn quốc</li>
                        <li>Phân bố theo vùng miền</li>
                        <li>Tăng trưởng theo thời gian</li>
                        <li>Top môn võ phổ biến</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏙️ Dashboard Thành Phố</h4>
                    <ul>
                        <li>Học viên trong thành phố</li>
                        <li>So sánh với thành phố khác</li>
                        <li>Hiệu quả các phường/xã</li>
                        <li>Chất lượng đào tạo</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏘️ Dashboard Phường/Xã</h4>
                    <ul>
                        <li>Học viên các CLB</li>
                        <li>Tỷ lệ tham gia</li>
                        <li>Thành tích thi cử</li>
                        <li>Hoạt động đào tạo</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🥋 Dashboard CLB</h4>
                    <ul>
                        <li>Học viên lớp học</li>
                        <li>Điểm danh và tiến độ</li>
                        <li>Chuẩn bị thi cử</li>
                        <li>Phản hồi phụ huynh</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="technology">
            <h2>6. Kiến Trúc Kỹ Thuật</h2>

            <h3>🔧 Technology Stack</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Technology</th>
                        <th>Mô Tả</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Backend Framework</strong></td>
                        <td>Java Spring Boot 3</td>
                        <td>Microservices architecture với Spring Cloud</td>
                    </tr>
                    <tr>
                        <td><strong>Database</strong></td>
                        <td>PostgreSQL + Redis</td>
                        <td>Primary database + Cache & Sessions</td>
                    </tr>
                    <tr>
                        <td><strong>Message Queue</strong></td>
                        <td>Apache Kafka</td>
                        <td>Event streaming, real-time updates</td>
                    </tr>
                    <tr>
                        <td><strong>Search Engine</strong></td>
                        <td>Elasticsearch</td>
                        <td>Full-text search, advanced queries</td>
                    </tr>
                    <tr>
                        <td><strong>Web Frontend</strong></td>
                        <td>React.js + TypeScript</td>
                        <td>Admin dashboard và web application</td>
                    </tr>
                    <tr>
                        <td><strong>Mobile Apps</strong></td>
                        <td>React Native</td>
                        <td>Cross-platform iOS/Android apps</td>
                    </tr>
                    <tr>
                        <td><strong>Cloud Infrastructure</strong></td>
                        <td>AWS/Azure</td>
                        <td>Scalable cloud hosting và services</td>
                    </tr>
                </tbody>
            </table>

            <h3>📱💻 Platform Distribution</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📱 Mobile App Priority</h4>
                    <ul>
                        <li>Điểm danh QR/NFC (offline capable)</li>
                        <li>Push notifications</li>
                        <li>Xem lịch học cá nhân</li>
                        <li>Chat với HLV và phụ huynh</li>
                        <li>GPS navigation đến lớp học</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>💻 Website Priority</h4>
                    <ul>
                        <li>Dashboard analytics với charts</li>
                        <li>Quản lý người dùng và phân quyền</li>
                        <li>Báo cáo chi tiết và export</li>
                        <li>Cấu hình hệ thống</li>
                        <li>Bulk operations</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🌐 Public Website</h4>
                    <ul>
                        <li>Tìm kiếm và tra cứu học viên</li>
                        <li>Xác thực chứng chỉ (QR scan)</li>
                        <li>Danh sách CLB và liên hệ</li>
                        <li>Lịch thi công khai</li>
                        <li>Tin tức và sự kiện</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🔄 Shared Features</h4>
                    <ul>
                        <li>Authentication và authorization</li>
                        <li>Profile management</li>
                        <li>Notifications system</li>
                        <li>File upload/download</li>
                        <li>Multi-language support</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="timeline">
            <h2>7. Kế Hoạch Triển Khai</h2>

            <div class="diagram-container">
                <div class="diagram-title">Development Timeline - 18 Months</div>
                <div class="mermaid">
gantt
    title Hệ Thống Quản Lý Học Viên - Development Timeline
    dateFormat  YYYY-MM-DD
    section Phase 1: Backend Foundation
    Backend Core Services     :p1-backend, 2024-01-01, 16w
    Database Design & Setup   :p1-db, 2024-01-01, 8w
    Authentication System     :p1-auth, after p1-db, 4w
    User Management          :p1-user, after p1-auth, 4w
    Basic APIs              :p1-api, after p1-user, 4w
    Testing & Documentation  :p1-test, after p1-api, 4w

    section Phase 2: Web Platform
    Admin Dashboard         :p2-admin, after p1-test, 8w
    Public Website         :p2-public, after p1-test, 6w
    Instructor Portal      :p2-instructor, after p2-public, 6w
    Reporting System       :p2-report, after p2-instructor, 4w
    Integration Testing    :p2-test, after p2-report, 2w

    section Phase 3: Mobile Apps
    Student Mobile App     :p3-student, after p2-test, 10w
    Instructor Mobile App  :p3-instructor, after p3-student, 6w
    Parent Mobile App      :p3-parent, after p3-instructor, 6w
    Mobile Testing        :p3-test, after p3-parent, 4w

    section Deployment & Launch
    Production Setup      :deploy, after p3-test, 2w
    User Training        :training, after deploy, 2w
    Go Live             :golive, after training, 1w
    Post-Launch Support  :support, after golive, 4w
                </div>
            </div>

            <h3>📅 Phase Details</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Phase 1: Backend Foundation (4 tháng)</h4>
                    <ul>
                        <li><strong>Database Design:</strong> ERD, schema, optimization (2 tháng)</li>
                        <li><strong>Authentication:</strong> JWT, RBAC, security (1 tháng)</li>
                        <li><strong>User Management:</strong> CRUD, hierarchy, permissions (1 tháng)</li>
                        <li><strong>Core APIs:</strong> REST APIs, documentation, testing</li>
                    </ul>
                </div>
                <div class="phase">
                    <h4>Phase 2: Web Platform (6 tháng)</h4>
                    <ul>
                        <li><strong>Admin Dashboard:</strong> React.js, real-time dashboard (2 tháng)</li>
                        <li><strong>Public Website:</strong> Next.js, SEO optimization (1.5 tháng)</li>
                        <li><strong>Instructor Portal:</strong> Class management, reports (1.5 tháng)</li>
                        <li><strong>Reporting System:</strong> Analytics, charts, exports (1 tháng)</li>
                    </ul>
                </div>
                <div class="phase">
                    <h4>Phase 3: Mobile Apps (5.5 tháng)</h4>
                    <ul>
                        <li><strong>Student App:</strong> QR scanning, notifications (2.5 tháng)</li>
                        <li><strong>Instructor App:</strong> Quick attendance, communication (1.5 tháng)</li>
                        <li><strong>Parent App:</strong> Child monitoring, payments (1.5 tháng)</li>
                    </ul>
                </div>
                <div class="phase">
                    <h4>Deployment & Launch (2.5 tháng)</h4>
                    <ul>
                        <li><strong>Production Setup:</strong> Cloud deployment, monitoring</li>
                        <li><strong>User Training:</strong> Documentation, video tutorials</li>
                        <li><strong>Go Live:</strong> Phased rollout, support system</li>
                        <li><strong>Post-Launch:</strong> Bug fixes, optimization, feedback</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="budget">
            <h2>8. Ngân Sách & Tài Nguyên</h2>

            <h3>💰 Chi Phí Phát Triển</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">$576K</div>
                    <div class="stat-label">Personnel Costs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$108K</div>
                    <div class="stat-label">Infrastructure & Tools</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$37K</div>
                    <div class="stat-label">Marketing & Training</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$721K</div>
                    <div class="stat-label">Total Project Cost</div>
                </div>
            </div>

            <h3>👥 Team Structure</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Size</th>
                        <th>Duration</th>
                        <th>Cost</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Backend Team</strong></td>
                        <td>5 người</td>
                        <td>12 tháng</td>
                        <td>$180,000</td>
                    </tr>
                    <tr>
                        <td><strong>Frontend Team</strong></td>
                        <td>4 người</td>
                        <td>12 tháng</td>
                        <td>$144,000</td>
                    </tr>
                    <tr>
                        <td><strong>Mobile Team</strong></td>
                        <td>4 người</td>
                        <td>12 tháng</td>
                        <td>$120,000</td>
                    </tr>
                    <tr>
                        <td><strong>QA Team</strong></td>
                        <td>2 người</td>
                        <td>12 tháng</td>
                        <td>$60,000</td>
                    </tr>
                    <tr>
                        <td><strong>Management</strong></td>
                        <td>2 người</td>
                        <td>12 tháng</td>
                        <td>$72,000</td>
                    </tr>
                </tbody>
            </table>

            <h3>🔧 Infrastructure Costs</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>☁️ Cloud Hosting</h4>
                    <p><strong>$36,000</strong> - AWS/Azure services, auto-scaling, load balancing</p>
                </div>
                <div class="feature-card">
                    <h4>🛠️ Development Tools</h4>
                    <p><strong>$24,000</strong> - IDEs, CI/CD, monitoring, security tools</p>
                </div>
                <div class="feature-card">
                    <h4>🔌 Third-party Services</h4>
                    <p><strong>$18,000</strong> - SMS, email, push notifications, analytics</p>
                </div>
                <div class="feature-card">
                    <h4>💻 Hardware & Equipment</h4>
                    <p><strong>$30,000</strong> - Development machines, testing devices</p>
                </div>
            </div>
        </div>

        <div class="section" id="benefits">
            <h2>9. Lợi Ích & ROI</h2>

            <h3>🎯 Lợi Ích Cho Các Bên</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏛️ Cơ Quan Quản Lý</h4>
                    <ul>
                        <li>Quản lý tập trung và thống nhất</li>
                        <li>Báo cáo real-time và chính xác</li>
                        <li>Giảm thiểu công việc thủ công</li>
                        <li>Minh bạch trong quy trình</li>
                        <li>Dễ dàng mở rộng quy mô</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👨‍🏫 Huấn Luyện Viên</h4>
                    <ul>
                        <li>Quản lý học viên hiệu quả</li>
                        <li>Điểm danh và báo cáo tự động</li>
                        <li>Theo dõi tiến độ chi tiết</li>
                        <li>Liên lạc dễ dàng với phụ huynh</li>
                        <li>Tài liệu giảng dạy phong phú</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👤 Học Viên</h4>
                    <ul>
                        <li>Trải nghiệm học tập hiện đại</li>
                        <li>Theo dõi tiến độ cá nhân</li>
                        <li>Tài liệu học tập đa dạng</li>
                        <li>Chứng chỉ có thể xác thực</li>
                        <li>Kết nối cộng đồng võ thuật</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👨‍👩‍👧‍👦 Phụ Huynh</h4>
                    <ul>
                        <li>Theo dõi con học võ</li>
                        <li>Thông báo điểm danh real-time</li>
                        <li>Liên lạc trực tiếp với HLV</li>
                        <li>Thanh toán học phí tiện lợi</li>
                        <li>Xem tiến độ và thành tích</li>
                    </ul>
                </div>
            </div>

            <h3>📊 ROI Analysis</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">60%</div>
                    <div class="stat-label">Giảm thời gian quản lý</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">40%</div>
                    <div class="stat-label">Tăng hiệu quả đào tạo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">80%</div>
                    <div class="stat-label">Giảm lỗi thủ công</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2 năm</div>
                    <div class="stat-label">Payback Period</div>
                </div>
            </div>

            <h3>🚀 Success Metrics</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Target</th>
                        <th>Timeline</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>User Adoption</strong></td>
                        <td>>80% trong 3 tháng</td>
                        <td>Post-launch</td>
                    </tr>
                    <tr>
                        <td><strong>System Uptime</strong></td>
                        <td>>99.5%</td>
                        <td>Ongoing</td>
                    </tr>
                    <tr>
                        <td><strong>Response Time</strong></td>
                        <td><2 seconds</td>
                        <td>Performance</td>
                    </tr>
                    <tr>
                        <td><strong>Bug Reports</strong></td>
                        <td><10/week sau 1 tháng</td>
                        <td>Quality</td>
                    </tr>
                    <tr>
                        <td><strong>User Satisfaction</strong></td>
                        <td>>4.5/5 stars</td>
                        <td>User feedback</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px;">
            <h3 style="color: white; margin-top: 0;">🥋 Hệ Thống Quản Lý Học Viên Võ Thuật</h3>
            <p style="font-size: 1.1em; margin-bottom: 0;">Giải Pháp Toàn Diện Cho Võ Thuật Quốc Gia</p>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; color: #666;">
            <p><strong>📄 Tài liệu được tạo bởi Augment Agent</strong></p>
            <p>🗓️ Ngày tạo: 14/07/2025 | 📋 Version: 1.0 - Complete Analysis</p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
