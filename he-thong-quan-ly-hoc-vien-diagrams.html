<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H<PERSON> 3 Cấp - Diagrams & Workflows</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 4px solid #667eea;
            padding-bottom: 30px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: -40px -40px 40px -40px;
            padding: 40px;
            border-radius: 15px 15px 0 0;
        }
        .header h1 {
            font-size: 2.8em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header .subtitle {
            font-size: 1.3em;
            margin-top: 15px;
            opacity: 0.9;
        }
        .diagram-section {
            margin-bottom: 60px;
            page-break-inside: avoid;
        }
        .diagram-section h2 {
            color: #667eea;
            border-left: 6px solid #667eea;
            padding-left: 20px;
            font-size: 2em;
            margin-bottom: 25px;
            background: linear-gradient(90deg, #f8f9ff, transparent);
            padding: 15px 20px;
            border-radius: 0 10px 10px 0;
        }
        .diagram-section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .diagram-container {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            position: relative;
            overflow-x: auto;
        }
        .diagram-title {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            position: absolute;
            top: -15px;
            left: 30px;
            z-index: 10;
        }
        .mermaid {
            margin-top: 30px;
            text-align: center;
        }
        .description {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
        }
        .description h4 {
            color: #856404;
            margin-top: 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .feature-card h4 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.2em;
        }
        .toc {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc h3 {
            color: #667eea;
            margin-top: 0;
        }
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        .toc li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .toc a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .toc a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
            .diagram-section {
                page-break-inside: avoid;
            }
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header {
                margin: -20px -20px 30px -20px;
                padding: 30px 20px;
            }
            .diagram-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🥋 Hệ Thống Quản Lý Học Viên 3 Cấp</h1>
            <div class="subtitle">Diagrams & Workflows - Võ Thuật Quốc Gia</div>
        </div>

        <div class="toc">
            <h3>📋 Mục Lục Diagrams</h3>
            <ul>
                <li><a href="#org-structure">1. Cấu Trúc Tổ Chức 3 Cấp</a></li>
                <li><a href="#unit-registration">2. Quy Trình Đăng Ký Đơn Vị</a></li>
                <li><a href="#student-registration">3. Quy Trình Đăng Ký Học Viên</a></li>
                <li><a href="#martial-arts-registration">4. Quy Trình Đăng Ký Môn Võ</a></li>
                <li><a href="#transfer-process">5. Quy Trình Chuyển Đơn Vị (2 Cấp Duyệt)</a></li>
                <li><a href="#exam-process">6. Quy Trình Tổ Chức Kì Thi</a></li>
                <li><a href="#certificate-system">7. Hệ Thống Quản Lý Chứng Chỉ</a></li>
                <li><a href="#student-history">8. Hệ Thống Quản Lý Lịch Sử Học Viên</a></li>
                <li><a href="#attendance-system">9. Quy Trình Điểm Danh</a></li>
                <li><a href="#reporting-system">10. Hệ Thống Báo Cáo Đa Cấp</a></li>
                <li><a href="#platform-distribution">11. Phân Bổ Chức Năng Mobile vs Website</a></li>
                <li><a href="#development-timeline">12. Kế Hoạch Triển Khai</a></li>
            </ul>
        </div>

        <div class="diagram-section" id="org-structure">
            <h2>1. Cấu Trúc Tổ Chức 3 Cấp</h2>
            <div class="diagram-container">
                <div class="diagram-title">Hierarchy Tổ Chức Võ Thuật Quốc Gia</div>
                <div class="mermaid">
graph TD
    subgraph "CẤP QUỐC GIA"
        NATIONAL[Tổng Cục Võ Thuật<br/>Quốc Gia]
        NATIONAL_DEPT[Ban Ngành Quốc Gia<br/>- Ban Thi Đấu<br/>- Ban Đào Tạo<br/>- Ban Kỹ Thuật]
    end
    
    subgraph "CẤP THÀNH PHỐ/TỈNH"
        CITY1[Sở Võ Thuật<br/>TP.HCM]
        CITY2[Sở Võ Thuật<br/>Hà Nội]
        CITY3[Sở Võ Thuật<br/>Đà Nẵng]
        CITY_DEPT[Ban Ngành Thành Phố<br/>- Ban Thi Đấu TP<br/>- Ban Đào Tạo TP<br/>- Ban Kỹ Thuật TP]
    end
    
    subgraph "CẤP PHƯỜNG/XÃ"
        WARD1[Phòng Võ Thuật<br/>Quận 1]
        WARD2[Phòng Võ Thuật<br/>Quận 2]
        WARD3[Phòng Võ Thuật<br/>Huyện A]
        WARD_DEPT[Ban Ngành Phường/Xã<br/>- Đội Thi Đấu<br/>- Nhóm Đào Tạo<br/>- Nhóm Kỹ Thuật]
    end
    
    subgraph "ĐƠN VỊ CƠ SỞ"
        CLUB1[Câu Lạc Bộ A]
        CLUB2[Câu Lạc Bộ B]
        SCHOOL1[Trường Võ A]
        SCHOOL2[Trường Võ B]
    end
    
    NATIONAL --> NATIONAL_DEPT
    NATIONAL --> CITY1
    NATIONAL --> CITY2
    NATIONAL --> CITY3
    
    CITY1 --> CITY_DEPT
    CITY1 --> WARD1
    CITY1 --> WARD2
    
    CITY2 --> WARD3
    
    WARD1 --> WARD_DEPT
    WARD1 --> CLUB1
    WARD1 --> SCHOOL1
    
    WARD2 --> CLUB2
    WARD2 --> SCHOOL2
    
    style NATIONAL fill:#e1f5fe
    style CITY1 fill:#f3e5f5
    style WARD1 fill:#e8f5e8
    style CLUB1 fill:#fff3e0
                </div>
            </div>
            <div class="description">
                <h4>🏛️ Mô Tả Cấu Trúc</h4>
                <p><strong>3 Cấp Chính:</strong> Quốc gia → Thành phố/Tỉnh → Phường/Xã → Đơn vị cơ sở</p>
                <p><strong>Ban Ngành:</strong> Mỗi cấp có các ban chuyên môn (Thi đấu, Đào tạo, Kỹ thuật)</p>
                <p><strong>Đơn vị cơ sở:</strong> Câu lạc bộ, Trường võ trực thuộc Phường/Xã</p>
            </div>
        </div>

        <div class="diagram-section" id="unit-registration">
            <h2>2. Quy Trình Đăng Ký Đơn Vị</h2>
            <div class="diagram-container">
                <div class="diagram-title">Workflow Đăng Ký Đơn Vị Theo Cấp Độ</div>
                <div class="mermaid">
flowchart TD
    START[Đơn vị muốn đăng ký] --> LEVEL{Xác định cấp độ}
    
    LEVEL -->|Cấp Thành phố| CITY_REG[Đăng ký Sở Võ Thuật<br/>Thành phố/Tỉnh]
    LEVEL -->|Cấp Phường/Xã| WARD_REG[Đăng ký Phòng Võ Thuật<br/>Phường/Xã]
    LEVEL -->|Câu lạc bộ| CLUB_REG[Đăng ký Câu lạc bộ/<br/>Trường võ]
    
    CITY_REG --> CITY_FORM[Điền form đăng ký<br/>- Tên đơn vị<br/>- Địa chỉ trụ sở<br/>- Người đại diện<br/>- Giấy phép hoạt động]
    
    WARD_REG --> WARD_FORM[Điền form đăng ký<br/>- Tên phòng võ<br/>- Địa chỉ<br/>- Thuộc Sở nào<br/>- Người phụ trách]
    
    CLUB_REG --> CLUB_FORM[Điền form đăng ký<br/>- Tên CLB/Trường<br/>- Địa chỉ<br/>- Thuộc Phòng nào<br/>- Huấn luyện viên trưởng]
    
    CITY_FORM --> NATIONAL_REVIEW{Super Admin<br/>Duyệt đơn}
    WARD_FORM --> CITY_REVIEW{City Admin<br/>Duyệt đơn}
    CLUB_FORM --> WARD_REVIEW{Ward Admin<br/>Duyệt đơn}
    
    NATIONAL_REVIEW -->|Approved| CITY_ACTIVE[Kích hoạt Sở<br/>Cấp tài khoản Admin]
    CITY_REVIEW -->|Approved| WARD_ACTIVE[Kích hoạt Phòng<br/>Cấp tài khoản Admin]
    WARD_REVIEW -->|Approved| CLUB_ACTIVE[Kích hoạt CLB<br/>Cấp tài khoản quản lý]
    
    style NATIONAL_REVIEW fill:#e1f5fe
    style CITY_REVIEW fill:#f3e5f5
    style WARD_REVIEW fill:#e8f5e8
    style CITY_ACTIVE fill:#c8e6c9
    style WARD_ACTIVE fill:#c8e6c9
    style CLUB_ACTIVE fill:#c8e6c9
                </div>
            </div>
        </div>

        <div class="diagram-section" id="student-registration">
            <h2>3. Quy Trình Đăng Ký Học Viên</h2>
            <div class="diagram-container">
                <div class="diagram-title">Student Registration Process</div>
                <div class="mermaid">
flowchart TD
    START[Học viên muốn đăng ký] --> CHOOSE_UNIT[Chọn đơn vị đào tạo<br/>Câu lạc bộ/Trường võ]
    
    CHOOSE_UNIT --> PERSONAL_INFO[Điền thông tin cá nhân<br/>- Họ tên đầy đủ<br/>- Ngày sinh, giới tính<br/>- Địa chỉ thường trú<br/>- Số điện thoại<br/>- Email<br/>- Số CCCD/CMND<br/>- Ghi chú đặc biệt]
    
    PERSONAL_INFO --> CHOOSE_MARTIAL[Chọn môn võ<br/>- Karate<br/>- Taekwondo<br/>- Vovinam<br/>- Judo<br/>- Kickboxing<br/>- Khác...]
    
    CHOOSE_MARTIAL --> INSTRUCTOR_REVIEW{Huấn luyện viên<br/>Xem xét đơn}
    
    INSTRUCTOR_REVIEW -->|Approved| WARD_REVIEW{Ward Admin<br/>Phê duyệt}
    
    WARD_REVIEW -->|Approved| CREATE_PROFILE[Tạo hồ sơ học viên<br/>- Cấp mã học viên<br/>- Tạo tài khoản<br/>- Phân lớp học]
    
    CREATE_PROFILE --> WELCOME[Hoàn tất đăng ký<br/>- Gửi thông tin lớp học<br/>- Hướng dẫn sử dụng app<br/>- Lịch học đầu tiên]
    
    style INSTRUCTOR_REVIEW fill:#fff3e0
    style WARD_REVIEW fill:#e8f5e8
    style CREATE_PROFILE fill:#c8e6c9
    style WELCOME fill:#c8e6c9
                </div>
            </div>
        </div>

        <div class="diagram-section" id="martial-arts-registration">
            <h2>4. Quy Trình Đăng Ký Môn Võ</h2>
            <div class="diagram-container">
                <div class="diagram-title">Martial Arts Registration Workflow</div>
                <div class="mermaid">
flowchart TD
    START[Đơn vị muốn mở môn võ mới] --> CHECK_LEVEL{Kiểm tra cấp độ<br/>đơn vị}

    CHECK_LEVEL -->|Câu lạc bộ| CLUB_REQUEST[CLB đề xuất môn võ mới<br/>- Tên môn võ<br/>- Lý do mở môn<br/>- HLV phụ trách<br/>- Kế hoạch đào tạo]

    CHECK_LEVEL -->|Phường/Xã| WARD_REQUEST[Phòng đề xuất môn võ<br/>- Môn võ trọng điểm<br/>- Phạm vi triển khai<br/>- Đội ngũ HLV<br/>- Ngân sách]

    CHECK_LEVEL -->|Thành phố| CITY_REQUEST[Sở đề xuất môn võ<br/>- Môn võ mới toàn thành phố<br/>- Kế hoạch phát triển<br/>- Đào tạo HLV<br/>- Tài trợ]

    CLUB_REQUEST --> WARD_APPROVE{Ward Admin<br/>Phê duyệt}
    WARD_REQUEST --> CITY_APPROVE{City Admin<br/>Phê duyệt}
    CITY_REQUEST --> NATIONAL_APPROVE{Super Admin<br/>Phê duyệt}

    WARD_APPROVE -->|Approved| CLUB_SETUP[Setup môn võ cho CLB<br/>- Tạo lớp học<br/>- Phân công HLV<br/>- Mở đăng ký]
    CITY_APPROVE -->|Approved| WARD_SETUP[Setup môn võ cho Phòng<br/>- Triển khai các CLB<br/>- Đào tạo HLV<br/>- Giám sát chất lượng]
    NATIONAL_APPROVE -->|Approved| CITY_SETUP[Setup môn võ cho Sở<br/>- Chính sách hỗ trợ<br/>- Đào tạo chuyên gia<br/>- Quảng bá rộng rãi]

    style WARD_APPROVE fill:#e8f5e8
    style CITY_APPROVE fill:#f3e5f5
    style NATIONAL_APPROVE fill:#e1f5fe
    style CLUB_SETUP fill:#c8e6c9
    style WARD_SETUP fill:#c8e6c9
    style CITY_SETUP fill:#c8e6c9
                </div>
            </div>
        </div>

        <div class="diagram-section" id="transfer-process">
            <h2>5. Quy Trình Chuyển Đơn Vị (2 Cấp Duyệt)</h2>
            <div class="diagram-container">
                <div class="diagram-title">Student Transfer Process với 2-Level Approval</div>
                <div class="mermaid">
flowchart TD
    START[Học viên muốn chuyển đơn vị] --> REASON[Chọn lý do chuyển<br/>- Chuyển nơi ở<br/>- Không phù hợp HLV<br/>- Thay đổi lịch học<br/>- Lý do khác]

    REASON --> SELECT_NEW[Chọn đơn vị mới<br/>- Tìm kiếm theo khu vực<br/>- Xem thông tin CLB<br/>- Kiểm tra lịch học<br/>- Liên hệ HLV]

    SELECT_NEW --> FILL_FORM[Điền đơn xin chuyển<br/>- Thông tin cá nhân<br/>- Đơn vị hiện tại<br/>- Đơn vị muốn chuyển đến<br/>- Lý do chi tiết<br/>- Ngày mong muốn]

    FILL_FORM --> CURRENT_INSTRUCTOR{HLV hiện tại<br/>Xác nhận}

    CURRENT_INSTRUCTOR -->|Approved| CURRENT_ADMIN{Admin đơn vị cũ<br/>Duyệt cấp 1}

    CURRENT_ADMIN -->|Approved| NEW_INSTRUCTOR{HLV đơn vị mới<br/>Xác nhận nhận}

    NEW_INSTRUCTOR -->|Approved| NEW_ADMIN{Admin đơn vị mới<br/>Duyệt cấp 2}

    NEW_ADMIN -->|Approved| TRANSFER_PROCESS[Xử lý chuyển đơn vị<br/>- Cập nhật hồ sơ<br/>- Chuyển lịch sử học tập<br/>- Thông báo các bên<br/>- Tạo mã học viên mới]

    TRANSFER_PROCESS --> WELCOME_NEW[Chào mừng tại đơn vị mới<br/>- Giới thiệu lớp học<br/>- Hướng dẫn quy định<br/>- Lịch tập đầu tiên<br/>- Hỗ trợ hòa nhập]

    style CURRENT_INSTRUCTOR fill:#fff3e0
    style CURRENT_ADMIN fill:#e8f5e8
    style NEW_INSTRUCTOR fill:#fff3e0
    style NEW_ADMIN fill:#f3e5f5
    style TRANSFER_PROCESS fill:#c8e6c9
    style WELCOME_NEW fill:#c8e6c9
                </div>
            </div>
        </div>

        <div class="diagram-section" id="exam-process">
            <h2>6. Quy Trình Tổ Chức Kì Thi</h2>
            <div class="diagram-container">
                <div class="diagram-title">Exam Organization & Belt Promotion Process</div>
                <div class="mermaid">
flowchart TD
    START[Đơn vị tổ chức kì thi] --> PLAN[Lập kế hoạch thi<br/>- Xác định cấp độ thi<br/>- Thời gian và địa điểm<br/>- Số lượng thí sinh dự kiến<br/>- Hội đồng thi]

    PLAN --> APPROVAL{Xin phê duyệt<br/>theo cấp độ}

    APPROVAL -->|Thi cấp CLB| CLUB_APPROVE[Ward Admin phê duyệt<br/>- Kiểm tra điều kiện<br/>- Xác nhận hội đồng<br/>- Cấp phép tổ chức]

    APPROVAL -->|Thi cấp Phường| WARD_APPROVE[City Admin phê duyệt<br/>- Đánh giá năng lực<br/>- Phân công giám khảo<br/>- Chuẩn bị tài liệu]

    APPROVAL -->|Thi cấp Thành phố| CITY_APPROVE[Super Admin phê duyệt<br/>- Xem xét toàn diện<br/>- Cử chuyên gia<br/>- Đảm bảo chất lượng]

    CLUB_APPROVE --> CONDUCT_EXAM[Tiến hành thi<br/>- Kiểm tra danh tính<br/>- Thi lý thuyết<br/>- Thi thực hành<br/>- Đánh giá kỹ năng]
    WARD_APPROVE --> CONDUCT_EXAM
    CITY_APPROVE --> CONDUCT_EXAM

    CONDUCT_EXAM --> EVALUATE[Chấm thi và đánh giá<br/>- Hội đồng chấm thi<br/>- Tính điểm tổng hợp<br/>- Xếp loại kết quả<br/>- Biên bản kết quả]

    EVALUATE --> RESULTS{Kết quả thi}

    RESULTS -->|Đạt| PASS[Thí sinh đạt<br/>- Cập nhật đẳng cấp<br/>- Chuẩn bị chứng chỉ<br/>- Thông báo kết quả<br/>- Lên lịch lễ trao]

    RESULTS -->|Không đạt| FAIL[Thí sinh không đạt<br/>- Thông báo kết quả<br/>- Phản hồi điểm yếu<br/>- Hướng dẫn cải thiện<br/>- Lịch thi lại]

    PASS --> CERTIFICATE[Cấp chứng chỉ<br/>- In chứng chỉ/bằng<br/>- Ký xác nhận<br/>- Đóng dấu chính thức<br/>- Cập nhật hệ thống]

    style CLUB_APPROVE fill:#e8f5e8
    style WARD_APPROVE fill:#f3e5f5
    style CITY_APPROVE fill:#e1f5fe
    style PASS fill:#c8e6c9
    style CERTIFICATE fill:#c8e6c9
                </div>
            </div>
        </div>

        <div class="diagram-section" id="certificate-system">
            <h2>7. Hệ Thống Quản Lý Chứng Chỉ</h2>
            <div class="diagram-container">
                <div class="diagram-title">Certificate Management & Belt Ranking System</div>
                <div class="mermaid">
graph TD
    subgraph "LOẠI CHỨNG CHỈ"
        BELT[Chứng Chỉ Đai<br/>- Đai trắng → đen<br/>- Theo từng môn võ<br/>- Có thời hạn]
        SKILL[Chứng Chỉ Kỹ Năng<br/>- Tự vệ<br/>- Trọng tài<br/>- Huấn luyện viên]
        COMPETE[Chứng Chỉ Thi Đấu<br/>- Vô địch các cấp<br/>- Thành tích thi đấu<br/>- Huy chương]
    end

    subgraph "QUY TRÌNH CẤP CHỨNG CHỈ"
        EXAM[Thi cử đạt yêu cầu] --> VERIFY[Xác minh kết quả<br/>Hội đồng thi]
        VERIFY --> GENERATE[Tạo chứng chỉ<br/>Template + Thông tin]
        GENERATE --> APPROVE[Phê duyệt cấp có thẩm quyền<br/>Ký và đóng dấu]
        APPROVE --> PRINT[In chứng chỉ<br/>Giấy chuyên dụng]
        PRINT --> CEREMONY[Lễ trao chứng chỉ<br/>Ghi nhận thành tích]
    end

    subgraph "QUẢN LÝ VÀ THEO DÕI"
        DATABASE[Database chứng chỉ<br/>- Mã số duy nhất<br/>- Thông tin chi tiết<br/>- Trạng thái hiệu lực]
        HISTORY[Lịch sử học viên<br/>- Timeline đẳng cấp<br/>- Chứng chỉ đã có<br/>- Tiến độ học tập]
        RENEWAL[Gia hạn/Cập nhật<br/>- Kiểm tra định kỳ<br/>- Đào tạo bổ sung<br/>- Duy trì trình độ]
    end

    subgraph "XÁC THỰC VÀ TRA CỨU"
        QR[QR Code trên chứng chỉ<br/>Link đến database]
        VERIFY_ONLINE[Tra cứu online<br/>Xác thực tính hợp lệ]
        API[API cho đối tác<br/>Kiểm tra chứng chỉ]
    end

    BELT --> EXAM
    SKILL --> EXAM
    COMPETE --> EXAM

    CEREMONY --> DATABASE
    DATABASE --> HISTORY
    DATABASE --> RENEWAL

    DATABASE --> QR
    QR --> VERIFY_ONLINE
    VERIFY_ONLINE --> API

    style EXAM fill:#fff3e0
    style APPROVE fill:#f3e5f5
    style CEREMONY fill:#c8e6c9
    style DATABASE fill:#e1f5fe
                </div>
            </div>
        </div>
        <div class="diagram-section" id="student-history">
            <h2>8. Hệ Thống Quản Lý Lịch Sử Học Viên</h2>
            <div class="diagram-container">
                <div class="diagram-title">Student Learning History Management System</div>
                <div class="mermaid">
graph TD
    subgraph "THÔNG TIN HỌC VIÊN"
        PROFILE[Hồ Sơ Cá Nhân<br/>- Thông tin cơ bản<br/>- Ảnh đại diện<br/>- Liên hệ khẩn cấp<br/>- Tình trạng sức khỏe]

        CURRENT[Trạng Thái Hiện Tại<br/>- Đơn vị đang học<br/>- Môn võ chính<br/>- Đẳng cấp hiện tại<br/>- HLV phụ trách]
    end

    subgraph "LỊCH SỬ THI CỬ"
        EXAM_HISTORY[Lịch Sử Thi<br/>- Ngày thi<br/>- Loại thi<br/>- Kết quả<br/>- Điểm số<br/>- Nhận xét]

        CERTIFICATES[Chứng Chỉ Đạt Được<br/>- Loại chứng chỉ<br/>- Ngày cấp<br/>- Cơ quan cấp<br/>- Trạng thái hiệu lực<br/>- File PDF]

        BELT_PROGRESS[Tiến Độ Đai<br/>- Timeline lên đai<br/>- Thời gian mỗi cấp<br/>- Đánh giá HLV<br/>- Mục tiêu tiếp theo]
    end

    subgraph "LỊCH SỬ CHUYỂN ĐƠN VỊ"
        TRANSFER_LOG[Nhật Ký Chuyển<br/>- Đơn vị cũ → mới<br/>- Ngày chuyển<br/>- Lý do chuyển<br/>- Người phê duyệt]

        ADAPTATION[Thích Ứng Mới<br/>- Đánh giá ban đầu<br/>- Tiến độ hòa nhập<br/>- Phản hồi HLV<br/>- Hỗ trợ cần thiết]
    end

    subgraph "ĐIỂM DANH & THAM GIA"
        ATTENDANCE[Điểm Danh Lớp Học<br/>- Ngày tham gia<br/>- Thời gian<br/>- Trạng thái<br/>- Ghi chú]

        TEAM_PARTICIPATION[Tham Gia Đội Tuyển<br/>- Đội tuyển thi đấu<br/>- Vai trò<br/>- Thành tích<br/>- Đánh giá]

        STATISTICS[Thống Kê Tham Gia<br/>- Tỷ lệ có mặt<br/>- Xu hướng<br/>- So sánh<br/>- Cảnh báo]
    end

    PROFILE --> EXAM_HISTORY
    CURRENT --> BELT_PROGRESS

    EXAM_HISTORY --> CERTIFICATES
    CERTIFICATES --> BELT_PROGRESS

    CURRENT --> TRANSFER_LOG
    TRANSFER_LOG --> ADAPTATION

    CURRENT --> ATTENDANCE
    ATTENDANCE --> TEAM_PARTICIPATION
    ATTENDANCE --> STATISTICS

    style PROFILE fill:#e1f5fe
    style EXAM_HISTORY fill:#f3e5f5
    style TRANSFER_LOG fill:#fff3e0
    style ATTENDANCE fill:#e8f5e8
                </div>
            </div>
        </div>

        <div class="diagram-section" id="attendance-system">
            <h2>9. Quy Trình Điểm Danh</h2>
            <div class="diagram-container">
                <div class="diagram-title">Attendance Management System</div>
                <div class="mermaid">
flowchart TD
    START[Bắt đầu buổi học/tập] --> METHOD{Chọn phương thức<br/>điểm danh}

    METHOD -->|QR Code| QR_SCAN[Học viên scan QR<br/>- Mở app<br/>- Scan mã lớp học<br/>- Xác nhận vị trí<br/>- Ghi nhận thời gian]

    METHOD -->|Manual| MANUAL[HLV điểm danh thủ công<br/>- Danh sách lớp<br/>- Tick có mặt/vắng<br/>- Ghi chú lý do<br/>- Cập nhật hệ thống]

    METHOD -->|NFC/RFID| NFC[Thẻ học viên NFC<br/>- Chạm thẻ vào reader<br/>- Tự động ghi nhận<br/>- Hiển thị thông tin<br/>- Xác nhận tham gia]

    QR_SCAN --> VERIFY[Xác minh thông tin<br/>- Kiểm tra học viên<br/>- Xác nhận lớp học<br/>- Validate thời gian<br/>- Check vị trí GPS]

    MANUAL --> VERIFY
    NFC --> VERIFY

    VERIFY --> RECORD[Ghi nhận điểm danh<br/>- Cập nhật database<br/>- Timestamp chính xác<br/>- Trạng thái tham gia<br/>- Ghi chú đặc biệt]

    RECORD --> NOTIFY[Thông báo<br/>- Xác nhận cho học viên<br/>- Cập nhật cho HLV<br/>- Thông báo phụ huynh<br/>- Sync với hệ thống]

    NOTIFY --> ANALYTICS[Phân tích dữ liệu<br/>- Tỷ lệ tham gia<br/>- Xu hướng vắng mặt<br/>- Cảnh báo rủi ro<br/>- Báo cáo định kỳ]

    subgraph "TEAM ATTENDANCE"
        TEAM_SELECT[Chọn đội tuyển<br/>- Đội thi đấu<br/>- Nhóm luyện tập<br/>- Lớp đặc biệt<br/>- Khóa huấn luyện]

        TEAM_TRACK[Theo dõi đội tuyển<br/>- Điểm danh thành viên<br/>- Vai trò trong đội<br/>- Hiệu suất tập luyện<br/>- Chuẩn bị thi đấu]
    end

    RECORD --> TEAM_SELECT
    TEAM_SELECT --> TEAM_TRACK

    style QR_SCAN fill:#e8f5e8
    style MANUAL fill:#fff3e0
    style NFC fill:#f3e5f5
    style VERIFY fill:#e1f5fe
    style RECORD fill:#c8e6c9
    style TEAM_TRACK fill:#fce4ec
                </div>
            </div>
        </div>

        <div class="diagram-section" id="reporting-system">
            <h2>10. Hệ Thống Báo Cáo Đa Cấp</h2>
            <div class="diagram-container">
                <div class="diagram-title">Multi-Level Reporting Dashboard System</div>
                <div class="mermaid">
graph TD
    subgraph "CẤP QUỐC GIA - SUPER ADMIN"
        NATIONAL_DASH[Dashboard Quốc Gia<br/>- Tổng số học viên toàn quốc<br/>- Phân bố theo vùng miền<br/>- Tăng trưởng theo tháng/năm<br/>- Top môn võ phổ biến]

        NATIONAL_REPORTS[Báo Cáo Quốc Gia<br/>- Báo cáo thường niên<br/>- Thống kê thi cử<br/>- Phân tích xu hướng<br/>- Đề xuất chính sách]
    end

    subgraph "CẤP THÀNH PHỐ - CITY ADMIN"
        CITY_DASH[Dashboard Thành Phố<br/>- Học viên trong thành phố<br/>- So sánh với các thành phố khác<br/>- Hiệu quả các phường/xã<br/>- Chất lượng đào tạo]

        CITY_REPORTS[Báo Cáo Thành Phố<br/>- Báo cáo quý/năm<br/>- Đánh giá đơn vị trực thuộc<br/>- Kế hoạch phát triển<br/>- Ngân sách và đầu tư]
    end

    subgraph "CẤP PHƯỜNG/XÃ - WARD ADMIN"
        WARD_DASH[Dashboard Phường/Xã<br/>- Học viên các CLB<br/>- Tỷ lệ tham gia<br/>- Thành tích thi cử<br/>- Hoạt động đào tạo]

        WARD_REPORTS[Báo Cáo Phường/Xã<br/>- Báo cáo tháng<br/>- Đánh giá CLB<br/>- Vấn đề và giải pháp<br/>- Đề xuất hỗ trợ]
    end

    subgraph "CẤP CLB - INSTRUCTOR"
        CLUB_DASH[Dashboard CLB<br/>- Học viên lớp học<br/>- Điểm danh và tiến độ<br/>- Chuẩn bị thi cử<br/>- Phản hồi phụ huynh]

        CLUB_REPORTS[Báo Cáo CLB<br/>- Báo cáo tuần<br/>- Tiến độ cá nhân<br/>- Kế hoạch luyện tập<br/>- Đề xuất cải thiện]
    end

    NATIONAL_DASH --> NATIONAL_REPORTS
    CITY_DASH --> CITY_REPORTS
    WARD_DASH --> WARD_REPORTS
    CLUB_DASH --> CLUB_REPORTS

    CITY_REPORTS --> NATIONAL_REPORTS
    WARD_REPORTS --> CITY_REPORTS
    CLUB_REPORTS --> WARD_REPORTS

    style NATIONAL_DASH fill:#e1f5fe
    style CITY_DASH fill:#f3e5f5
    style WARD_DASH fill:#e8f5e8
    style CLUB_DASH fill:#fff3e0
                </div>
            </div>
        </div>
        <div class="diagram-section" id="platform-distribution">
            <h2>11. Phân Bổ Chức Năng Mobile vs Website</h2>
            <div class="diagram-container">
                <div class="diagram-title">Platform Feature Distribution</div>
                <div class="mermaid">
graph TD
    subgraph "MOBILE APP - Ưu tiên cao"
        MOBILE_STUDENT[Học Viên Mobile<br/>📱 Chức năng chính<br/>- Điểm danh QR/NFC<br/>- Xem lịch học<br/>- Tải tài liệu<br/>- Thông báo push<br/>- Chat với HLV<br/>- Đăng ký thi cử<br/>- Xem chứng chỉ<br/>- GPS đến lớp học]

        MOBILE_INSTRUCTOR[HLV Mobile<br/>📱 Quản lý lớp học<br/>- Điểm danh nhanh<br/>- Cập nhật tiến độ<br/>- Chụp ảnh/video<br/>- Thông báo khẩn<br/>- Liên hệ phụ huynh<br/>- Báo cáo nhanh<br/>- Lịch dạy cá nhân<br/>- Offline mode]

        MOBILE_PARENT[Phụ Huynh Mobile<br/>📱 Theo dõi con<br/>- Thông báo điểm danh<br/>- Xem tiến độ học<br/>- Lịch thi cử<br/>- Thanh toán học phí<br/>- Chat với HLV<br/>- Xem ảnh/video<br/>- Đăng ký sự kiện<br/>- Emergency contact]
    end

    subgraph "WEBSITE - Quản lý & Tra cứu"
        WEB_ADMIN[Admin Website<br/>💻 Quản lý hệ thống<br/>- Dashboard analytics<br/>- Quản lý người dùng<br/>- Báo cáo chi tiết<br/>- Cấu hình hệ thống<br/>- Import/Export data<br/>- Backup & restore<br/>- Audit logs<br/>- System monitoring]

        WEB_PUBLIC[Public Website<br/>🌐 Tra cứu công khai<br/>- Tìm kiếm học viên<br/>- Xác thực chứng chỉ<br/>- Danh sách CLB<br/>- Lịch thi công khai<br/>- Tin tức sự kiện<br/>- Liên hệ hỗ trợ<br/>- FAQ & hướng dẫn<br/>- Download forms]

        WEB_INSTRUCTOR[HLV Website<br/>💻 Quản lý nâng cao<br/>- Lập kế hoạch dài hạn<br/>- Phân tích chi tiết<br/>- Tạo tài liệu<br/>- Quản lý thi cử<br/>- Báo cáo tổng hợp<br/>- Training materials<br/>- Video conferencing<br/>- Bulk operations]
    end

    subgraph "SHARED FEATURES"
        SHARED[Tính năng chung<br/>🔄 Sync giữa platforms<br/>- Authentication<br/>- Profile management<br/>- Notifications<br/>- Basic CRUD<br/>- Search & filter<br/>- File upload/download<br/>- Multi-language<br/>- Help & support]
    end

    MOBILE_STUDENT --> SHARED
    MOBILE_INSTRUCTOR --> SHARED
    MOBILE_PARENT --> SHARED

    WEB_ADMIN --> SHARED
    WEB_PUBLIC --> SHARED
    WEB_INSTRUCTOR --> SHARED

    style MOBILE_STUDENT fill:#e8f5e8
    style MOBILE_INSTRUCTOR fill:#fff3e0
    style MOBILE_PARENT fill:#f3e5f5
    style WEB_ADMIN fill:#e1f5fe
    style WEB_PUBLIC fill:#fce4ec
    style SHARED fill:#f5f5f5
                </div>
            </div>
        </div>

        <div class="diagram-section" id="development-timeline">
            <h2>12. Kế Hoạch Triển Khai</h2>
            <div class="diagram-container">
                <div class="diagram-title">Development Timeline - 18 Months</div>
                <div class="mermaid">
    gantt
        title Hệ Thống Quản Lý Học Viên - Development Timeline
        dateFormat  YYYY-MM-DD
        section Phase 1: Backend Foundation
        Backend Core Services     :p1-backend, 2024-01-01, 16w
        Database Design & Setup   :p1-db, 2024-01-01, 8w
        Authentication System     :p1-auth, after p1-db, 4w
        User Management          :p1-user, after p1-auth, 4w
        Basic APIs              :p1-api, after p1-user, 4w
        Testing & Documentation  :p1-test, after p1-api, 4w

        section Phase 2: Web Platform
        Admin Dashboard         :p2-admin, after p1-test, 8w
        Public Website         :p2-public, after p1-test, 6w
        Instructor Portal      :p2-instructor, after p2-public, 6w
        Reporting System       :p2-report, after p2-instructor, 4w
        Integration Testing    :p2-test, after p2-report, 2w

        section Phase 3: Mobile Apps
        Student Mobile App     :p3-student, after p2-test, 10w
        Instructor Mobile App  :p3-instructor, after p3-student, 6w
        Parent Mobile App      :p3-parent, after p3-instructor, 6w
        Mobile Testing        :p3-test, after p3-parent, 4w

        section Deployment & Launch
        Production Setup      :deploy, after p3-test, 2w
        User Training        :training, after deploy, 2w
        Go Live             :golive, after training, 1w
        Post-Launch Support  :support, after golive, 4w
                </div>
            </div>
            <div class="description">
                <h4>📊 Project Summary</h4>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>⏱️ Timeline</h4>
                        <p><strong>18 tháng</strong> development với 3 phases chính</p>
                    </div>
                    <div class="feature-card">
                        <h4>👥 Team Size</h4>
                        <p><strong>12-15 người</strong> developers, QA, và management</p>
                    </div>
                    <div class="feature-card">
                        <h4>💰 Budget</h4>
                        <p><strong>$721,000</strong> total project cost</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔧 Technology</h4>
                        <p><strong>Java Spring Boot</strong>, React, React Native, PostgreSQL</p>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px;">
            <h3 style="color: white; margin-top: 0;">🥋 Hệ Thống Quản Lý Học Viên Võ Thuật</h3>
            <p style="font-size: 1.1em; margin-bottom: 0;">Complete Workflow Documentation - 3-Level Management System</p>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; color: #666;">
            <p><strong>📄 Tài liệu được tạo bởi Augment Agent</strong></p>
            <p>🗓️ Ngày tạo: 14/07/2025 | 📋 Version: 1.0 - Complete Diagrams</p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
