# 📋 GIẢI THÍCH SOURCE CODE DỰ ÁN JAVA CIRCLELIFE

## 🏗️ TỔNG QUAN KIẾN TRÚC DỰ ÁN

Đây là một ứng dụng **Spring Boot 3** demo về quản lý người dùng và hồ sơ cá nhân, đ<PERSON><PERSON><PERSON> xây dựng theo mô hình **MVC** với các tính năng hiện đại.

## 📁 CẤU TRÚC THƯ MỤC

```
src/main/java/com/circlelife/
├── 📱 controller/          # Điều khiển HTTP requests
├── 🗃️ model/              # Các entity/model classes  
├── 🔧 service/            # Business logic layer
├── 📊 repository/         # Data access layer
├── ⚙️ config/             # Cấu hình ứng dụng
├── 📋 component/          # Các component khác
└── 🎯 JavaCircleLifeApplication.java  # Main class
```

## 🎯 MAIN APPLICATION CLASS

```java
@SpringBootApplication  // Kết hợp 3 annotation: @Configuration, @EnableAutoConfiguration, @ComponentScan
@EnableCaching         // Bật tính năng cache của Spring
@EnableScheduling      // Bật tính năng lập lịch tự động
@EnableAsync          // Bật tính năng xử lý bất đồng bộ
public class JavaCircleLifeApplication {
    public static void main(String[] args) {
        SpringApplication.run(JavaCircleLifeApplication.class, args);
        // In ra các URL quan trọng khi ứng dụng khởi động
    }
}
```

**Giải thích:**
- `@SpringBootApplication`: Annotation tổng hợp bao gồm @Configuration, @EnableAutoConfiguration, @ComponentScan
- `@EnableCaching`: Kích hoạt hệ thống cache của Spring
- `@EnableScheduling`: Cho phép chạy các task theo lịch
- `@EnableAsync`: Hỗ trợ xử lý bất đồng bộ

## 🗃️ MODEL LAYER - CÁC ENTITY

### User Entity (Người dùng)

```java
@Entity                    // Đánh dấu đây là JPA entity
@Table(name = "users",     // Tên bảng trong database
       uniqueConstraints = {
           @UniqueConstraint(columnNames = "email"),      // Email phải unique
           @UniqueConstraint(columnNames = "username")    // Username phải unique
       },
       indexes = {            // Tạo các index để tăng tốc truy vấn
           @Index(name = "idx_user_email", columnList = "email"),
           @Index(name = "idx_user_username", columnList = "username")
       })
@EntityListeners(AuditingEntityListener.class)  // Tự động cập nhật createdDate, lastModifiedDate
@SQLDelete(sql = "UPDATE users SET status = 'DELETED' WHERE user_id = ?")  // Soft delete
@Where(clause = "status != 'DELETED'")          // Chỉ lấy user chưa bị xóa
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)  // Auto increment ID
    private Long id;
    
    @NotBlank(message = "Username không được để trống")
    @Size(min = 3, max = 50, message = "Username phải từ 3-50 ký tự")
    private String username;
    
    @Email(message = "Email không hợp lệ")
    @NotBlank(message = "Email không được để trống")
    private String email;
    
    @Enumerated(EnumType.STRING)  // Lưu enum dưới dạng string
    private UserStatus status;
    
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference  // Tránh circular reference
    private List<UserProfile> profiles = new ArrayList<>();
}
```

### UserProfile Entity (Hồ sơ người dùng)

```java
@Entity
@Table(name = "user_profiles",
       indexes = {
           @Index(name = "idx_profile_user_id", columnList = "user_id"),    // Index cho foreign key
           @Index(name = "idx_profile_type", columnList = "profile_type")   // Index cho loại profile
       })
@EntityListeners(AuditingEntityListener.class)  // Tự động audit
public class UserProfile {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)  // Auto increment ID
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)  // Quan hệ nhiều-một với User
    @JoinColumn(name = "user_id", nullable = false)  // Foreign key
    @JsonBackReference  // Tránh circular reference khi serialize JSON
    private User user;
    
    @Enumerated(EnumType.STRING)
    private ProfileType profileType;
    
    @NotBlank(message = "Profile key không được để trống")
    private String profileKey;
    
    @Column(columnDefinition = "TEXT")  // Cho phép text dài
    private String profileValue;
}
```

## 📊 REPOSITORY LAYER - TRUY CẬP DỮ LIỆU

```java
@Repository  // Đánh dấu đây là Spring Data Repository
public interface UserRepository extends JpaRepository<User, Long> {

    // Spring Data JPA tự động tạo implementation từ tên method
    Optional<User> findByEmail(String email);           // Tìm theo email
    Optional<User> findByUsername(String username);     // Tìm theo username
    List<User> findByStatus(User.UserStatus status);    // Tìm theo trạng thái
    
    // Custom query với @Query annotation
    @Query("SELECT u FROM User u WHERE u.email = :email AND u.status = :status")
    Optional<User> findByEmailAndStatus(@Param("email") String email, 
                                       @Param("status") User.UserStatus status);
    
    // Native SQL query
    @Query(value = "SELECT COUNT(*) FROM users WHERE status = ?1", nativeQuery = true)
    long countByStatusNative(String status);
    
    // Method với Pageable
    Page<User> findByStatusOrderByCreatedAtDesc(User.UserStatus status, Pageable pageable);
}
```

## 🔧 SERVICE LAYER - BUSINESS LOGIC

```java
@Service              // Đánh dấu đây là Spring Service component
@Transactional        // Tất cả method đều chạy trong transaction
@RequiredArgsConstructor  // Lombok tự tạo constructor cho final fields
@Slf4j               // Lombok tự tạo logger
public class UserService {

    private final UserRepository userRepository;

    @CachePut(value = "users", key = "#result.id")  // Cập nhật cache sau khi tạo
    public User createUser(User user) {
        log.info("Creating new user: {}", user.getUsername());
        
        // Kiểm tra ràng buộc unique
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        
        user.setStatus(User.UserStatus.ACTIVE);
        return userRepository.save(user);
    }

    @Cacheable(value = "users", key = "#id")  // Cache kết quả theo ID
    @Transactional(readOnly = true)           // Transaction chỉ đọc
    public Optional<User> findById(Long id) {
        log.debug("Finding user by id: {}", id);
        return userRepository.findById(id);
    }
    
    @Async("taskExecutor")  // Chạy bất đồng bộ
    public CompletableFuture<Void> sendWelcomeEmailAsync(String email, String username) {
        log.info("Sending welcome email to: {}", email);
        // Simulate email sending
        try {
            Thread.sleep(2000); // Giả lập gửi email
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return CompletableFuture.completedFuture(null);
    }
    
    @CacheEvict(value = "users", key = "#id")  // Xóa cache khi delete
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
}
```

## 📱 CONTROLLER LAYER - XỬ LÝ HTTP REQUESTS

### REST API Controller

```java
@RestController                    // Kết hợp @Controller + @ResponseBody
@RequestMapping("/api/v1/users")   // Base URL cho tất cả endpoints
@CrossOrigin(origins = "*")        // Cho phép CORS từ mọi domain
@RequiredArgsConstructor           // Lombok tự tạo constructor
@Slf4j                            // Lombok tự tạo logger
@Tag(name = "User Management")     // Swagger documentation
public class UserController {

    private final UserService userService;

    @PostMapping                   // Xử lý HTTP POST requests
    @Operation(summary = "Create a new user")  // Swagger documentation
    public ResponseEntity<User> createUser(@Valid @RequestBody User user) {
        log.info("Creating user: {}", user.getUsername());
        
        User createdUser = userService.createUser(user);
        
        // Gửi email chào mừng bất đồng bộ
        userService.sendWelcomeEmailAsync(user.getEmail(), user.getUsername());
        
        return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
    }

    @GetMapping("/{id}")           // Xử lý HTTP GET requests với path variable
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        return userService.findById(id)
                .map(user -> ResponseEntity.ok(user))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping                    // GET với query parameters
    public ResponseEntity<Page<User>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<User> users = userService.findAll(pageable, status);
        return ResponseEntity.ok(users);
    }
}
```

### Web Controller (Thymeleaf Views)

```java
@Controller                    // Spring MVC Controller cho web pages
@RequestMapping("/web")        // Base URL cho web interface
@RequiredArgsConstructor       // Lombok tự tạo constructor
@Slf4j                        // Lombok tự tạo logger
public class WebController {

    private final UserService userService;
    private final UserProfileService userProfileService;

    @GetMapping("/")           // Trang chủ
    public String home(Model model) {
        // Thêm dữ liệu vào model để hiển thị trong view
        model.addAttribute("userStats", userService.getUserStatistics());
        model.addAttribute("profileStats", userProfileService.getProfileStatistics());
        
        return "home";         // Trả về tên template (home.html)
    }

    @GetMapping("/users")      // Danh sách users
    public String listUsers(Model model, 
                           @RequestParam(defaultValue = "0") int page,
                           @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<User> users = userService.findAll(pageable);
        
        model.addAttribute("users", users);
        return "users/list";   // Trả về template users/list.html
    }
    
    @PostMapping("/users")     // Xử lý form submit
    public String createUser(@Valid @ModelAttribute User user, 
                            BindingResult result,
                            RedirectAttributes redirectAttributes) {
        
        if (result.hasErrors()) {
            return "users/create";  // Trả về form với lỗi
        }
        
        try {
            userService.createUser(user);
            redirectAttributes.addFlashAttribute("success", "User created successfully!");
            return "redirect:/web/users";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/web/users/create";
        }
    }
}
```

## ⚙️ CONFIGURATION LAYER - CẤU HÌNH

### AppConfig

```java
@Configuration        // Đánh dấu đây là class cấu hình Spring
@EnableJpaAuditing    // Bật tính năng JPA auditing (@CreatedDate, @LastModifiedDate)
@EnableCaching        // Bật tính năng cache
@EnableScheduling     // Bật tính năng lập lịch tự động
@EnableAsync         // Bật tính năng xử lý bất đồng bộ
public class AppConfig implements WebMvcConfigurer {

    @Value("${app.cors.allowed-origins:*}")  // Inject giá trị từ properties file
    private String allowedOrigins;

    @Bean                    // Tạo bean được quản lý bởi Spring container
    @Profile("!redis")       // Chỉ active khi không dùng profile "redis"
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager("users", "userStats", "userProfiles");
    }

    @Bean(name = "taskExecutor")  // Tạo bean với tên cụ thể
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);      // Số thread tối thiểu
        executor.setMaxPoolSize(10);      // Số thread tối đa
        executor.setQueueCapacity(500);   // Kích thước queue
        executor.setThreadNamePrefix("CircleLife-Async-");
        return executor;
    }

    @Override  // Override method từ WebMvcConfigurer
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins(allowedOrigins.split(","))
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                .allowCredentials(true);
    }
}
```

## 🎯 SCHEDULED TASKS - TÁC VỤ TỰ ĐỘNG

```java
@Component              // Đánh dấu đây là Spring component
@RequiredArgsConstructor // Lombok tự tạo constructor
@Slf4j                  // Lombok tự tạo logger
public class ScheduledTasks {

    private final UserService userService;

    @Scheduled(fixedRate = 60000)  // Chạy mỗi 60 giây (không đợi task trước hoàn thành)
    public void reportUserStatistics() {
        log.info("=== User Statistics Report at {} ===", LocalDateTime.now());

        UserService.UserStatistics stats = userService.getUserStatistics();
        log.info("Total Users: {}", stats.totalUsers());
        log.info("Active Users: {}", stats.activeUsers());
    }

    @Scheduled(fixedDelay = 300000, initialDelay = 60000)  // Đợi 5 phút sau khi task trước hoàn thành
    public void cleanupInactiveUsers() {
        log.info("=== Cleanup Inactive Users Task ===");
        // Logic dọn dẹp user không hoạt động
    }

    @Scheduled(cron = "0 0 2 * * ?")  // Chạy lúc 2:00 AM mỗi ngày
    public void dailyMaintenanceTask() {
        log.info("=== Daily Maintenance Task ===");
        // Logic bảo trì hàng ngày
    }

    @Scheduled(cron = "0 */30 * * * ?")  // Chạy mỗi 30 phút
    public void healthCheck() {
        log.info("=== Health Check ===");
        // Kiểm tra sức khỏe hệ thống
    }
}
```

## 📋 APPLICATION PROPERTIES - CẤU HÌNH

```yaml
spring:
  application:
    name: java-circlelife        # Tên ứng dụng

  profiles:
    active: dev                  # Profile mặc định

  datasource:                    # Cấu hình database
    url: jdbc:h2:mem:circlelife  # H2 in-memory database
    driver-class-name: org.h2.Driver
    username: sa
    password: password

  jpa:                          # Cấu hình JPA/Hibernate
    hibernate:
      ddl-auto: create-drop     # Tự động tạo/xóa schema
    show-sql: true              # Hiển thị SQL queries
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true        # Format SQL cho dễ đọc

  cache:                        # Cấu hình cache
    type: simple
    cache-names:
      - users                   # Tên các cache
      - userStats
      - userProfiles

  security:                     # Cấu hình bảo mật cơ bản
    user:
      name: admin
      password: admin123
      roles: ADMIN

server:
  port: 8080                    # Port chạy ứng dụng

# Custom properties
app:
  cors:
    allowed-origins: http://localhost:3000,http://localhost:4200
  async:
    core-pool-size: 2
    max-pool-size: 10
    queue-capacity: 500

# Logging configuration
logging:
  level:
    com.circlelife: DEBUG       # Log level cho package
    org.hibernate.SQL: DEBUG    # Hiển thị SQL
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
```

## 🧪 TESTING - KIỂM THỬ

### Unit Test với Mockito

```java
@ExtendWith(MockitoExtension.class)  // Bật Mockito cho JUnit 5
class UserServiceTest {

    @Mock                            // Tạo mock object
    private UserRepository userRepository;

    @InjectMocks                     // Inject mock vào object được test
    private UserService userService;

    private User testUser;

    @BeforeEach                      // Chạy trước mỗi test method
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setStatus(User.UserStatus.ACTIVE);
    }

    @Test                           // Đánh dấu đây là test method
    void testCreateUser_Success() {
        // Given - Chuẩn bị dữ liệu test
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // When - Thực hiện hành động cần test
        User result = userService.createUser(testUser);

        // Then - Kiểm tra kết quả
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        verify(userRepository).save(testUser);  // Verify method được gọi
    }

    @Test
    void testCreateUser_EmailExists_ThrowsException() {
        // Given
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            userService.createUser(testUser);
        });
    }
}
```

## 🎨 FRONTEND - THYMELEAF TEMPLATES

### Layout Template (main.html)

```html
<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head>
    <meta charset="UTF-8">
    <title th:text="${pageTitle != null ? pageTitle + ' - CircleLife' : 'CircleLife'}">CircleLife</title>

    <!-- Bootstrap CSS -->
    <link th:href="@{/webjars/bootstrap/5.3.0/css/bootstrap.min.css}" rel="stylesheet">
    <!-- Font Awesome -->
    <link th:href="@{/webjars/font-awesome/6.4.0/css/all.min.css}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/web/}">
                <i class="fas fa-circle me-2"></i>CircleLife
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/web/}">
                                <i class="fas fa-home me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/web/users}">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Page Content -->
            <main class="col-md-10 ms-sm-auto px-4">
                <div layout:fragment="content">
                    <!-- Nội dung trang sẽ được chèn vào đây -->
                </div>
            </main>
        </div>
    </div>
</body>
</html>
```

### Home Page Template

```html
<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">  <!-- Sử dụng layout chính -->

<head>
    <title>Dashboard - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">     <!-- Nội dung sẽ được chèn vào layout -->
        <!-- Page Header -->
        <div class="page-header text-center">
            <h1><i class="fas fa-tachometer-alt me-3"></i>CircleLife Dashboard</h1>
            <p class="lead mb-0">Welcome to the Spring Boot Annotations Demo Application</p>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h3 th:text="${userStats.totalUsers}">0</h3>  <!-- Thymeleaf expression -->
                    <p>Total Users</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card success">
                    <h3 th:text="${userStats.activeUsers}">0</h3>
                    <p>Active Users</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <a th:href="@{/web/users/create}" class="btn btn-primary">  <!-- Thymeleaf URL -->
                            <i class="fas fa-user-plus me-2"></i>Add New User
                        </a>
                        <a th:href="@{/web/users}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>View All Users
                        </a>
                        <a th:href="@{/swagger-ui.html}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-code me-2"></i>API Documentation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
```

## 📊 TÓM TẮT CÁC ANNOTATION QUAN TRỌNG

### 🏗️ Core Spring Annotations:
- `@SpringBootApplication` - Khởi động ứng dụng Spring Boot
- `@Configuration` - Class cấu hình Spring
- `@Component` - Component tổng quát
- `@Service` - Service layer component
- `@Repository` - Data access layer component
- `@Controller` - Web MVC controller
- `@RestController` - REST API controller

### 🗃️ JPA/Database Annotations:
- `@Entity` - JPA entity
- `@Table` - Cấu hình bảng database
- `@Id` - Primary key
- `@GeneratedValue` - Auto-generated value
- `@Column` - Cấu hình cột
- `@ManyToOne`, `@OneToMany` - Quan hệ giữa entities
- `@JoinColumn` - Foreign key

### 🌐 Web Annotations:
- `@RequestMapping` - Map HTTP requests
- `@GetMapping`, `@PostMapping` - HTTP method specific mapping
- `@PathVariable` - Extract path variables
- `@RequestParam` - Extract request parameters
- `@RequestBody` - Bind request body
- `@Valid` - Trigger validation

### ⚡ Advanced Annotations:
- `@Cacheable`, `@CachePut`, `@CacheEvict` - Cache management
- `@Transactional` - Transaction management
- `@Scheduled` - Scheduled tasks
- `@Async` - Asynchronous execution
- `@Value` - Inject property values

### 🧪 Testing Annotations:
- `@ExtendWith` - JUnit 5 extensions
- `@Mock` - Create mock objects
- `@InjectMocks` - Inject mocks
- `@Test` - Test methods
- `@BeforeEach`, `@AfterEach` - Setup/teardown

## 🚀 LUỒNG HOẠT ĐỘNG CỦA ỨNG DỤNG

1. **Khởi động**: `@SpringBootApplication` khởi động Spring context
2. **Cấu hình**: `@Configuration` classes được load
3. **Component Scan**: Spring tìm và tạo các beans (`@Component`, `@Service`, `@Repository`)
4. **Database**: JPA entities được map với database
5. **Web Layer**: Controllers xử lý HTTP requests
6. **Business Logic**: Services thực hiện business logic
7. **Data Access**: Repositories truy cập database
8. **Caching**: Kết quả được cache để tăng performance
9. **Scheduling**: Các task tự động chạy theo lịch
10. **Response**: Kết quả được trả về dưới dạng JSON hoặc HTML

## 🎯 ĐIỂM NỔI BẬT CỦA DỰ ÁN

### ✅ Tính năng đã implement:
- ✅ **REST API** hoàn chỉnh với CRUD operations
- ✅ **Web Interface** với Thymeleaf templates
- ✅ **JPA/Hibernate** với relationships và auditing
- ✅ **Caching** với Spring Cache abstraction
- ✅ **Validation** với Bean Validation
- ✅ **Scheduling** với các task tự động
- ✅ **Async Processing** cho các tác vụ nặng
- ✅ **Testing** với JUnit 5 và Mockito
- ✅ **Security** cơ bản với Spring Security
- ✅ **Documentation** với Swagger/OpenAPI

### 🔧 Công nghệ sử dụng:
- **Java 17** - Programming language
- **Spring Boot 3** - Framework chính
- **Spring Data JPA** - Data access layer
- **H2 Database** - In-memory database
- **Thymeleaf** - Template engine
- **Bootstrap 5** - CSS framework
- **Lombok** - Reduce boilerplate code
- **JUnit 5** - Testing framework
- **Mockito** - Mocking framework

Đây là một ứng dụng demo hoàn chỉnh thể hiện hầu hết các annotation và pattern quan trọng trong Spring Boot!
```
