# 📚 Spring Boot Annotations Guide / Hướng dẫn Spring Boot Annotations

## 🏗️ Core Annotations / Annotations Cốt lõi

### @SpringBootApplication
**English**: Bootstrap the app with default config, auto-config, component scan  
**Tiếng Việt**: Khởi động ứng dụng với cấu hình mặc định, tự động cấu hình, quét component  
**Used On / Sử dụng trên**: Main class / Class chính  

```java
@SpringBootApplication
public class MyApplication {
    public static void main(String[] args) {
        SpringApplication.run(MyApplication.class, args);
    }
}
```

### @ComponentScan
**English**: Scan specific packages for Spring beans  
**Tiếng Việt**: Quét các package cụ thể để tìm Spring beans  
**Used On / Sử dụng trên**: Config/main class / Class cấu hình/chính  

```java
@ComponentScan(basePackages = "com.example.service")
@SpringBootApplication
public class Application { }
```

### @Configuration
**English**: Define Spring beans in Java config  
**Tiếng Việt**: Đ<PERSON><PERSON> nghĩa Spring beans trong cấu hình Java  
**Used On / Sử dụng trên**: Config class / Class cấu hình  

```java
@Configuration
public class AppConfig {
    @Bean
    public DataSource dataSource() {
        return new HikariDataSource();
    }
}
```

## 🔧 Bean Management / Quản lý Bean

### @Bean
**English**: Declare and customize a bean instance  
**Tiếng Việt**: Khai báo và tùy chỉnh một instance bean  
**Used On / Sử dụng trên**: Method inside config class / Method trong class cấu hình  

### @Value
**English**: Inject values from application properties  
**Tiếng Việt**: Inject giá trị từ application properties  
**Used On / Sử dụng trên**: Field, setter, constructor / Trường, setter, constructor  

```java
@Value("${app.name}")
private String appName;

@Value("${server.port:8080}")  // Default value
private int port;
```

### @PropertySource
**English**: Load external properties file  
**Tiếng Việt**: Tải file properties bên ngoài  
**Used On / Sử dụng trên**: Config class / Class cấu hình  

## 🎯 Dependency Injection / Tiêm phụ thuộc

### @Autowired
**English**: Auto-inject dependencies by type  
**Tiếng Việt**: Tự động tiêm phụ thuộc theo kiểu  
**Used On / Sử dụng trên**: Field, setter, constructor / Trường, setter, constructor  

```java
@Autowired
private UserService userService;

// Constructor injection (recommended)
@Autowired
public UserController(UserService userService) {
    this.userService = userService;
}
```

### @Qualifier
**English**: Specify which bean to inject if multiple exist  
**Tiếng Việt**: Chỉ định bean nào để inject khi có nhiều bean  
**Used On / Sử dụng trên**: Field, param / Trường, tham số  

```java
@Autowired
@Qualifier("primaryDataSource")
private DataSource dataSource;
```

### @Primary
**English**: Mark default bean when multiple types exist  
**Tiếng Việt**: Đánh dấu bean mặc định khi có nhiều kiểu  
**Used On / Sử dụng trên**: Bean class or method / Class bean hoặc method  

## 🏷️ Stereotype Annotations / Annotations Stereotype

### @Component
**English**: General-purpose Spring-managed bean  
**Tiếng Việt**: Bean được Spring quản lý mục đích chung  
**Used On / Sử dụng trên**: Class / Lớp  

### @Service
**English**: Business logic layer bean  
**Tiếng Việt**: Bean tầng logic nghiệp vụ  
**Used On / Sử dụng trên**: Class / Lớp  

```java
@Service
@Transactional
public class UserService {
    public User createUser(User user) {
        // Business logic
        return userRepository.save(user);
    }
}
```

### @Repository
**English**: DAO class with exception translation  
**Tiếng Việt**: Class DAO với chuyển đổi exception  
**Used On / Sử dụng trên**: Class or interface / Class hoặc interface  

### @Controller
**English**: MVC controller for web pages  
**Tiếng Việt**: Controller MVC cho trang web  
**Used On / Sử dụng trên**: Class / Lớp  

### @RestController
**English**: REST controller returning JSON/XML  
**Tiếng Việt**: REST controller trả về JSON/XML  
**Used On / Sử dụng trên**: Class / Lớp  

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping("/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        return ResponseEntity.ok(userService.findById(id));
    }
}
```

## 🌐 Web (MVC) Annotations / Annotations Web (MVC)

### @RequestMapping
**English**: Map HTTP requests to methods  
**Tiếng Việt**: Ánh xạ HTTP requests tới methods  
**Used On / Sử dụng trên**: Class/method / Class/method  

### @GetMapping, @PostMapping, etc.
**English**: Shorthand for request methods  
**Tiếng Việt**: Viết tắt cho các HTTP methods  
**Used On / Sử dụng trên**: Method / Method  

### @PathVariable
**English**: Bind URI template variable  
**Tiếng Việt**: Liên kết biến URI template  
**Used On / Sử dụng trên**: Method param / Tham số method  

```java
@GetMapping("/users/{id}")
public User getUser(@PathVariable("id") Long userId) {
    return userService.findById(userId);
}
```

### @RequestParam
**English**: Bind query parameter from URL  
**Tiếng Việt**: Liên kết query parameter từ URL  
**Used On / Sử dụng trên**: Method param / Tham số method  

```java
@GetMapping("/users")
public List<User> getUsers(
    @RequestParam(defaultValue = "0") int page,
    @RequestParam(defaultValue = "10") int size) {
    return userService.findAll(page, size);
}
```

### @RequestBody
**English**: Bind HTTP request body (JSON) to object  
**Tiếng Việt**: Liên kết HTTP request body (JSON) tới object  
**Used On / Sử dụng trên**: Method param / Tham số method  

### @ResponseBody
**English**: Return JSON/XML response from method  
**Tiếng Việt**: Trả về JSON/XML response từ method  
**Used On / Sử dụng trên**: Method / Method  

### @ModelAttribute
**English**: Bind form data to model  
**Tiếng Việt**: Liên kết dữ liệu form tới model  
**Used On / Sử dụng trên**: Method param / Tham số method  

### @CrossOrigin
**English**: Enable CORS for APIs  
**Tiếng Việt**: Bật CORS cho APIs  
**Used On / Sử dụng trên**: Class/method / Class/method  

## ✅ Validation Annotations / Annotations Validation

### @Valid
**English**: Trigger validation on DTOs  
**Tiếng Việt**: Kích hoạt validation trên DTOs  
**Used On / Sử dụng trên**: Method param / Tham số method  

### @NotNull, @Size, etc.
**English**: Validate bean properties (JSR-303)  
**Tiếng Việt**: Validate thuộc tính bean (JSR-303)  
**Used On / Sử dụng trên**: DTO field / Trường DTO  

```java
public class CreateUserRequest {
    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 50)
    private String username;
    
    @Email(message = "Invalid email format")
    private String email;
}
```

## 🗃️ JPA Annotations / Annotations JPA

### @Entity
**English**: Mark model class as DB entity
**Tiếng Việt**: Đánh dấu model class là DB entity
**Used On / Sử dụng trên**: Model class / Class model

### @Table, @Column
**English**: Customize DB table/column mappings
**Tiếng Việt**: Tùy chỉnh ánh xạ DB table/column
**Used On / Sử dụng trên**: Entity/field / Entity/trường

```java
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_email", columnList = "email")
})
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email_address", unique = true, nullable = false)
    private String email;
}
```

### @Id, @GeneratedValue
**English**: Primary key and auto ID generation
**Tiếng Việt**: Khóa chính và tự động tạo ID
**Used On / Sử dụng trên**: Field / Trường

### @Repository
**English**: Enable JPA repositories
**Tiếng Việt**: Bật JPA repositories
**Used On / Sử dụng trên**: Config class / Class cấu hình

### @EnableJpaRepositories
**English**: Enable repo scanning
**Tiếng Việt**: Bật quét repositories
**Used On / Sử dụng trên**: Config class / Class cấu hình

### @Transactional
**English**: Define transactional boundaries
**Tiếng Việt**: Định nghĩa ranh giới transaction
**Used On / Sử dụng trên**: Method/class / Method/class

```java
@Service
@Transactional
public class UserService {

    @Transactional(readOnly = true)
    public User findById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        return userRepository.save(user);
    }
}
```

### @Modifying, @Query
**English**: Custom JPA queries or updates
**Tiếng Việt**: Truy vấn hoặc cập nhật JPA tùy chỉnh
**Used On / Sử dụng trên**: Repository method / Method repository

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    @Query("SELECT u FROM User u WHERE u.email = :email")
    Optional<User> findByEmail(@Param("email") String email);

    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.id = :id")
    int updateUserStatus(@Param("id") Long id, @Param("status") String status);
}
```

## ⏰ Scheduling / Async / Lập lịch / Bất đồng bộ

### @EnableScheduling
**English**: Enable @Scheduled task support
**Tiếng Việt**: Bật hỗ trợ task @Scheduled
**Used On / Sử dụng trên**: Config class / Class cấu hình

### @Scheduled
**English**: Run method on cron/fixed rate
**Tiếng Việt**: Chạy method theo cron/tần suất cố định
**Used On / Sử dụng trên**: Method / Method

```java
@Component
public class ScheduledTasks {

    @Scheduled(fixedRate = 60000) // Every 60 seconds
    public void reportCurrentTime() {
        log.info("Current time: {}", LocalDateTime.now());
    }

    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    public void dailyCleanup() {
        log.info("Running daily cleanup...");
    }
}
```

### @EnableAsync
**English**: Enable @Async processing
**Tiếng Việt**: Bật xử lý @Async
**Used On / Sử dụng trên**: Config class / Class cấu hình

### @Async
**English**: Run method in a separate thread
**Tiếng Việt**: Chạy method trong thread riêng biệt
**Used On / Sử dụng trên**: Method / Method

```java
@Service
public class EmailService {

    @Async
    public CompletableFuture<Void> sendWelcomeEmail(String email) {
        // Send email asynchronously
        log.info("Sending welcome email to: {}", email);
        return CompletableFuture.completedFuture(null);
    }
}
```

## 💾 Caching Annotations / Annotations Cache

### @EnableCaching
**English**: Enable caching system-wide
**Tiếng Việt**: Bật hệ thống cache toàn hệ thống
**Used On / Sử dụng trên**: Config class / Class cấu hình

### @Cacheable
**English**: Cache result of method
**Tiếng Việt**: Cache kết quả của method
**Used On / Sử dụng trên**: Method / Method

### @CachePut
**English**: Update cache with method result
**Tiếng Việt**: Cập nhật cache với kết quả method
**Used On / Sử dụng trên**: Method / Method

### @CacheEvict
**English**: Remove cache entry
**Tiếng Việt**: Xóa entry cache
**Used On / Sử dụng trên**: Method / Method

```java
@Service
public class UserService {

    @Cacheable(value = "users", key = "#id")
    public User findById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    @CachePut(value = "users", key = "#result.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    @CacheEvict(value = "users", key = "#id")
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
}
```

## 🔐 Conditional Annotations / Annotations Điều kiện

### @ConditionalOnProperty
**English**: Load bean based on property value
**Tiếng Việt**: Tải bean dựa trên giá trị property
**Used On / Sử dụng trên**: Bean method/class / Bean method/class

### @ConditionalOnClass
**English**: Load bean if class is on classpath
**Tiếng Việt**: Tải bean nếu class có trong classpath
**Used On / Sử dụng trên**: Bean method/class / Bean method/class

```java
@Configuration
public class DatabaseConfig {

    @Bean
    @ConditionalOnProperty(name = "app.database.type", havingValue = "mysql")
    public DataSource mysqlDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @ConditionalOnClass(name = "org.postgresql.Driver")
    public DataSource postgresDataSource() {
        return new HikariDataSource();
    }
}
```

## 🔒 Security Annotations / Annotations Bảo mật

### @EnableWebSecurity
**English**: Enable Spring Security
**Tiếng Việt**: Bật Spring Security
**Used On / Sử dụng trên**: Config class / Class cấu hình

### @PreAuthorize
**English**: Pre-check access with SpEL
**Tiếng Việt**: Kiểm tra trước quyền truy cập với SpEL
**Used On / Sử dụng trên**: Method / Method

### @PostAuthorize
**English**: Post-check access with SpEL
**Tiếng Việt**: Kiểm tra sau quyền truy cập với SpEL
**Used On / Sử dụng trên**: Method / Method

### @Secured
**English**: Role-based access (basic)
**Tiếng Việt**: Truy cập dựa trên role (cơ bản)
**Used On / Sử dụng trên**: Method / Method

### @WithMockUser
**English**: Simulate user in tests
**Tiếng Việt**: Mô phỏng user trong tests
**Used On / Sử dụng trên**: Test method / Test method

```java
@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @GetMapping("/users")
    @PreAuthorize("hasAuthority('READ_USERS')")
    public List<User> getAllUsers() {
        return userService.findAll();
    }

    @DeleteMapping("/users/{id}")
    @PreAuthorize("hasRole('ADMIN') and #id != authentication.principal.id")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteById(id);
    }
}
```

## 🧪 Testing Annotations / Annotations Testing

### @SpringBootTest
**English**: Full Spring context for integration testing
**Tiếng Việt**: Context Spring đầy đủ cho integration testing
**Used On / Sử dụng trên**: Test class / Test class

### @WebMvcTest
**English**: Test only web layer (controllers)
**Tiếng Việt**: Test chỉ tầng web (controllers)
**Used On / Sử dụng trên**: Test class / Test class

### @DataJpaTest
**English**: Test only JPA repositories
**Tiếng Việt**: Test chỉ JPA repositories
**Used On / Sử dụng trên**: Test class / Test class

### @MockBean
**English**: Inject a mock into Spring context
**Tiếng Việt**: Inject mock vào Spring context
**Used On / Sử dụng trên**: Test class / Test class

### @TestConfiguration
**English**: Custom config for test only
**Tiếng Việt**: Cấu hình tùy chỉnh chỉ cho test
**Used On / Sử dụng trên**: Inner class / Inner class

```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class UserServiceIntegrationTest {

    @Autowired
    private UserService userService;

    @MockBean
    private EmailService emailService;

    @Test
    void shouldCreateUserSuccessfully() {
        // Given
        User user = new User("<EMAIL>", "John Doe");

        // When
        User savedUser = userService.createUser(user);

        // Then
        assertThat(savedUser.getId()).isNotNull();
        verify(emailService).sendWelcomeEmail(user.getEmail());
    }
}

@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Test
    void shouldReturnUserById() throws Exception {
        // Given
        User user = new User("<EMAIL>", "John Doe");
        when(userService.findById(1L)).thenReturn(user);

        // When & Then
        mockMvc.perform(get("/api/users/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }
}
```

## 🔧 Misc Annotations / Annotations Khác

### @Profile
**English**: Load bean only for specific profile (dev, prod)
**Tiếng Việt**: Tải bean chỉ cho profile cụ thể (dev, prod)
**Used On / Sử dụng trên**: Bean/class / Bean/class

### @Scope
**English**: Define bean lifecycle (singleton, prototype)
**Tiếng Việt**: Định nghĩa vòng đời bean (singleton, prototype)
**Used On / Sử dụng trên**: Bean class / Bean class

### @Import
**English**: Import other config classes
**Tiếng Việt**: Import các config classes khác
**Used On / Sử dụng trên**: Config class / Config class

### @EnableConfigurationProperties
**English**: Enable @ConfigurationProperties class
**Tiếng Việt**: Bật @ConfigurationProperties class
**Used On / Sử dụng trên**: Config class / Config class

### @ConfigurationProperties
**English**: Bind properties to POJO
**Tiếng Việt**: Liên kết properties tới POJO
**Used On / Sử dụng trên**: Config class / Config class

```java
@ConfigurationProperties(prefix = "app.mail")
@Component
public class MailProperties {
    private String host;
    private int port;
    private String username;
    private String password;

    // Getters and setters
}

// In application.yml:
// app:
//   mail:
//     host: smtp.gmail.com
//     port: 587
//     username: <EMAIL>
//     password: your-password
```

---

## 📋 Quick Reference Summary / Tóm tắt Tham khảo Nhanh

| **Category / Danh mục** | **Key Annotations / Annotations Chính** | **Purpose / Mục đích** |
|-------------------------|------------------------------------------|------------------------|
| **Core / Cốt lõi** | `@SpringBootApplication`, `@Configuration`, `@ComponentScan` | App bootstrap & config / Khởi động app & cấu hình |
| **Dependency Injection / Tiêm phụ thuộc** | `@Autowired`, `@Qualifier`, `@Value` | Bean injection / Tiêm bean |
| **Stereotype / Stereotype** | `@Component`, `@Service`, `@Repository`, `@Controller` | Bean classification / Phân loại bean |
| **Web MVC** | `@RestController`, `@RequestMapping`, `@GetMapping`, `@PostMapping` | HTTP handling / Xử lý HTTP |
| **Data Binding / Liên kết dữ liệu** | `@RequestBody`, `@PathVariable`, `@RequestParam` | Request data binding / Liên kết dữ liệu request |
| **JPA/Database** | `@Entity`, `@Repository`, `@Transactional` | Database operations / Thao tác database |
| **Validation / Validation** | `@Valid`, `@NotNull`, `@Size` | Input validation / Validation đầu vào |
| **Caching / Cache** | `@Cacheable`, `@CacheEvict`, `@EnableCaching` | Performance optimization / Tối ưu hiệu suất |
| **Security / Bảo mật** | `@PreAuthorize`, `@Secured`, `@EnableWebSecurity` | Access control / Kiểm soát truy cập |
| **Testing / Testing** | `@SpringBootTest`, `@MockBean`, `@WebMvcTest` | Unit & integration testing / Unit & integration testing |

---

## 💡 Best Practices / Thực hành Tốt nhất

### English:
1. **Use constructor injection** instead of field injection for better testability
2. **Prefer specific annotations** (`@Service`, `@Repository`) over generic `@Component`
3. **Use `@Transactional` at service layer** for proper transaction management
4. **Combine `@RestController` with `@RequestMapping`** for clean API design
5. **Use `@Valid` with custom validation messages** for better user experience
6. **Leverage `@Profile`** for environment-specific configurations
7. **Use `@MockBean` in tests** to isolate units under test

### Tiếng Việt:
1. **Sử dụng constructor injection** thay vì field injection để test tốt hơn
2. **Ưu tiên annotations cụ thể** (`@Service`, `@Repository`) hơn `@Component` chung chung
3. **Sử dụng `@Transactional` ở service layer** để quản lý transaction đúng cách
4. **Kết hợp `@RestController` với `@RequestMapping`** cho thiết kế API sạch
5. **Sử dụng `@Valid` với custom validation messages** để trải nghiệm người dùng tốt hơn
6. **Tận dụng `@Profile`** cho cấu hình theo môi trường cụ thể
7. **Sử dụng `@MockBean` trong tests** để cô lập units đang test

---

*This guide covers the most commonly used Spring Boot annotations with practical examples in both English and Vietnamese. / Hướng dẫn này bao gồm các Spring Boot annotations được sử dụng phổ biến nhất với ví dụ thực tế bằng cả tiếng Anh và tiếng Việt.*
