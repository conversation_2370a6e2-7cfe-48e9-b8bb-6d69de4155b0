package com.circlelife.controller;

import com.circlelife.model.UserProfile;
import com.circlelife.service.UserProfileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * UserProfile Controller demonstrating JPA relationship management
 * 
 * @RestController - Combines @Controller and @ResponseBody
 * @RequestMapping - Maps HTTP requests to handler methods
 * @CrossOrigin - Enables CORS for cross-origin requests
 */
@RestController
@RequestMapping("/api/v1/profiles")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Profile Management", description = "APIs for managing user profiles and relationships")
public class UserProfileController {

    @Autowired
    private final UserProfileService userProfileService;

    /**
     * Create a new user profile
     * 
     * @PostMapping - Maps HTTP POST requests
     * @RequestBody - Binds HTTP request body to method parameter
     * @Valid - Triggers validation on the request body
     */
    @PostMapping
    @Operation(summary = "Create a new user profile", description = "Creates a new profile for a user")
    public ResponseEntity<UserProfile> createProfile(
            @Valid @RequestBody UserProfile profile) {
        
        log.info("Creating profile for user: {}, type: {}", 
                profile.getUser().getId(), profile.getProfileType());
        
        try {
            UserProfile createdProfile = userProfileService.createProfile(profile);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdProfile);
        } catch (IllegalArgumentException e) {
            log.error("Error creating profile: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get profile by ID
     * 
     * @GetMapping - Maps HTTP GET requests
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get profile by ID", description = "Retrieves a profile by its unique identifier")
    public ResponseEntity<UserProfile> getProfileById(
            @Parameter(description = "Profile ID", required = true)
            @PathVariable Long id) {
        
        log.debug("Getting profile by ID: {}", id);
        
        return userProfileService.findById(id)
                .map(profile -> ResponseEntity.ok(profile))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get profiles by user ID
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get profiles by user ID", description = "Retrieves all profiles for a specific user")
    public ResponseEntity<List<UserProfile>> getProfilesByUserId(
            @Parameter(description = "User ID", required = true)
            @PathVariable Long userId) {
        
        log.debug("Getting profiles for user: {}", userId);
        
        try {
            List<UserProfile> profiles = userProfileService.findByUserId(userId);
            return ResponseEntity.ok(profiles);
        } catch (IllegalArgumentException e) {
            log.error("Error getting profiles for user: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get active profiles by user ID
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/user/{userId}/active")
    @Operation(summary = "Get active profiles by user ID", description = "Retrieves only active profiles for a specific user")
    public ResponseEntity<List<UserProfile>> getActiveProfilesByUserId(
            @Parameter(description = "User ID", required = true)
            @PathVariable Long userId) {
        
        log.debug("Getting active profiles for user: {}", userId);
        
        List<UserProfile> profiles = userProfileService.findActiveProfilesByUserId(userId);
        return ResponseEntity.ok(profiles);
    }

    /**
     * Get profiles by type
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @RequestParam - Binds query parameter to method parameter
     */
    @GetMapping("/type")
    @Operation(summary = "Get profiles by type", description = "Retrieves profiles filtered by their type")
    public ResponseEntity<List<UserProfile>> getProfilesByType(
            @Parameter(description = "Profile type", required = true)
            @RequestParam UserProfile.ProfileType profileType) {
        
        log.debug("Getting profiles by type: {}", profileType);
        
        List<UserProfile> profiles = userProfileService.findByProfileType(profileType);
        return ResponseEntity.ok(profiles);
    }

    /**
     * Get profiles by user and type
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @PathVariable - Binds URI template variable to method parameter
     * @RequestParam - Binds query parameter to method parameter
     */
    @GetMapping("/user/{userId}/type")
    @Operation(summary = "Get profiles by user and type", description = "Retrieves profiles for a specific user and type")
    public ResponseEntity<List<UserProfile>> getProfilesByUserAndType(
            @Parameter(description = "User ID", required = true)
            @PathVariable Long userId,
            
            @Parameter(description = "Profile type", required = true)
            @RequestParam UserProfile.ProfileType profileType) {
        
        log.debug("Getting profiles for user: {} and type: {}", userId, profileType);
        
        List<UserProfile> profiles = userProfileService.findByUserIdAndType(userId, profileType);
        return ResponseEntity.ok(profiles);
    }

    /**
     * Search profiles with multiple criteria
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @RequestParam - Binds query parameters to method parameters
     */
    @GetMapping("/search")
    @Operation(summary = "Search profiles", description = "Search profiles with multiple criteria and pagination")
    public ResponseEntity<Page<UserProfile>> searchProfiles(
            @Parameter(description = "User ID filter")
            @RequestParam(required = false) Long userId,
            
            @Parameter(description = "Profile type filter")
            @RequestParam(required = false) UserProfile.ProfileType profileType,
            
            @Parameter(description = "Active status filter")
            @RequestParam(required = false) Boolean isActive,
            
            @Parameter(description = "Search text in key or value")
            @RequestParam(required = false) String searchText,
            
            @Parameter(description = "Page number (0-based)")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Page size")
            @RequestParam(defaultValue = "10") int size,
            
            @Parameter(description = "Sort field")
            @RequestParam(defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "Sort direction")
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.debug("Searching profiles with criteria - userId: {}, type: {}, active: {}, search: {}", 
                 userId, profileType, isActive, searchText);
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<UserProfile> profiles = userProfileService.searchProfiles(
            userId, profileType, isActive, searchText, pageable);
        
        return ResponseEntity.ok(profiles);
    }

    /**
     * Get profile by user and key
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @PathVariable - Binds URI template variable to method parameter
     * @RequestParam - Binds query parameter to method parameter
     */
    @GetMapping("/user/{userId}/key")
    @Operation(summary = "Get profile by user and key", description = "Retrieves a specific profile by user ID and profile key")
    public ResponseEntity<UserProfile> getProfileByUserAndKey(
            @Parameter(description = "User ID", required = true)
            @PathVariable Long userId,
            
            @Parameter(description = "Profile key", required = true)
            @RequestParam String profileKey) {
        
        log.debug("Getting profile for user: {} and key: {}", userId, profileKey);
        
        return userProfileService.findByUserIdAndKey(userId, profileKey)
                .map(profile -> ResponseEntity.ok(profile))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Update profile
     * 
     * @PutMapping - Maps HTTP PUT requests
     * @PathVariable - Binds URI template variable to method parameter
     * @RequestBody - Binds HTTP request body to method parameter
     * @Valid - Triggers validation on the request body
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update profile", description = "Updates an existing profile with new information")
    public ResponseEntity<UserProfile> updateProfile(
            @Parameter(description = "Profile ID", required = true)
            @PathVariable Long id,
            
            @Valid @RequestBody UserProfile profile) {
        
        log.info("Updating profile: {}", id);
        
        try {
            profile.setId(id);
            UserProfile updatedProfile = userProfileService.updateProfile(profile);
            return ResponseEntity.ok(updatedProfile);
        } catch (IllegalArgumentException e) {
            log.error("Error updating profile: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Update profile value only
     * 
     * @PatchMapping - Maps HTTP PATCH requests
     * @PathVariable - Binds URI template variable to method parameter
     * @RequestBody - Binds HTTP request body to method parameter
     */
    @PatchMapping("/{id}/value")
    @Operation(summary = "Update profile value", description = "Updates only the value of a profile")
    public ResponseEntity<String> updateProfileValue(
            @Parameter(description = "Profile ID", required = true)
            @PathVariable Long id,
            
            @RequestBody String value) {
        
        log.info("Updating profile value for ID: {}", id);
        
        boolean updated = userProfileService.updateProfileValue(id, value);
        if (updated) {
            return ResponseEntity.ok("Profile value updated successfully");
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Delete profile
     * 
     * @DeleteMapping - Maps HTTP DELETE requests
     * @PathVariable - Binds URI template variable to method parameter
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete profile", description = "Permanently deletes a profile")
    public ResponseEntity<Void> deleteProfile(
            @Parameter(description = "Profile ID", required = true)
            @PathVariable Long id) {
        
        log.info("Deleting profile: {}", id);
        
        try {
            userProfileService.deleteProfile(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            log.error("Error deleting profile: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Deactivate profile (soft delete)
     * 
     * @PatchMapping - Maps HTTP PATCH requests
     * @PathVariable - Binds URI template variable to method parameter
     */
    @PatchMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate profile", description = "Marks a profile as inactive without removing from database")
    public ResponseEntity<UserProfile> deactivateProfile(
            @Parameter(description = "Profile ID", required = true)
            @PathVariable Long id) {
        
        log.info("Deactivating profile: {}", id);
        
        try {
            UserProfile deactivatedProfile = userProfileService.deactivateProfile(id);
            return ResponseEntity.ok(deactivatedProfile);
        } catch (IllegalArgumentException e) {
            log.error("Error deactivating profile: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Deactivate all profiles for a user
     * 
     * @PatchMapping - Maps HTTP PATCH requests
     * @PathVariable - Binds URI template variable to method parameter
     */
    @PatchMapping("/user/{userId}/deactivate")
    @Operation(summary = "Deactivate user profiles", description = "Deactivates all profiles for a specific user")
    public ResponseEntity<String> deactivateUserProfiles(
            @Parameter(description = "User ID", required = true)
            @PathVariable Long userId) {
        
        log.info("Deactivating all profiles for user: {}", userId);
        
        try {
            int deactivatedCount = userProfileService.deactivateProfilesByUserId(userId);
            return ResponseEntity.ok("Deactivated " + deactivatedCount + " profiles");
        } catch (IllegalArgumentException e) {
            log.error("Error deactivating user profiles: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get profile statistics
     * 
     * @GetMapping - Maps HTTP GET requests
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get profile statistics", description = "Retrieves statistical information about profiles")
    public ResponseEntity<UserProfileService.ProfileStatistics> getProfileStatistics() {
        log.debug("Getting profile statistics");
        
        UserProfileService.ProfileStatistics stats = userProfileService.getProfileStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get recent profiles
     * 
     * @GetMapping - Maps HTTP GET requests with path
     * @RequestParam - Binds query parameter to method parameter
     */
    @GetMapping("/recent")
    @Operation(summary = "Get recent profiles", description = "Retrieves profiles created in the last N days")
    public ResponseEntity<List<UserProfile>> getRecentProfiles(
            @Parameter(description = "Number of days")
            @RequestParam(defaultValue = "7") int days) {
        
        log.debug("Getting profiles created in last {} days", days);
        
        List<UserProfile> profiles = userProfileService.findRecentProfiles(days);
        return ResponseEntity.ok(profiles);
    }

    /**
     * Cleanup inactive profiles
     * 
     * @DeleteMapping - Maps HTTP DELETE requests with path
     * @RequestParam - Binds query parameter to method parameter
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "Cleanup inactive profiles", description = "Removes inactive profiles older than specified days")
    public ResponseEntity<String> cleanupInactiveProfiles(
            @Parameter(description = "Number of days")
            @RequestParam(defaultValue = "30") int days) {
        
        log.info("Cleaning up inactive profiles older than {} days", days);
        
        int deletedCount = userProfileService.cleanupInactiveProfiles(days);
        return ResponseEntity.ok("Cleaned up " + deletedCount + " inactive profiles");
    }

    /**
     * Clear profile caches
     * 
     * @PostMapping - Maps HTTP POST requests
     */
    @PostMapping("/cache/clear")
    @Operation(summary = "Clear profile caches", description = "Clears all profile-related caches")
    public ResponseEntity<String> clearProfileCaches() {
        log.info("Clearing profile caches");
        
        userProfileService.clearProfileCaches();
        return ResponseEntity.ok("Profile caches cleared successfully");
    }
}
