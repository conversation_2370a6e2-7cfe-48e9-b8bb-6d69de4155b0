package com.circlelife.controller;

import com.circlelife.model.User;
import com.circlelife.model.UserProfile;
import com.circlelife.model.Address;
import com.circlelife.service.UserService;
import com.circlelife.service.UserProfileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * Web Controller demonstrating Server-Side Rendering with <PERSON><PERSON><PERSON>eaf
 * 
 * @Controller - Marks this class as a Spring MVC controller for web pages
 * Unlike @RestController, this returns view names instead of JSON responses
 * 
 * @RequestMapping - Maps HTTP requests to handler methods
 * @GetMapping, @PostMapping - HTTP method specific mappings
 * Model - Spring MVC model for passing data to views
 * RedirectAttributes - For flash attributes in redirects
 */
@Controller
@RequestMapping("/web")
@RequiredArgsConstructor
@Slf4j
public class WebController {

    private final UserService userService;
    private final UserProfileService userProfileService;

    /**
     * Home page
     * 
     * @GetMapping - Maps HTTP GET requests
     * Model - Container for model attributes passed to the view
     */
    @GetMapping("/")
    public String home(Model model) {
        log.debug("Rendering home page");
        
        // Add statistics to model
        UserService.UserStatistics userStats = userService.getUserStatistics();
        UserProfileService.ProfileStatistics profileStats = userProfileService.getProfileStatistics();
        
        model.addAttribute("userStats", userStats);
        model.addAttribute("profileStats", profileStats);
        model.addAttribute("pageTitle", "CircleLife Dashboard");
        
        return "home"; // Returns view name (home.html)
    }

    /**
     * Users list page with pagination
     * 
     * @GetMapping - Maps HTTP GET requests
     * @RequestParam - Binds query parameters with default values
     */
    @GetMapping("/users")
    public String usersList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) User.UserStatus status,
            @RequestParam(required = false) String search,
            Model model) {
        
        log.debug("Rendering users list - page: {}, size: {}, sortBy: {}, sortDir: {}", 
                 page, size, sortBy, sortDir);
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<User> users;
        
        // Filter by status if provided
        if (status != null) {
            users = userService.findAllUsers(pageable); // You can implement filtered version
        } else {
            users = userService.findAllUsers(pageable);
        }
        
        model.addAttribute("users", users);
        model.addAttribute("currentPage", page);
        model.addAttribute("pageSize", size);
        model.addAttribute("sortBy", sortBy);
        model.addAttribute("sortDir", sortDir);
        model.addAttribute("reverseSortDir", sortDir.equals("asc") ? "desc" : "asc");
        model.addAttribute("selectedStatus", status);
        model.addAttribute("search", search);
        model.addAttribute("userStatuses", User.UserStatus.values());
        model.addAttribute("pageTitle", "Users Management");
        
        return "users/list";
    }

    /**
     * User details page
     * 
     * @GetMapping - Maps HTTP GET requests with path variable
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/users/{id}")
    public String userDetails(@PathVariable Long id, Model model) {
        log.debug("Rendering user details for ID: {}", id);
        
        Optional<User> userOpt = userService.findById(id);
        if (userOpt.isEmpty()) {
            return "redirect:/web/users?error=User not found";
        }
        
        User user = userOpt.get();
        List<UserProfile> profiles = userProfileService.findByUserId(id);
        
        model.addAttribute("user", user);
        model.addAttribute("profiles", profiles);
        model.addAttribute("pageTitle", "User Details - " + user.getFullName());
        
        return "users/details";
    }

    /**
     * Create user form page
     * 
     * @GetMapping - Maps HTTP GET requests
     * Model - For passing empty user object to form
     */
    @GetMapping("/users/create")
    public String createUserForm(Model model) {
        log.debug("Rendering create user form");

        User user = new User();
        user.setAddress(new Address()); // Initialize address

        model.addAttribute("user", user);
        model.addAttribute("userStatuses", User.UserStatus.values());
        model.addAttribute("pageTitle", "Create New User");
        model.addAttribute("isEdit", false);

        return "users/form";
    }

    /**
     * Process create user form submission
     * 
     * @PostMapping - Maps HTTP POST requests
     * @Valid - Triggers validation on the form object
     * BindingResult - Contains validation results
     * RedirectAttributes - For flash messages
     */
    @PostMapping("/users/create")
    public String createUser(@Valid @ModelAttribute User user, 
                           BindingResult bindingResult, 
                           Model model,
                           RedirectAttributes redirectAttributes) {
        
        log.info("Processing create user form for: {}", user.getUsername());
        
        if (bindingResult.hasErrors()) {
            log.warn("Validation errors in create user form: {}", bindingResult.getAllErrors());
            model.addAttribute("userStatuses", User.UserStatus.values());
            model.addAttribute("pageTitle", "Create New User");
            model.addAttribute("isEdit", false);
            return "users/form";
        }
        
        try {
            User createdUser = userService.createUser(user);
            redirectAttributes.addFlashAttribute("successMessage", 
                "User created successfully: " + createdUser.getFullName());
            return "redirect:/web/users/" + createdUser.getId();
        } catch (IllegalArgumentException e) {
            log.error("Error creating user: {}", e.getMessage());
            model.addAttribute("errorMessage", e.getMessage());
            model.addAttribute("userStatuses", User.UserStatus.values());
            model.addAttribute("pageTitle", "Create New User");
            model.addAttribute("isEdit", false);
            return "users/form";
        }
    }

    /**
     * Edit user form page
     * 
     * @GetMapping - Maps HTTP GET requests with path variable
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/users/{id}/edit")
    public String editUserForm(@PathVariable Long id, Model model) {
        log.debug("Rendering edit user form for ID: {}", id);

        Optional<User> userOpt = userService.findById(id);
        if (userOpt.isEmpty()) {
            return "redirect:/web/users?error=User not found";
        }

        User user = userOpt.get();
        if (user.getAddress() == null) {
            user.setAddress(new Address()); // Initialize address if null
        }

        model.addAttribute("user", user);
        model.addAttribute("userStatuses", User.UserStatus.values());
        model.addAttribute("pageTitle", "Edit User - " + user.getFullName());
        model.addAttribute("isEdit", true);

        return "users/form";
    }

    /**
     * Process edit user form submission
     * 
     * @PostMapping - Maps HTTP POST requests with path variable
     * @PathVariable - Binds URI template variable to method parameter
     * @Valid - Triggers validation on the form object
     * BindingResult - Contains validation results
     */
    @PostMapping("/users/{id}/edit")
    public String editUser(@PathVariable Long id,
                         @Valid @ModelAttribute User user, 
                         BindingResult bindingResult, 
                         Model model,
                         RedirectAttributes redirectAttributes) {
        
        log.info("Processing edit user form for ID: {}", id);
        
        if (bindingResult.hasErrors()) {
            log.warn("Validation errors in edit user form: {}", bindingResult.getAllErrors());
            model.addAttribute("userStatuses", User.UserStatus.values());
            model.addAttribute("pageTitle", "Edit User");
            model.addAttribute("isEdit", true);
            return "users/form";
        }
        
        try {
            user.setId(id);
            User updatedUser = userService.updateUser(user);
            redirectAttributes.addFlashAttribute("successMessage", 
                "User updated successfully: " + updatedUser.getFullName());
            return "redirect:/web/users/" + updatedUser.getId();
        } catch (IllegalArgumentException e) {
            log.error("Error updating user: {}", e.getMessage());
            model.addAttribute("errorMessage", e.getMessage());
            model.addAttribute("userStatuses", User.UserStatus.values());
            model.addAttribute("pageTitle", "Edit User");
            model.addAttribute("isEdit", true);
            return "users/form";
        }
    }

    /**
     * Delete user
     * 
     * @PostMapping - Maps HTTP POST requests for delete action
     * @PathVariable - Binds URI template variable to method parameter
     * RedirectAttributes - For flash messages
     */
    @PostMapping("/users/{id}/delete")
    public String deleteUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        log.info("Processing delete user for ID: {}", id);
        
        try {
            Optional<User> userOpt = userService.findById(id);
            if (userOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("errorMessage", "User not found");
                return "redirect:/web/users";
            }
            
            String userName = userOpt.get().getFullName();
            userService.deleteUser(id);
            redirectAttributes.addFlashAttribute("successMessage", 
                "User deleted successfully: " + userName);
        } catch (IllegalArgumentException e) {
            log.error("Error deleting user: {}", e.getMessage());
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
        }
        
        return "redirect:/web/users";
    }

    /**
     * User profiles page
     * 
     * @GetMapping - Maps HTTP GET requests with path variable
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/users/{userId}/profiles")
    public String userProfiles(@PathVariable Long userId, Model model) {
        log.debug("Rendering user profiles for user ID: {}", userId);
        
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isEmpty()) {
            return "redirect:/web/users?error=User not found";
        }
        
        User user = userOpt.get();
        List<UserProfile> profiles = userProfileService.findByUserId(userId);
        
        model.addAttribute("user", user);
        model.addAttribute("profiles", profiles);
        model.addAttribute("profileTypes", UserProfile.ProfileType.values());
        model.addAttribute("pageTitle", "Profiles - " + user.getFullName());
        
        return "profiles/list";
    }

    /**
     * Create profile form page
     * 
     * @GetMapping - Maps HTTP GET requests with path variable
     * @PathVariable - Binds URI template variable to method parameter
     */
    @GetMapping("/users/{userId}/profiles/create")
    public String createProfileForm(@PathVariable Long userId, Model model) {
        log.debug("Rendering create profile form for user ID: {}", userId);
        
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isEmpty()) {
            return "redirect:/web/users?error=User not found";
        }
        
        User user = userOpt.get();
        UserProfile profile = new UserProfile();
        profile.setUser(user);
        
        model.addAttribute("user", user);
        model.addAttribute("profile", profile);
        model.addAttribute("profileTypes", UserProfile.ProfileType.values());
        model.addAttribute("pageTitle", "Create Profile - " + user.getFullName());
        model.addAttribute("isEdit", false);
        
        return "profiles/form";
    }

    /**
     * Process create profile form submission
     * 
     * @PostMapping - Maps HTTP POST requests with path variable
     * @PathVariable - Binds URI template variable to method parameter
     * @Valid - Triggers validation on the form object
     * BindingResult - Contains validation results
     */
    @PostMapping("/users/{userId}/profiles/create")
    public String createProfile(@PathVariable Long userId,
                              @Valid @ModelAttribute UserProfile profile, 
                              BindingResult bindingResult, 
                              Model model,
                              RedirectAttributes redirectAttributes) {
        
        log.info("Processing create profile form for user ID: {}", userId);
        
        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isEmpty()) {
            return "redirect:/web/users?error=User not found";
        }
        
        User user = userOpt.get();
        profile.setUser(user);
        
        if (bindingResult.hasErrors()) {
            log.warn("Validation errors in create profile form: {}", bindingResult.getAllErrors());
            model.addAttribute("user", user);
            model.addAttribute("profileTypes", UserProfile.ProfileType.values());
            model.addAttribute("pageTitle", "Create Profile - " + user.getFullName());
            model.addAttribute("isEdit", false);
            return "profiles/form";
        }
        
        try {
            UserProfile createdProfile = userProfileService.createProfile(profile);
            redirectAttributes.addFlashAttribute("successMessage", 
                "Profile created successfully: " + createdProfile.getProfileKey());
            return "redirect:/web/users/" + userId + "/profiles";
        } catch (IllegalArgumentException e) {
            log.error("Error creating profile: {}", e.getMessage());
            model.addAttribute("user", user);
            model.addAttribute("errorMessage", e.getMessage());
            model.addAttribute("profileTypes", UserProfile.ProfileType.values());
            model.addAttribute("pageTitle", "Create Profile - " + user.getFullName());
            model.addAttribute("isEdit", false);
            return "profiles/form";
        }
    }

    /**
     * Statistics page
     * 
     * @GetMapping - Maps HTTP GET requests
     */
    @GetMapping("/statistics")
    public String statistics(Model model) {
        log.debug("Rendering statistics page");
        
        UserService.UserStatistics userStats = userService.getUserStatistics();
        UserProfileService.ProfileStatistics profileStats = userProfileService.getProfileStatistics();
        
        model.addAttribute("userStats", userStats);
        model.addAttribute("profileStats", profileStats);
        model.addAttribute("pageTitle", "System Statistics");
        
        return "statistics";
    }
}
