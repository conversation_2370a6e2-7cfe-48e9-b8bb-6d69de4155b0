package com.circlelife.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * Address Entity demonstrating JPA Embeddable
 * 
 * @Embeddable - Marks this class as embeddable in other entities
 * This class can be embedded in other entities using @Embedded
 */
@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class Address {

    /**
     * @Column - Column mapping for embedded fields
     * @Size - Validation constraint
     */
    @Size(max = 255, message = "Street address cannot exceed 255 characters")
    @Column(name = "street_address", length = 255)
    private String streetAddress;

    /**
     * @Size - Length constraint
     */
    @Size(max = 100, message = "City cannot exceed 100 characters")
    @Column(name = "city", length = 100)
    private String city;

    /**
     * @Size - Length constraint
     */
    @Size(max = 100, message = "State cannot exceed 100 characters")
    @Column(name = "state", length = 100)
    private String state;

    /**
     * @Pattern - Regular expression validation
     */
    @Pattern(regexp = "^[0-9]{5}(-[0-9]{4})?$", message = "Invalid postal code format")
    @Column(name = "postal_code", length = 10)
    private String postalCode;

    /**
     * @Size - Length constraint
     */
    @Size(max = 100, message = "Country cannot exceed 100 characters")
    @Column(name = "country", length = 100)
    private String country;

    /**
     * @Column - Additional address information
     */
    @Size(max = 500, message = "Additional info cannot exceed 500 characters")
    @Column(name = "additional_info", length = 500)
    private String additionalInfo;

    /**
     * Utility method to get full address
     */
    public String getFullAddress() {
        StringBuilder fullAddress = new StringBuilder();
        
        if (streetAddress != null && !streetAddress.trim().isEmpty()) {
            fullAddress.append(streetAddress).append(", ");
        }
        
        if (city != null && !city.trim().isEmpty()) {
            fullAddress.append(city).append(", ");
        }
        
        if (state != null && !state.trim().isEmpty()) {
            fullAddress.append(state).append(" ");
        }
        
        if (postalCode != null && !postalCode.trim().isEmpty()) {
            fullAddress.append(postalCode).append(", ");
        }
        
        if (country != null && !country.trim().isEmpty()) {
            fullAddress.append(country);
        }
        
        // Remove trailing comma and space if present
        String result = fullAddress.toString();
        if (result.endsWith(", ")) {
            result = result.substring(0, result.length() - 2);
        }
        
        return result;
    }

    /**
     * Check if address is complete
     */
    public boolean isComplete() {
        return city != null && !city.trim().isEmpty() &&
               country != null && !country.trim().isEmpty();
    }
}
