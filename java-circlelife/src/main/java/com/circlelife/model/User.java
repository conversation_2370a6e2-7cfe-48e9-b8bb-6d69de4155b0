package com.circlelife.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * User Entity demonstrating JPA annotations
 * 
 * @Entity - Marks this class as a JPA entity
 * @Table - Specifies the table name and constraints
 * @EntityListeners - Enables JPA auditing
 */
@Entity
@Table(name = "users",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = "email"),
           @UniqueConstraint(columnNames = "username")
       },
       indexes = {
           @Index(name = "idx_user_email", columnList = "email"),
           @Index(name = "idx_user_username", columnList = "username"),
           @Index(name = "idx_user_status", columnList = "status"),
           @Index(name = "idx_user_created_at", columnList = "created_at")
       })
@EntityListeners(AuditingEntityListener.class)
@SQLDelete(sql = "UPDATE users SET status = 'DELETED' WHERE user_id = ?")
@Where(clause = "status != 'DELETED'")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(exclude = {"password", "profiles"})
public class User {

    /**
     * @Id - Marks this field as the primary key
     * @GeneratedValue - Specifies the primary key generation strategy
     * @Column - Customizes the column mapping
     * @EqualsAndHashCode.Include - Include in equals/hashCode
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_id")
    @EqualsAndHashCode.Include
    private Long id;

    /**
     * @NotBlank - Validation: field cannot be null or empty
     * @Size - Validation: field length constraints
     * @Column - Column customization with nullable and length
     */
    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    @Column(name = "username", nullable = false, length = 50)
    private String username;

    /**
     * @Email - Validation: field must be a valid email format
     * @NotBlank - Validation: field cannot be null or empty
     */
    @Email(message = "Email should be valid")
    @NotBlank(message = "Email is required")
    @Column(name = "email", nullable = false, unique = true, length = 100)
    private String email;

    /**
     * @JsonIgnore - Excludes this field from JSON serialization
     * @NotBlank - Validation: field cannot be null or empty
     * @Size - Validation: minimum length for password
     */
    @JsonIgnore
    @NotBlank(message = "Password is required")
    @Size(min = 6, message = "Password must be at least 6 characters")
    @Column(name = "password", nullable = false)
    private String password;

    /**
     * @NotBlank - Validation: field cannot be null or empty
     * @Size - Validation: field length constraints
     */
    @NotBlank(message = "Full name is required")
    @Size(max = 100, message = "Full name cannot exceed 100 characters")
    @Column(name = "full_name", nullable = false, length = 100)
    private String fullName;

    /**
     * @Min - Validation: minimum value constraint
     * @Max - Validation: maximum value constraint
     */
    @Min(value = 18, message = "Age must be at least 18")
    @Max(value = 120, message = "Age must be less than 120")
    @Column(name = "age")
    private Integer age;

    /**
     * @Enumerated - Specifies how enum should be persisted
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status = UserStatus.ACTIVE;

    /**
     * @CreatedDate - Automatically sets creation timestamp
     * @Column - Column customization
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * @LastModifiedDate - Automatically updates modification timestamp
     */
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * @Embedded - Embeds Address entity
     * @AttributeOverrides - Customizes embedded field column names
     */
    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "streetAddress", column = @Column(name = "address_street")),
        @AttributeOverride(name = "city", column = @Column(name = "address_city")),
        @AttributeOverride(name = "state", column = @Column(name = "address_state")),
        @AttributeOverride(name = "postalCode", column = @Column(name = "address_postal_code")),
        @AttributeOverride(name = "country", column = @Column(name = "address_country")),
        @AttributeOverride(name = "additionalInfo", column = @Column(name = "address_additional_info"))
    })
    private Address address;

    /**
     * @Version - Enables optimistic locking
     */
    @Version
    @Column(name = "version")
    private Long version;

    /**
     * One-to-Many relationship with UserProfile
     * @OneToMany - Defines one-to-many relationship
     * @JoinColumn - Specifies foreign key column
     * @Cascade - Cascade operations to related entities
     * @Fetch - Lazy loading strategy
     */
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference
    private List<UserProfile> profiles = new ArrayList<>();

    /**
     * User Status Enum
     */
    public enum UserStatus {
        ACTIVE, INACTIVE, SUSPENDED, DELETED
    }

    /**
     * Helper method to add profile
     */
    public void addProfile(UserProfile profile) {
        profiles.add(profile);
        profile.setUser(this);
    }

    /**
     * Helper method to remove profile
     */
    public void removeProfile(UserProfile profile) {
        profiles.remove(profile);
        profile.setUser(null);
    }

    /**
     * @PrePersist - JPA callback executed before entity is persisted
     */
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
        if (address == null) {
            address = new Address();
        }
    }

    /**
     * @PreUpdate - JPA callback executed before entity is updated
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
