package com.circlelife.service;

import com.circlelife.model.User;
import com.circlelife.model.UserProfile;
import com.circlelife.repository.UserProfileRepository;
import com.circlelife.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * UserProfile Service demonstrating advanced JPA operations
 * 
 * @Service - Marks this class as a Spring service component
 * @Transactional - Provides transaction management for JPA operations
 * @Cacheable, @CachePut, @CacheEvict - Cache management annotations
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class UserProfileService {

    private final UserProfileRepository userProfileRepository;
    private final UserRepository userRepository;

    /**
     * Create a new user profile
     * 
     * @Transactional - Ensures method runs in a transaction
     * @CachePut - Updates cache with new value
     */
    @CachePut(value = "userProfiles", key = "#result.id")
    public UserProfile createProfile(UserProfile profile) {
        log.info("Creating new profile for user: {}, type: {}", 
                profile.getUser().getId(), profile.getProfileType());
        
        // Validate user exists
        if (!userRepository.existsById(profile.getUser().getId())) {
            throw new IllegalArgumentException("User not found with ID: " + profile.getUser().getId());
        }
        
        // Check for duplicate profile key for the same user
        if (userProfileRepository.existsByUserAndProfileKey(profile.getUser(), profile.getProfileKey())) {
            throw new IllegalArgumentException("Profile key already exists for this user: " + profile.getProfileKey());
        }
        
        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("Profile created successfully with ID: {}", savedProfile.getId());
        return savedProfile;
    }

    /**
     * Find profile by ID
     * 
     * @Cacheable - Caches the result for subsequent calls
     * @Transactional(readOnly = true) - Optimizes for read-only operations
     */
    @Cacheable(value = "userProfiles", key = "#id")
    @Transactional(readOnly = true)
    public Optional<UserProfile> findById(Long id) {
        log.debug("Finding profile by ID: {}", id);
        return userProfileRepository.findById(id);
    }

    /**
     * Find profiles by user ID
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public List<UserProfile> findByUserId(Long userId) {
        log.debug("Finding profiles by user ID: {}", userId);
        return userProfileRepository.findByUserId(userId);
    }

    /**
     * Find active profiles by user ID
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public List<UserProfile> findActiveProfilesByUserId(Long userId) {
        log.debug("Finding active profiles by user ID: {}", userId);
        return userProfileRepository.findActiveProfilesByUserId(userId);
    }

    /**
     * Find profiles by type
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public List<UserProfile> findByProfileType(UserProfile.ProfileType profileType) {
        log.debug("Finding profiles by type: {}", profileType);
        return userProfileRepository.findByProfileType(profileType);
    }

    /**
     * Find profiles by user and type
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public List<UserProfile> findByUserIdAndType(Long userId, UserProfile.ProfileType profileType) {
        log.debug("Finding profiles by user ID: {} and type: {}", userId, profileType);
        return userProfileRepository.findByUserIdAndProfileType(userId, profileType);
    }

    /**
     * Search profiles with multiple criteria
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public Page<UserProfile> searchProfiles(Long userId, UserProfile.ProfileType profileType,
                                           Boolean isActive, String searchText, Pageable pageable) {
        log.debug("Searching profiles with criteria - userId: {}, type: {}, active: {}, search: {}",
                 userId, profileType, isActive, searchText);
        // For now, ignore searchText parameter until we implement proper search
        return userProfileRepository.searchProfiles(userId, profileType, isActive, pageable);
    }

    /**
     * Update profile
     * 
     * @CachePut - Updates cache with new value
     */
    @CachePut(value = "userProfiles", key = "#profile.id")
    public UserProfile updateProfile(UserProfile profile) {
        log.info("Updating profile: {}", profile.getId());
        
        if (!userProfileRepository.existsById(profile.getId())) {
            throw new IllegalArgumentException("Profile not found with ID: " + profile.getId());
        }
        
        UserProfile updatedProfile = userProfileRepository.save(profile);
        log.info("Profile updated successfully: {}", updatedProfile.getId());
        return updatedProfile;
    }

    /**
     * Update profile value only
     * 
     * @CacheEvict - Removes entry from cache since we're doing partial update
     */
    @CacheEvict(value = "userProfiles", key = "#id")
    public boolean updateProfileValue(Long id, String value) {
        log.info("Updating profile value for ID: {}", id);
        
        int updatedRows = userProfileRepository.updateProfileValue(id, value);
        boolean success = updatedRows > 0;
        
        if (success) {
            log.info("Profile value updated successfully for ID: {}", id);
        } else {
            log.warn("No profile found to update with ID: {}", id);
        }
        
        return success;
    }

    /**
     * Delete profile by ID
     * 
     * @CacheEvict - Removes entry from cache
     */
    @CacheEvict(value = "userProfiles", key = "#id")
    public void deleteProfile(Long id) {
        log.info("Deleting profile: {}", id);
        
        if (!userProfileRepository.existsById(id)) {
            throw new IllegalArgumentException("Profile not found with ID: " + id);
        }
        
        userProfileRepository.deleteById(id);
        log.info("Profile deleted successfully: {}", id);
    }

    /**
     * Deactivate profile (soft delete)
     * 
     * @CachePut - Updates cache with new value
     */
    @CachePut(value = "userProfiles", key = "#id")
    public UserProfile deactivateProfile(Long id) {
        log.info("Deactivating profile: {}", id);
        
        UserProfile profile = userProfileRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Profile not found with ID: " + id));
        
        profile.setIsActive(false);
        UserProfile updatedProfile = userProfileRepository.save(profile);
        
        log.info("Profile deactivated successfully: {}", id);
        return updatedProfile;
    }

    /**
     * Deactivate all profiles for a user
     * 
     * @CacheEvict - Clears cache for affected entries
     */
    @CacheEvict(value = "userProfiles", allEntries = true)
    public int deactivateProfilesByUserId(Long userId) {
        log.info("Deactivating all profiles for user: {}", userId);
        
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        int deactivatedCount = userProfileRepository.deactivateProfilesByUser(user);
        log.info("Deactivated {} profiles for user: {}", deactivatedCount, userId);
        return deactivatedCount;
    }

    /**
     * Get profile statistics by type
     * 
     * @Cacheable - Caches the result
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Cacheable(value = "profileStats", key = "'statistics'")
    @Transactional(readOnly = true)
    public ProfileStatistics getProfileStatistics() {
        log.debug("Calculating profile statistics");
        
        List<Object[]> stats = userProfileRepository.getProfileStatisticsByType();
        long totalProfiles = userProfileRepository.count();
        
        ProfileStatistics.Builder builder = ProfileStatistics.builder()
            .totalProfiles(totalProfiles);
        
        for (Object[] stat : stats) {
            UserProfile.ProfileType type = (UserProfile.ProfileType) stat[0];
            Long count = (Long) stat[1];
            
            switch (type) {
                case PERSONAL -> builder.personalProfiles(count);
                case CONTACT -> builder.contactProfiles(count);
                case PREFERENCE -> builder.preferenceProfiles(count);
                case SOCIAL -> builder.socialProfiles(count);
                case PROFESSIONAL -> builder.professionalProfiles(count);
                case CUSTOM -> builder.customProfiles(count);
            }
        }
        
        return builder.build();
    }

    /**
     * Find recent profiles (created in last N days)
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public List<UserProfile> findRecentProfiles(int days) {
        log.debug("Finding profiles created in last {} days", days);
        return userProfileRepository.findRecentActiveProfiles(days);
    }

    /**
     * Cleanup inactive profiles older than specified days
     * 
     * @CacheEvict - Clears all cache entries since we're doing bulk delete
     */
    @CacheEvict(value = "userProfiles", allEntries = true)
    public int cleanupInactiveProfiles(int days) {
        log.info("Cleaning up inactive profiles older than {} days", days);
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        int deletedCount = userProfileRepository.deleteInactiveProfilesOlderThan(cutoffDate);
        
        log.info("Cleaned up {} inactive profiles", deletedCount);
        return deletedCount;
    }

    /**
     * Find profile by user and key
     * 
     * @Transactional(readOnly = true) - Read-only transaction
     */
    @Transactional(readOnly = true)
    public Optional<UserProfile> findByUserIdAndKey(Long userId, String profileKey) {
        log.debug("Finding profile by user ID: {} and key: {}", userId, profileKey);
        
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        return userProfileRepository.findByUserAndProfileKey(user, profileKey);
    }

    /**
     * Clear all profile caches
     * 
     * @CacheEvict - Clears all entries from specified caches
     */
    @CacheEvict(value = {"userProfiles", "profileStats"}, allEntries = true)
    public void clearProfileCaches() {
        log.info("Clearing all profile caches");
    }

    /**
     * Profile Statistics record
     */
    public record ProfileStatistics(
        long totalProfiles,
        long personalProfiles,
        long contactProfiles,
        long preferenceProfiles,
        long socialProfiles,
        long professionalProfiles,
        long customProfiles
    ) {
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private long totalProfiles;
            private long personalProfiles;
            private long contactProfiles;
            private long preferenceProfiles;
            private long socialProfiles;
            private long professionalProfiles;
            private long customProfiles;
            
            public Builder totalProfiles(long totalProfiles) {
                this.totalProfiles = totalProfiles;
                return this;
            }
            
            public Builder personalProfiles(long personalProfiles) {
                this.personalProfiles = personalProfiles;
                return this;
            }
            
            public Builder contactProfiles(long contactProfiles) {
                this.contactProfiles = contactProfiles;
                return this;
            }
            
            public Builder preferenceProfiles(long preferenceProfiles) {
                this.preferenceProfiles = preferenceProfiles;
                return this;
            }
            
            public Builder socialProfiles(long socialProfiles) {
                this.socialProfiles = socialProfiles;
                return this;
            }
            
            public Builder professionalProfiles(long professionalProfiles) {
                this.professionalProfiles = professionalProfiles;
                return this;
            }
            
            public Builder customProfiles(long customProfiles) {
                this.customProfiles = customProfiles;
                return this;
            }
            
            public ProfileStatistics build() {
                return new ProfileStatistics(totalProfiles, personalProfiles, contactProfiles,
                    preferenceProfiles, socialProfiles, professionalProfiles, customProfiles);
            }
        }
    }
}
