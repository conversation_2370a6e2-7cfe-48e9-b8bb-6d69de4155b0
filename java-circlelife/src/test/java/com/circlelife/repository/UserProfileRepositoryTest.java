package com.circlelife.repository;

import com.circlelife.model.User;
import com.circlelife.model.UserProfile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserProfile Repository Test demonstrating JPA testing annotations
 * 
 * @DataJpaTest - Configures JPA repositories and in-memory database for testing
 * @ActiveProfiles - Specifies which profiles to activate during testing
 */
@DataJpaTest
@ActiveProfiles("test")
@Transactional
@Rollback
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class UserProfileRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserRepository userRepository;

    private User testUser;
    private UserProfile testProfile;

    /**
     * @BeforeEach - Setup method that runs before each test
     */
    @BeforeEach
    void setUp() {
        // Create and persist test user
        testUser = new User();
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setFullName("Test User");
        testUser.setAge(25);
        testUser.setStatus(User.UserStatus.ACTIVE);
        testUser = entityManager.persistAndFlush(testUser);

        // Create test profile
        testProfile = new UserProfile();
        testProfile.setUser(testUser);
        testProfile.setProfileType(UserProfile.ProfileType.PERSONAL);
        testProfile.setProfileKey("bio");
        testProfile.setProfileValue("Test bio");
        testProfile.setIsActive(true);
        testProfile = entityManager.persistAndFlush(testProfile);

        entityManager.clear();
    }

    /**
     * @Test - Test method for finding profiles by user
     */
    @Test
    void findByUser_ShouldReturnProfiles_WhenUserExists() {
        // When
        List<UserProfile> profiles = userProfileRepository.findByUser(testUser);

        // Then
        assertNotNull(profiles);
        assertEquals(1, profiles.size());
        assertEquals(testProfile.getId(), profiles.get(0).getId());
        assertEquals(testProfile.getProfileKey(), profiles.get(0).getProfileKey());
    }

    /**
     * @Test - Test method for finding profiles by user ID
     */
    @Test
    void findByUserId_ShouldReturnProfiles_WhenUserExists() {
        // When
        List<UserProfile> profiles = userProfileRepository.findByUserId(testUser.getId());

        // Then
        assertNotNull(profiles);
        assertEquals(1, profiles.size());
        assertEquals(testProfile.getId(), profiles.get(0).getId());
    }

    /**
     * @Test - Test method for finding profiles by profile type
     */
    @Test
    void findByProfileType_ShouldReturnProfiles_WhenTypeMatches() {
        // Given - Clear existing data and create fresh test data
        userProfileRepository.deleteAll();
        entityManager.flush();

        User newUser = createTestUser("findByType", "<EMAIL>");
        UserProfile newProfile = createTestProfile(newUser, "test-key", "test-value", UserProfile.ProfileType.CONTACT);

        // When
        List<UserProfile> profiles = userProfileRepository.findByProfileType(UserProfile.ProfileType.CONTACT);

        // Then
        assertNotNull(profiles);
        assertEquals(1, profiles.size());
        assertEquals(newProfile.getId(), profiles.get(0).getId());
    }

    /**
     * @Test - Test method for finding profiles by user and profile type
     */
    @Test
    void findByUserAndProfileType_ShouldReturnProfiles_WhenBothMatch() {
        // When
        List<UserProfile> profiles = userProfileRepository.findByUserAndProfileType(
            testUser, UserProfile.ProfileType.PERSONAL);

        // Then
        assertNotNull(profiles);
        assertEquals(1, profiles.size());
        assertEquals(testProfile.getId(), profiles.get(0).getId());
    }

    /**
     * @Test - Test method for finding active profiles by user
     */
    @Test
    void findByUserAndIsActiveTrue_ShouldReturnActiveProfiles() {
        // Given - Create an inactive profile
        UserProfile inactiveProfile = new UserProfile();
        inactiveProfile.setUser(testUser);
        inactiveProfile.setProfileType(UserProfile.ProfileType.CONTACT);
        inactiveProfile.setProfileKey("phone");
        inactiveProfile.setProfileValue("************");
        inactiveProfile.setIsActive(false);
        entityManager.persistAndFlush(inactiveProfile);

        // When
        List<UserProfile> activeProfiles = userProfileRepository.findByUserAndIsActiveTrue(testUser);

        // Then
        assertNotNull(activeProfiles);
        assertEquals(1, activeProfiles.size());
        assertEquals(testProfile.getId(), activeProfiles.get(0).getId());
        assertTrue(activeProfiles.get(0).getIsActive());
    }

    /**
     * @Test - Test method for checking if profile exists by user and key
     */
    @Test
    void existsByUserAndProfileKey_ShouldReturnTrue_WhenExists() {
        // When
        boolean exists = userProfileRepository.existsByUserAndProfileKey(testUser, "bio");

        // Then
        assertTrue(exists);
    }

    /**
     * @Test - Test method for checking if profile exists by user and key when not exists
     */
    @Test
    void existsByUserAndProfileKey_ShouldReturnFalse_WhenNotExists() {
        // When
        boolean exists = userProfileRepository.existsByUserAndProfileKey(testUser, "nonexistent");

        // Then
        assertFalse(exists);
    }

    /**
     * @Test - Test method for counting profiles by user
     */
    @Test
    void countByUser_ShouldReturnCorrectCount() {
        // Given - Create additional profile
        UserProfile additionalProfile = new UserProfile();
        additionalProfile.setUser(testUser);
        additionalProfile.setProfileType(UserProfile.ProfileType.CONTACT);
        additionalProfile.setProfileKey("email");
        additionalProfile.setProfileValue("<EMAIL>");
        additionalProfile.setIsActive(true);
        entityManager.persistAndFlush(additionalProfile);

        // When
        long count = userProfileRepository.countByUser(testUser);

        // Then
        assertEquals(2, count);
    }

    /**
     * @Test - Test method for counting profiles by type
     */
    @Test
    void countByProfileType_ShouldReturnCorrectCount() {
        // Given - Clear existing data and create fresh test data
        userProfileRepository.deleteAll();
        entityManager.flush();

        User newUser = createTestUser("countByType", "<EMAIL>");
        UserProfile newProfile = createTestProfile(newUser, "test-key", "test-value", UserProfile.ProfileType.PROFESSIONAL);

        // When
        long count = userProfileRepository.countByProfileType(UserProfile.ProfileType.PROFESSIONAL);

        // Then
        assertEquals(1, count);
    }

    /**
     * @Test - Test method for finding profiles with pagination
     */
    @Test
    void findByUser_WithPageable_ShouldReturnPagedResults() {
        // Given - Create additional profiles
        for (int i = 0; i < 5; i++) {
            UserProfile profile = new UserProfile();
            profile.setUser(testUser);
            profile.setProfileType(UserProfile.ProfileType.CUSTOM);
            profile.setProfileKey("key" + i);
            profile.setProfileValue("value" + i);
            profile.setIsActive(true);
            entityManager.persistAndFlush(profile);
        }

        // When
        Pageable pageable = PageRequest.of(0, 3);
        Page<UserProfile> profilePage = userProfileRepository.findByUser(testUser, pageable);

        // Then
        assertNotNull(profilePage);
        assertEquals(3, profilePage.getSize());
        assertEquals(6, profilePage.getTotalElements()); // 1 original + 5 new
        assertTrue(profilePage.hasContent());
    }

    /**
     * @Test - Test method for custom query - findActiveProfilesByUserId
     */
    @Test
    void findActiveProfilesByUserId_ShouldReturnActiveProfiles() {
        // Given - Create inactive profile
        UserProfile inactiveProfile = new UserProfile();
        inactiveProfile.setUser(testUser);
        inactiveProfile.setProfileType(UserProfile.ProfileType.CONTACT);
        inactiveProfile.setProfileKey("phone");
        inactiveProfile.setProfileValue("************");
        inactiveProfile.setIsActive(false);
        entityManager.persistAndFlush(inactiveProfile);

        // When
        List<UserProfile> activeProfiles = userProfileRepository.findActiveProfilesByUserId(testUser.getId());

        // Then
        assertNotNull(activeProfiles);
        assertEquals(1, activeProfiles.size());
        assertEquals(testProfile.getId(), activeProfiles.get(0).getId());
        assertTrue(activeProfiles.get(0).getIsActive());
    }

    /**
     * @Test - Test method for finding profile by user and key
     */
    @Test
    void findByUserAndProfileKey_ShouldReturnProfile_WhenExists() {
        // When
        Optional<UserProfile> foundProfile = userProfileRepository.findByUserAndProfileKey(testUser, "bio");

        // Then
        assertTrue(foundProfile.isPresent());
        assertEquals(testProfile.getId(), foundProfile.get().getId());
        assertEquals("bio", foundProfile.get().getProfileKey());
    }

    /**
     * @Test - Test method for finding profile by user and key when not exists
     */
    @Test
    void findByUserAndProfileKey_ShouldReturnEmpty_WhenNotExists() {
        // When
        Optional<UserProfile> foundProfile = userProfileRepository.findByUserAndProfileKey(testUser, "nonexistent");

        // Then
        assertFalse(foundProfile.isPresent());
    }

    /**
     * @Test - Test method for search profiles with multiple criteria
     */
    @Test
    void searchProfiles_ShouldReturnMatchingProfiles() {
        // Given - Create additional profiles
        UserProfile contactProfile = new UserProfile();
        contactProfile.setUser(testUser);
        contactProfile.setProfileType(UserProfile.ProfileType.CONTACT);
        contactProfile.setProfileKey("email");
        contactProfile.setProfileValue("<EMAIL>");
        contactProfile.setIsActive(true);
        entityManager.persistAndFlush(contactProfile);

        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<UserProfile> results = userProfileRepository.searchProfiles(
            testUser.getId(), null, true, pageable);

        // Then
        assertNotNull(results);
        assertEquals(2, results.getTotalElements());
        assertTrue(results.getContent().stream()
            .allMatch(profile -> profile.getIsActive()));
    }

    /**
     * @Test - Test method for finding profiles by value containing text
     */
    @Test
    void findProfilesByValueContaining_ShouldReturnMatchingProfiles() {
        // When
        List<UserProfile> profiles = userProfileRepository.findProfilesByValueContaining("bio");

        // Then
        assertNotNull(profiles);
        assertEquals(1, profiles.size());
        assertEquals(testProfile.getId(), profiles.get(0).getId());
    }

    /**
     * @Test - Test method for getting profile statistics by type
     */
    @Test
    void getProfileStatisticsByType_ShouldReturnStatistics() {
        // Given - Create profiles of different types
        UserProfile contactProfile = new UserProfile();
        contactProfile.setUser(testUser);
        contactProfile.setProfileType(UserProfile.ProfileType.CONTACT);
        contactProfile.setProfileKey("phone");
        contactProfile.setProfileValue("************");
        contactProfile.setIsActive(true);
        entityManager.persistAndFlush(contactProfile);

        UserProfile socialProfile = new UserProfile();
        socialProfile.setUser(testUser);
        socialProfile.setProfileType(UserProfile.ProfileType.SOCIAL);
        socialProfile.setProfileKey("linkedin");
        socialProfile.setProfileValue("https://linkedin.com/in/test");
        socialProfile.setIsActive(true);
        entityManager.persistAndFlush(socialProfile);

        // When
        List<Object[]> statistics = userProfileRepository.getProfileStatisticsByType();

        // Then
        assertNotNull(statistics);
        assertTrue(statistics.size() >= 3); // At least PERSONAL, CONTACT, SOCIAL
        
        // Verify statistics contain expected types
        boolean hasPersonal = statistics.stream()
            .anyMatch(stat -> stat[0] == UserProfile.ProfileType.PERSONAL);
        boolean hasContact = statistics.stream()
            .anyMatch(stat -> stat[0] == UserProfile.ProfileType.CONTACT);
        boolean hasSocial = statistics.stream()
            .anyMatch(stat -> stat[0] == UserProfile.ProfileType.SOCIAL);
        
        assertTrue(hasPersonal);
        assertTrue(hasContact);
        assertTrue(hasSocial);
    }

    // Helper methods
    private User createTestUser(String username, String email) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setFullName("Test User");
        user.setPassword("password");
        user.setAge(25);
        user.setStatus(User.UserStatus.ACTIVE);
        return entityManager.persistAndFlush(user);
    }

    private UserProfile createTestProfile(User user, String key, String value, UserProfile.ProfileType type) {
        UserProfile profile = new UserProfile();
        profile.setUser(user);
        profile.setProfileKey(key);
        profile.setProfileValue(value);
        profile.setProfileType(type);
        profile.setIsActive(true);
        return entityManager.persistAndFlush(profile);
    }
}
