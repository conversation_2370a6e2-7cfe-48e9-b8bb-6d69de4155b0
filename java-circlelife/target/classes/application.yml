# Spring Boot Application Configuration
# Demonstrating @Value and @ConfigurationProperties usage

spring:
  # Application Configuration
  application:
    name: java-circlelife
  
  # Profile Configuration
  profiles:
    active: dev
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:circlelife
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  # H2 Console Configuration
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    defer-datasource-initialization: true
  
  # SQL Initialization
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
  
  # Cache Configuration
  cache:
    type: simple
    cache-names:
      - users
      - userStats
      - userProfiles
      - profileStats
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC
    date-format: yyyy-MM-dd HH:mm:ss

  # Thymeleaf Configuration
  thymeleaf:
    cache: false
    mode: HTML
    encoding: UTF-8
    prefix: classpath:/templates/
    suffix: .html
  
  # Security Configuration (Basic)
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,beans,mappings
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# Application Custom Properties
app:
  name: Java CircleLife
  version: 1.0.0
  description: Spring Boot Application demonstrating all annotations with use cases
  
  # CORS Configuration
  cors:
    allowed-origins: http://localhost:3000,http://localhost:4200
    allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
  
  # Async Configuration
  async:
    core-pool-size: 2
    max-pool-size: 10
    queue-capacity: 500
    thread-name-prefix: CircleLife-Async-
  
  # Cache Configuration
  cache:
    ttl: 3600 # 1 hour in seconds
    max-entries: 1000
  
  # Scheduling Configuration
  scheduling:
    enabled: true
    pool-size: 5

# Logging Configuration
logging:
  level:
    com.circlelife: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/circlelife.log

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  info:
    title: Java CircleLife API
    description: Spring Boot Application demonstrating all annotations with use cases
    version: 1.0.0
    contact:
      name: CircleLife Team
      email: <EMAIL>

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: jdbc:h2:mem:circlelife-dev
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop

logging:
  level:
    com.circlelife: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: **************************************
    username: ${DB_USERNAME:circlelife}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  h2:
    console:
      enabled: false

logging:
  level:
    com.circlelife: INFO
    org.springframework: WARN
    org.hibernate: WARN

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:circlelife-test
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false

logging:
  level:
    com.circlelife: WARN
