-- Sample data for Java CircleLife Application
-- This file demonstrates SQL initialization in Spring Boot with JPA relationships

-- Insert sample users with address information
INSERT INTO users (username, email, password, full_name, age, status,
                  address_street, address_city, address_state, address_postal_code, address_country,
                  created_at, updated_at, version) VALUES
('john_doe', '<EMAIL>', 'password123', '<PERSON>', 25, 'ACTIVE',
 '123 Main St', 'New York', 'NY', '10001', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('jane_smith', '<EMAIL>', 'password123', '<PERSON>', 30, 'ACTIVE',
 '456 Oak Ave', 'Los Angeles', 'CA', '90210', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('bob_wilson', '<EMAIL>', 'password123', '<PERSON>', 35, 'INACTIVE',
 '789 Pine Rd', 'Chicago', 'IL', '60601', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('alice_brown', '<EMAIL>', 'password123', '<PERSON> Brown', 28, 'ACTIVE',
 '321 Elm St', 'Houston', 'TX', '77001', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('charlie_davis', '<EMAIL>', 'password123', 'Charlie Davis', 42, '<PERSON>USPENDED',
 '654 Maple Dr', 'Phoenix', 'AZ', '85001', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('diana_miller', '<EMAIL>', 'password123', 'Diana Miller', 26, 'ACTIVE',
 '987 Cedar Ln', 'Philadelphia', 'PA', '19101', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('edward_jones', '<EMAIL>', 'password123', 'Edward Jones', 38, 'DELETED',
 '147 Birch Way', 'San Antonio', 'TX', '78201', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('fiona_garcia', '<EMAIL>', 'password123', 'Fiona Garcia', 31, 'ACTIVE',
 '258 Spruce St', 'San Diego', 'CA', '92101', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('george_martinez', '<EMAIL>', 'password123', 'George Martinez', 29, 'ACTIVE',
 '369 Willow Ave', 'Dallas', 'TX', '75201', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('helen_rodriguez', '<EMAIL>', 'password123', 'Helen Rodriguez', 33, 'INACTIVE',
 '741 Aspen Blvd', 'San Jose', 'CA', '95101', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0);

-- Additional test data for different scenarios
INSERT INTO users (username, email, password, full_name, age, status,
                  address_street, address_city, address_state, address_postal_code, address_country,
                  created_at, updated_at, version) VALUES
('test_user1', '<EMAIL>', 'test123', 'Test User One', 22, 'ACTIVE',
 '111 Test St', 'Boston', 'MA', '02101', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('test_user2', '<EMAIL>', 'test123', 'Test User Two', 45, 'ACTIVE',
 '222 Demo Ave', 'Seattle', 'WA', '98101', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('test_user3', '<EMAIL>', 'test123', 'Test User Three', 19, 'SUSPENDED',
 '333 Sample Rd', 'Denver', 'CO', '80201', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('admin_user', '<EMAIL>', 'admin123', 'Admin User', 40, 'ACTIVE',
 '444 Admin Blvd', 'Washington', 'DC', '20001', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
('demo_user', '<EMAIL>', 'demo123', 'Demo User', 27, 'ACTIVE',
 '555 Demo Dr', 'Miami', 'FL', '33101', 'USA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0);

-- Insert sample user profiles demonstrating JPA relationships
-- Personal profiles
INSERT INTO user_profiles (user_id, profile_type, profile_key, profile_value, is_active, created_at, updated_at) VALUES
(1, 'PERSONAL', 'bio', 'Software developer passionate about technology and innovation.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'PERSONAL', 'interests', 'Programming, Reading, Hiking, Photography', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PERSONAL', 'bio', 'Marketing professional with 8+ years of experience.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PERSONAL', 'hobbies', 'Yoga, Cooking, Traveling', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'PERSONAL', 'bio', 'UX/UI designer creating beautiful and functional interfaces.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Contact profiles
INSERT INTO user_profiles (user_id, profile_type, profile_key, profile_value, is_active, created_at, updated_at) VALUES
(1, 'CONTACT', 'phone', '******-0101', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'CONTACT', 'emergency_contact', 'Jane Doe - ******-0102', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'CONTACT', 'phone', '******-0201', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'CONTACT', 'work_phone', '******-0202', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'CONTACT', 'phone', '******-0401', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Social media profiles
INSERT INTO user_profiles (user_id, profile_type, profile_key, profile_value, is_active, created_at, updated_at) VALUES
(1, 'SOCIAL', 'linkedin', 'https://linkedin.com/in/johndoe', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'SOCIAL', 'github', 'https://github.com/johndoe', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'SOCIAL', 'twitter', 'https://twitter.com/johndoe', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'SOCIAL', 'linkedin', 'https://linkedin.com/in/janesmith', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'SOCIAL', 'instagram', 'https://instagram.com/janesmith', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'SOCIAL', 'behance', 'https://behance.net/alicebrown', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'SOCIAL', 'dribbble', 'https://dribbble.com/alicebrown', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Professional profiles
INSERT INTO user_profiles (user_id, profile_type, profile_key, profile_value, is_active, created_at, updated_at) VALUES
(1, 'PROFESSIONAL', 'company', 'TechCorp Inc.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'PROFESSIONAL', 'position', 'Senior Software Engineer', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'PROFESSIONAL', 'skills', 'Java, Spring Boot, React, PostgreSQL, AWS', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PROFESSIONAL', 'company', 'Marketing Solutions LLC', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PROFESSIONAL', 'position', 'Marketing Manager', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PROFESSIONAL', 'skills', 'Digital Marketing, SEO, Content Strategy, Analytics', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'PROFESSIONAL', 'company', 'Design Studio Pro', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'PROFESSIONAL', 'position', 'Senior UX Designer', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'PROFESSIONAL', 'skills', 'Figma, Sketch, Adobe Creative Suite, User Research', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Preference profiles
INSERT INTO user_profiles (user_id, profile_type, profile_key, profile_value, is_active, created_at, updated_at) VALUES
(1, 'PREFERENCE', 'theme', 'dark', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'PREFERENCE', 'language', 'en-US', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'PREFERENCE', 'notifications', '{"email": true, "sms": false, "push": true}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PREFERENCE', 'theme', 'light', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PREFERENCE', 'language', 'en-US', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'PREFERENCE', 'timezone', 'America/Los_Angeles', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'PREFERENCE', 'theme', 'auto', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'PREFERENCE', 'language', 'en-US', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Custom profiles
INSERT INTO user_profiles (user_id, profile_type, profile_key, profile_value, is_active, created_at, updated_at) VALUES
(1, 'CUSTOM', 'favorite_quote', 'Code is poetry in motion.', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1, 'CUSTOM', 'programming_setup', '{"editor": "IntelliJ IDEA", "os": "macOS", "keyboard": "mechanical"}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'CUSTOM', 'favorite_books', 'The Lean Startup, Purple Cow, Made to Stick', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2, 'CUSTOM', 'travel_wishlist', 'Japan, Iceland, New Zealand, Patagonia', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'CUSTOM', 'design_inspiration', 'Minimalism, Bauhaus, Scandinavian Design', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(4, 'CUSTOM', 'color_palette', '{"primary": "#2563eb", "secondary": "#64748b", "accent": "#f59e0b"}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
