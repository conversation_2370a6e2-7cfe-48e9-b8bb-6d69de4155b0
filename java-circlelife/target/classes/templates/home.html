<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title>Dashboard - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="page-header text-center">
            <h1><i class="fas fa-tachometer-alt me-3"></i>CircleLife Dashboard</h1>
            <p class="lead mb-0">Welcome to the Spring Boot Annotations Demo Application</p>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0" th:text="${userStats.totalUsers}">0</h3>
                            <p class="mb-0">Total Users</p>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0" th:text="${userStats.activeUsers}">0</h3>
                            <p class="mb-0">Active Users</p>
                        </div>
                        <i class="fas fa-user-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0" th:text="${profileStats.totalProfiles}">0</h3>
                            <p class="mb-0">Total Profiles</p>
                        </div>
                        <i class="fas fa-id-card fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card info">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0" th:text="${userStats.suspendedUsers + userStats.inactiveUsers}">0</h3>
                            <p class="mb-0">Inactive Users</p>
                        </div>
                        <i class="fas fa-user-times fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/web/users/create}" class="btn btn-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>Add New User
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/web/users}" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-list me-2"></i>View All Users
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/web/statistics}" class="btn btn-outline-success w-100">
                                    <i class="fas fa-chart-bar me-2"></i>View Statistics
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/swagger-ui.html}" target="_blank" class="btn btn-outline-info w-100">
                                    <i class="fas fa-code me-2"></i>API Documentation
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Status Breakdown -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>User Status Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 text-center">
                                <div class="border-end">
                                    <h4 class="text-success" th:text="${userStats.activeUsers}">0</h4>
                                    <span class="badge bg-success">Active</span>
                                </div>
                            </div>
                            <div class="col-6 text-center">
                                <h4 class="text-warning" th:text="${userStats.inactiveUsers}">0</h4>
                                <span class="badge bg-warning">Inactive</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6 text-center">
                                <div class="border-end">
                                    <h4 class="text-danger" th:text="${userStats.suspendedUsers}">0</h4>
                                    <span class="badge bg-danger">Suspended</span>
                                </div>
                            </div>
                            <div class="col-6 text-center">
                                <h4 class="text-secondary" th:text="${userStats.deletedUsers}">0</h4>
                                <span class="badge bg-secondary">Deleted</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>Profile Types</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h6 class="text-primary" th:text="${profileStats.personalProfiles}">0</h6>
                                    <small class="text-muted">Personal</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h6 class="text-info" th:text="${profileStats.contactProfiles}">0</h6>
                                    <small class="text-muted">Contact</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h6 class="text-success" th:text="${profileStats.socialProfiles}">0</h6>
                                    <small class="text-muted">Social</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h6 class="text-warning" th:text="${profileStats.professionalProfiles}">0</h6>
                                    <small class="text-muted">Professional</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Application Features</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-database text-primary me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">JPA Integration</h6>
                                        <small class="text-muted">Complete JPA with relationships, caching, and advanced queries</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-shield-alt text-success me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">Validation</h6>
                                        <small class="text-muted">Bean validation with custom constraints and error handling</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tachometer-alt text-warning me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">Caching</h6>
                                        <small class="text-muted">Multi-level caching with Spring Cache abstraction</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-info me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">Scheduling</h6>
                                        <small class="text-muted">Automated tasks with cron expressions and async processing</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-code text-danger me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">REST API</h6>
                                        <small class="text-muted">Complete RESTful API with OpenAPI documentation</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-vial text-secondary me-3 fa-2x"></i>
                                    <div>
                                        <h6 class="mb-1">Testing</h6>
                                        <small class="text-muted">Comprehensive testing with JUnit 5, Mockito, and TestContainers</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Stack -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Technology Stack</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2 mb-3">
                                <i class="fab fa-java text-danger fa-3x mb-2"></i>
                                <h6>Java 17</h6>
                            </div>
                            <div class="col-md-2 mb-3">
                                <i class="fas fa-leaf text-success fa-3x mb-2"></i>
                                <h6>Spring Boot 3</h6>
                            </div>
                            <div class="col-md-2 mb-3">
                                <i class="fas fa-database text-primary fa-3x mb-2"></i>
                                <h6>JPA/Hibernate</h6>
                            </div>
                            <div class="col-md-2 mb-3">
                                <i class="fas fa-code text-info fa-3x mb-2"></i>
                                <h6>Thymeleaf</h6>
                            </div>
                            <div class="col-md-2 mb-3">
                                <i class="fab fa-bootstrap text-purple fa-3x mb-2"></i>
                                <h6>Bootstrap 5</h6>
                            </div>
                            <div class="col-md-2 mb-3">
                                <i class="fas fa-server text-warning fa-3x mb-2"></i>
                                <h6>H2 Database</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
