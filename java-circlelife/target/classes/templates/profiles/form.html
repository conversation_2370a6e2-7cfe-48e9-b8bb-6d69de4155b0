<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title th:text="${isEdit ? 'Edit Profile' : 'Create Profile'} + ' - ' + ${user.fullName} + ' - CircleLife'">Profile Form - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i th:class="${isEdit ? 'fas fa-edit' : 'fas fa-plus'}" class="me-2"></i>
                    <span th:text="${isEdit ? 'Edit Profile' : 'Create New Profile'}">Profile Form</span>
                </h2>
                <p class="text-muted mb-0">
                    <span th:text="${isEdit ? 'Update profile information for' : 'Add a new profile for'}">Action</span>
                    <strong th:text="${user.fullName}">User Name</strong>
                    <span class="badge bg-primary ms-2" th:text="'@' + ${user.username}">@username</span>
                </p>
            </div>
            <div class="btn-group" role="group">
                <a th:href="@{/web/users/{id}/profiles(id=${user.id})}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profiles
                </a>
                <a th:href="@{/web/users/{id}(id=${user.id})}" class="btn btn-outline-info">
                    <i class="fas fa-user me-2"></i>View User
                </a>
            </div>
        </div>

        <!-- User Info Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                             style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                            <span th:text="${#strings.substring(user.fullName, 0, 1)}">U</span>
                        </div>
                    </div>
                    <div class="col-md-10">
                        <h5 class="mb-1" th:text="${user.fullName}">Full Name</h5>
                        <p class="text-muted mb-1" th:text="${user.email}"><EMAIL></p>
                        <span class="badge" 
                              th:class="${user.status == T(com.circlelife.model.User.UserStatus).ACTIVE ? 'bg-success' : 
                                         user.status == T(com.circlelife.model.User.UserStatus).INACTIVE ? 'bg-warning' : 
                                         user.status == T(com.circlelife.model.User.UserStatus).SUSPENDED ? 'bg-danger' : 'bg-secondary'}"
                              th:text="${user.status}">ACTIVE</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-id-card me-2"></i>Profile Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="${isEdit ? '/web/users/' + user.id + '/profiles/' + profile.id + '/edit' : '/web/users/' + user.id + '/profiles/create'}" 
                              th:object="${profile}" method="post" novalidate>
                            
                            <!-- Hidden user ID -->
                            <input type="hidden" th:field="*{user.id}">
                            
                            <!-- Profile Type -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-tag me-2"></i>Profile Type & Key
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="profileType" class="form-label">Profile Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="profileType" th:field="*{profileType}" 
                                            th:class="${#fields.hasErrors('profileType')} ? 'form-select is-invalid' : 'form-select'"
                                            onchange="updateProfileTypeInfo()" required>
                                        <option value="">Select Profile Type</option>
                                        <option th:each="type : ${profileTypes}" 
                                                th:value="${type}" 
                                                th:text="${type}">Type</option>
                                    </select>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('profileType')}" th:errors="*{profileType}">
                                        Profile type error
                                    </div>
                                    <div class="form-text" id="profileTypeHelp">
                                        Select the category that best describes this profile
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="profileKey" class="form-label">Profile Key <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="profileKey" th:field="*{profileKey}" 
                                           th:class="${#fields.hasErrors('profileKey')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter profile key" required>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('profileKey')}" th:errors="*{profileKey}">
                                        Profile key error
                                    </div>
                                    <div class="form-text">
                                        Unique identifier for this profile (e.g., "bio", "phone", "linkedin")
                                    </div>
                                </div>
                            </div>

                            <!-- Profile Value -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-edit me-2"></i>Profile Content
                                    </h6>
                                </div>
                                
                                <div class="col-md-12 mb-3">
                                    <label for="profileValue" class="form-label">Profile Value</label>
                                    <textarea class="form-control" id="profileValue" th:field="*{profileValue}" 
                                              th:class="${#fields.hasErrors('profileValue')} ? 'form-control is-invalid' : 'form-control'"
                                              rows="6" placeholder="Enter profile value or content"></textarea>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('profileValue')}" th:errors="*{profileValue}">
                                        Profile value error
                                    </div>
                                    <div class="form-text">
                                        The actual content or value for this profile. Can be text, JSON, URLs, etc.
                                    </div>
                                </div>
                            </div>

                            <!-- Profile Status -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-toggle-on me-2"></i>Profile Settings
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="isActive" th:field="*{isActive}">
                                        <label class="form-check-label" for="isActive">
                                            Active Profile
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        Inactive profiles are hidden from public view but not deleted
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="row">
                                <div class="col-12">
                                    <hr class="my-4">
                                    <div class="d-flex justify-content-between">
                                        <a th:href="@{/web/users/{id}/profiles(id=${user.id})}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                        <div>
                                            <button type="reset" class="btn btn-outline-warning me-2">
                                                <i class="fas fa-undo me-2"></i>Reset
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i th:class="${isEdit ? 'fas fa-save' : 'fas fa-plus'}" class="me-2"></i>
                                                <span th:text="${isEdit ? 'Update Profile' : 'Create Profile'}">Submit</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Type Information -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info me-2"></i>Profile Type Guidelines
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0 small">
                                    <li><strong>Personal:</strong> Bio, interests, hobbies, personal information</li>
                                    <li><strong>Contact:</strong> Phone numbers, addresses, emergency contacts</li>
                                    <li><strong>Social:</strong> Social media links, online profiles</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0 small">
                                    <li><strong>Professional:</strong> Work info, skills, company details</li>
                                    <li><strong>Preference:</strong> User settings, themes, notifications</li>
                                    <li><strong>Custom:</strong> Any other type of profile data</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Example Values -->
        <div class="row justify-content-center mt-3">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>Example Profile Values
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary">Personal Profile Examples:</h6>
                                <ul class="small mb-0">
                                    <li><strong>bio:</strong> "Software developer passionate about technology"</li>
                                    <li><strong>interests:</strong> "Programming, Reading, Hiking"</li>
                                    <li><strong>hobbies:</strong> "Photography, Cooking, Gaming"</li>
                                </ul>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="text-info">Contact Profile Examples:</h6>
                                <ul class="small mb-0">
                                    <li><strong>phone:</strong> "******-0123"</li>
                                    <li><strong>work_phone:</strong> "******-0456"</li>
                                    <li><strong>emergency_contact:</strong> "John Doe - ******-0789"</li>
                                </ul>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="text-success">Social Profile Examples:</h6>
                                <ul class="small mb-0">
                                    <li><strong>linkedin:</strong> "https://linkedin.com/in/username"</li>
                                    <li><strong>github:</strong> "https://github.com/username"</li>
                                    <li><strong>twitter:</strong> "https://twitter.com/username"</li>
                                </ul>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="text-warning">Preference Profile Examples:</h6>
                                <ul class="small mb-0">
                                    <li><strong>theme:</strong> "dark" or "light"</li>
                                    <li><strong>language:</strong> "en-US"</li>
                                    <li><strong>notifications:</strong> {"email": true, "sms": false}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <th:block layout:fragment="scripts">
        <script>
            function updateProfileTypeInfo() {
                const profileType = document.getElementById('profileType').value;
                const helpText = document.getElementById('profileTypeHelp');
                
                const descriptions = {
                    'PERSONAL': 'Personal information like bio, interests, hobbies',
                    'CONTACT': 'Contact information like phone, email, addresses',
                    'SOCIAL': 'Social media links and online profiles',
                    'PROFESSIONAL': 'Work-related information, skills, company details',
                    'PREFERENCE': 'User preferences, settings, and configurations',
                    'CUSTOM': 'Custom profile data that doesn\'t fit other categories'
                };
                
                if (profileType && descriptions[profileType]) {
                    helpText.textContent = descriptions[profileType];
                } else {
                    helpText.textContent = 'Select the category that best describes this profile';
                }
            }

            // Real-time validation feedback
            document.addEventListener('DOMContentLoaded', function() {
                const inputs = document.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.checkValidity()) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                        } else {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                        }
                    });
                });

                // Initialize profile type info
                updateProfileTypeInfo();
            });

            // Auto-resize textarea
            document.getElementById('profileValue').addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        </script>
    </th:block>
</body>
</html>
