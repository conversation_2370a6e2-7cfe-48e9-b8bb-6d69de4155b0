<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title th:text="'Profiles - ' + ${user.fullName} + ' - CircleLife'">User Profiles - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-id-card me-2"></i>User Profiles</h2>
                <p class="text-muted mb-0">
                    Managing profiles for <strong th:text="${user.fullName}">User Name</strong>
                    <span class="badge bg-primary ms-2" th:text="'@' + ${user.username}">@username</span>
                </p>
            </div>
            <div class="btn-group" role="group">
                <a th:href="@{/web/users/{id}(id=${user.id})}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to User
                </a>
                <a th:href="@{/web/users/{id}/profiles/create(id=${user.id})}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Profile
                </a>
            </div>
        </div>

        <!-- User Info Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                             style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                            <span th:text="${#strings.substring(user.fullName, 0, 1)}">U</span>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h5 class="mb-1" th:text="${user.fullName}">Full Name</h5>
                        <p class="text-muted mb-1" th:text="${user.email}"><EMAIL></p>
                        <span class="badge" 
                              th:class="${user.status == T(com.circlelife.model.User.UserStatus).ACTIVE ? 'bg-success' : 
                                         user.status == T(com.circlelife.model.User.UserStatus).INACTIVE ? 'bg-warning' : 
                                         user.status == T(com.circlelife.model.User.UserStatus).SUSPENDED ? 'bg-danger' : 'bg-secondary'}"
                              th:text="${user.status}">ACTIVE</span>
                    </div>
                    <div class="col-md-2 text-end">
                        <h4 class="text-primary mb-0" th:text="${#lists.size(profiles)}">0</h4>
                        <small class="text-muted">Total Profiles</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Type Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-0">Filter by Profile Type:</h6>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="profileTypeFilter" onchange="filterProfiles()">
                            <option value="">All Types</option>
                            <option th:each="type : ${profileTypes}" th:value="${type}" th:text="${type}">Type</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profiles Grid -->
        <div th:if="${#lists.isEmpty(profiles)}" class="text-center py-5">
            <i class="fas fa-id-card fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">No profiles found</h4>
            <p class="text-muted">This user doesn't have any profiles yet.</p>
            <a th:href="@{/web/users/{id}/profiles/create(id=${user.id})}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create First Profile
            </a>
        </div>

        <div th:if="${!#lists.isEmpty(profiles)}" class="row" id="profilesContainer">
            <div th:each="profile : ${profiles}" 
                 class="col-md-6 col-lg-4 mb-4 profile-card" 
                 th:data-type="${profile.profileType}">
                <div class="card h-100 border-start border-4" 
                     th:class="'card h-100 border-start border-4 border-' + 
                              (${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'primary' :
                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'info' :
                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'success' :
                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'warning' :
                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'secondary' : 'dark')">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="badge" 
                              th:class="'badge bg-' + 
                                       (${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'primary' :
                                        ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'info' :
                                        ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'success' :
                                        ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'warning' :
                                        ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'secondary' : 'dark')"
                              th:text="${profile.profileType}">TYPE</span>
                        <span class="badge" th:class="${profile.isActive ? 'bg-success' : 'bg-secondary'}"
                              th:text="${profile.isActive ? 'Active' : 'Inactive'}">Active</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title" th:text="${profile.profileKey}">Profile Key</h6>
                        <p class="card-text text-muted small" 
                           th:text="${#strings.length(profile.profileValue) > 150 ? 
                                     #strings.substring(profile.profileValue, 0, 150) + '...' : 
                                     profile.profileValue}">Profile value</p>
                        
                        <!-- Profile Type Icon -->
                        <div class="mb-3">
                            <i th:class="${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'fas fa-user text-primary' :
                                         ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'fas fa-address-book text-info' :
                                         ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'fas fa-share-alt text-success' :
                                         ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'fas fa-briefcase text-warning' :
                                         ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'fas fa-cog text-secondary' : 'fas fa-puzzle-piece text-dark'"
                               class="fa-2x"></i>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted" th:text="'Created: ' + ${#temporals.format(profile.createdAt, 'MMM dd, yyyy')}">Created date</small>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        data-bs-toggle="modal" 
                                        th:data-bs-target="'#profileModal' + ${profile.id}"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                        onclick="editProfile(this)"
                                        th:data-profile-id="${profile.id}"
                                        title="Edit Profile">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteProfile(this)"
                                        th:data-profile-id="${profile.id}"
                                        th:data-profile-key="${profile.profileKey}"
                                        title="Delete Profile">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Detail Modal -->
                <div class="modal fade" th:id="'profileModal' + ${profile.id}" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i th:class="${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'fas fa-user text-primary' :
                                                 ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'fas fa-address-book text-info' :
                                                 ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'fas fa-share-alt text-success' :
                                                 ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'fas fa-briefcase text-warning' :
                                                 ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'fas fa-cog text-secondary' : 'fas fa-puzzle-piece text-dark'"
                                       class="me-2"></i>
                                    <span th:text="${profile.profileKey}">Profile Key</span>
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Profile Type</label>
                                        <p>
                                            <span class="badge" 
                                                  th:class="'badge bg-' + 
                                                           (${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'primary' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'info' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'success' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'warning' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'secondary' : 'dark')"
                                                  th:text="${profile.profileType}">TYPE</span>
                                        </p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Status</label>
                                        <p>
                                            <span class="badge" th:class="${profile.isActive ? 'bg-success' : 'bg-secondary'}"
                                                  th:text="${profile.isActive ? 'Active' : 'Inactive'}">Active</span>
                                        </p>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label text-muted">Profile Key</label>
                                        <p class="fw-bold" th:text="${profile.profileKey}">Profile Key</p>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label text-muted">Profile Value</label>
                                        <div class="bg-light p-3 rounded">
                                            <pre class="mb-0" th:text="${profile.profileValue}">Profile value</pre>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Created At</label>
                                        <p th:text="${#temporals.format(profile.createdAt, 'MMMM dd, yyyy HH:mm:ss')}">Creation date</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Last Updated</label>
                                        <p th:text="${#temporals.format(profile.updatedAt, 'MMMM dd, yyyy HH:mm:ss')}">Update date</p>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-warning" 
                                        onclick="editProfile(this)"
                                        th:data-profile-id="${profile.id}">
                                    <i class="fas fa-edit me-2"></i>Edit Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Statistics -->
        <div th:if="${!#lists.isEmpty(profiles)}" class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Profile Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2 mb-3">
                                <h4 class="text-primary" th:text="${#lists.size(profiles)}">0</h4>
                                <small class="text-muted">Total Profiles</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <h4 class="text-success" th:text="${#lists.size(#lists.select(profiles, profile.isActive))}">0</h4>
                                <small class="text-muted">Active</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <h4 class="text-primary" th:text="${#lists.size(#lists.select(profiles, profile.profileType.name() == 'PERSONAL'))}">0</h4>
                                <small class="text-muted">Personal</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <h4 class="text-info" th:text="${#lists.size(#lists.select(profiles, profile.profileType.name() == 'CONTACT'))}">0</h4>
                                <small class="text-muted">Contact</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <h4 class="text-success" th:text="${#lists.size(#lists.select(profiles, profile.profileType.name() == 'SOCIAL'))}">0</h4>
                                <small class="text-muted">Social</small>
                            </div>
                            <div class="col-md-2 mb-3">
                                <h4 class="text-warning" th:text="${#lists.size(#lists.select(profiles, profile.profileType.name() == 'PROFESSIONAL'))}">0</h4>
                                <small class="text-muted">Professional</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <th:block layout:fragment="scripts">
        <script>
            function filterProfiles() {
                const filterValue = document.getElementById('profileTypeFilter').value;
                const profileCards = document.querySelectorAll('.profile-card');
                
                profileCards.forEach(card => {
                    if (filterValue === '' || card.dataset.type === filterValue) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }
            
            function editProfile(button) {
                const profileId = button.dataset.profileId;
                // Implement edit functionality
                alert('Edit profile functionality would be implemented here for profile ID: ' + profileId);
            }
            
            function deleteProfile(button) {
                const profileId = button.dataset.profileId;
                const profileKey = button.dataset.profileKey;
                
                if (confirm('Are you sure you want to delete the profile "' + profileKey + '"? This action cannot be undone.')) {
                    // Implement delete functionality
                    alert('Delete profile functionality would be implemented here for profile ID: ' + profileId);
                }
            }
        </script>
    </th:block>
</body>
</html>
