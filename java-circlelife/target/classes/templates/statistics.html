<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title>System Statistics - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="page-header text-center">
            <h1><i class="fas fa-chart-line me-3"></i>System Statistics</h1>
            <p class="lead mb-0">Comprehensive analytics and insights</p>
        </div>

        <!-- Overview Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0" th:text="${userStats.totalUsers}">0</h2>
                            <p class="mb-0">Total Users</p>
                            <small class="opacity-75">All registered users</small>
                        </div>
                        <i class="fas fa-users fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0" th:text="${userStats.activeUsers}">0</h2>
                            <p class="mb-0">Active Users</p>
                            <small class="opacity-75">Currently active</small>
                        </div>
                        <i class="fas fa-user-check fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0" th:text="${profileStats.totalProfiles}">0</h2>
                            <p class="mb-0">Total Profiles</p>
                            <small class="opacity-75">All user profiles</small>
                        </div>
                        <i class="fas fa-id-card fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card info">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0" th:text="${userStats.activeUsers > 0 ? #numbers.formatDecimal((userStats.activeUsers * 100.0 / userStats.totalUsers), 1, 1) + '%' : '0%'}">0%</h2>
                            <p class="mb-0">Active Rate</p>
                            <small class="opacity-75">User engagement</small>
                        </div>
                        <i class="fas fa-percentage fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- User Status Distribution -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>User Status Distribution</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-4">
                                <div class="text-center">
                                    <div class="position-relative d-inline-block">
                                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center text-white" 
                                             style="width: 80px; height: 80px;">
                                            <h4 class="mb-0" th:text="${userStats.activeUsers}">0</h4>
                                        </div>
                                    </div>
                                    <h6 class="mt-2 text-success">Active Users</h6>
                                    <small class="text-muted" 
                                           th:text="${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.activeUsers * 100.0 / userStats.totalUsers), 1, 1) + '% of total' : '0% of total'}">
                                        Percentage
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-4">
                                <div class="text-center">
                                    <div class="position-relative d-inline-block">
                                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center text-white" 
                                             style="width: 80px; height: 80px;">
                                            <h4 class="mb-0" th:text="${userStats.inactiveUsers}">0</h4>
                                        </div>
                                    </div>
                                    <h6 class="mt-2 text-warning">Inactive Users</h6>
                                    <small class="text-muted" 
                                           th:text="${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.inactiveUsers * 100.0 / userStats.totalUsers), 1, 1) + '% of total' : '0% of total'}">
                                        Percentage
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-4">
                                <div class="text-center">
                                    <div class="position-relative d-inline-block">
                                        <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center text-white" 
                                             style="width: 80px; height: 80px;">
                                            <h4 class="mb-0" th:text="${userStats.suspendedUsers}">0</h4>
                                        </div>
                                    </div>
                                    <h6 class="mt-2 text-danger">Suspended Users</h6>
                                    <small class="text-muted" 
                                           th:text="${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.suspendedUsers * 100.0 / userStats.totalUsers), 1, 1) + '% of total' : '0% of total'}">
                                        Percentage
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-4">
                                <div class="text-center">
                                    <div class="position-relative d-inline-block">
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center text-white" 
                                             style="width: 80px; height: 80px;">
                                            <h4 class="mb-0" th:text="${userStats.deletedUsers}">0</h4>
                                        </div>
                                    </div>
                                    <h6 class="mt-2 text-secondary">Deleted Users</h6>
                                    <small class="text-muted" 
                                           th:text="${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.deletedUsers * 100.0 / userStats.totalUsers), 1, 1) + '% of total' : '0% of total'}">
                                        Percentage
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Type Distribution -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>Profile Type Distribution</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <span>Personal</span>
                                            <strong th:text="${profileStats.personalProfiles}">0</strong>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-primary" 
                                                 th:style="'width: ' + (${profileStats.totalProfiles > 0 ? (profileStats.personalProfiles * 100.0 / profileStats.totalProfiles) : 0}) + '%'"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-info rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <span>Contact</span>
                                            <strong th:text="${profileStats.contactProfiles}">0</strong>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-info" 
                                                 th:style="'width: ' + (${profileStats.totalProfiles > 0 ? (profileStats.contactProfiles * 100.0 / profileStats.totalProfiles) : 0}) + '%'"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-success rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <span>Social</span>
                                            <strong th:text="${profileStats.socialProfiles}">0</strong>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-success" 
                                                 th:style="'width: ' + (${profileStats.totalProfiles > 0 ? (profileStats.socialProfiles * 100.0 / profileStats.totalProfiles) : 0}) + '%'"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-warning rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <span>Professional</span>
                                            <strong th:text="${profileStats.professionalProfiles}">0</strong>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-warning" 
                                                 th:style="'width: ' + (${profileStats.totalProfiles > 0 ? (profileStats.professionalProfiles * 100.0 / profileStats.totalProfiles) : 0}) + '%'"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <span>Preference</span>
                                            <strong th:text="${profileStats.preferenceProfiles}">0</strong>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-secondary" 
                                                 th:style="'width: ' + (${profileStats.totalProfiles > 0 ? (profileStats.preferenceProfiles * 100.0 / profileStats.totalProfiles) : 0}) + '%'"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-dark rounded-circle me-3" style="width: 12px; height: 12px;"></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between">
                                            <span>Custom</span>
                                            <strong th:text="${profileStats.customProfiles}">0</strong>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-dark" 
                                                 th:style="'width: ' + (${profileStats.totalProfiles > 0 ? (profileStats.customProfiles * 100.0 / profileStats.totalProfiles) : 0}) + '%'"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Statistics -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Detailed Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- User Statistics Table -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">User Statistics</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <td><i class="fas fa-users text-primary me-2"></i>Total Users</td>
                                                <td class="text-end"><strong th:text="${userStats.totalUsers}">0</strong></td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-user-check text-success me-2"></i>Active Users</td>
                                                <td class="text-end">
                                                    <strong th:text="${userStats.activeUsers}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.activeUsers * 100.0 / userStats.totalUsers), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-user-times text-warning me-2"></i>Inactive Users</td>
                                                <td class="text-end">
                                                    <strong th:text="${userStats.inactiveUsers}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.inactiveUsers * 100.0 / userStats.totalUsers), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-user-slash text-danger me-2"></i>Suspended Users</td>
                                                <td class="text-end">
                                                    <strong th:text="${userStats.suspendedUsers}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.suspendedUsers * 100.0 / userStats.totalUsers), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-user-minus text-secondary me-2"></i>Deleted Users</td>
                                                <td class="text-end">
                                                    <strong th:text="${userStats.deletedUsers}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${userStats.totalUsers > 0 ? #numbers.formatDecimal((userStats.deletedUsers * 100.0 / userStats.totalUsers), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Profile Statistics Table -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Profile Statistics</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <td><i class="fas fa-id-card text-primary me-2"></i>Total Profiles</td>
                                                <td class="text-end"><strong th:text="${profileStats.totalProfiles}">0</strong></td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-user text-primary me-2"></i>Personal Profiles</td>
                                                <td class="text-end">
                                                    <strong th:text="${profileStats.personalProfiles}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${profileStats.totalProfiles > 0 ? #numbers.formatDecimal((profileStats.personalProfiles * 100.0 / profileStats.totalProfiles), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-address-book text-info me-2"></i>Contact Profiles</td>
                                                <td class="text-end">
                                                    <strong th:text="${profileStats.contactProfiles}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${profileStats.totalProfiles > 0 ? #numbers.formatDecimal((profileStats.contactProfiles * 100.0 / profileStats.totalProfiles), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-share-alt text-success me-2"></i>Social Profiles</td>
                                                <td class="text-end">
                                                    <strong th:text="${profileStats.socialProfiles}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${profileStats.totalProfiles > 0 ? #numbers.formatDecimal((profileStats.socialProfiles * 100.0 / profileStats.totalProfiles), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-briefcase text-warning me-2"></i>Professional Profiles</td>
                                                <td class="text-end">
                                                    <strong th:text="${profileStats.professionalProfiles}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${profileStats.totalProfiles > 0 ? #numbers.formatDecimal((profileStats.professionalProfiles * 100.0 / profileStats.totalProfiles), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-cog text-secondary me-2"></i>Preference Profiles</td>
                                                <td class="text-end">
                                                    <strong th:text="${profileStats.preferenceProfiles}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${profileStats.totalProfiles > 0 ? #numbers.formatDecimal((profileStats.preferenceProfiles * 100.0 / profileStats.totalProfiles), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><i class="fas fa-puzzle-piece text-dark me-2"></i>Custom Profiles</td>
                                                <td class="text-end">
                                                    <strong th:text="${profileStats.customProfiles}">0</strong>
                                                    <small class="text-muted ms-2" 
                                                           th:text="'(' + (${profileStats.totalProfiles > 0 ? #numbers.formatDecimal((profileStats.customProfiles * 100.0 / profileStats.totalProfiles), 1, 1) + '%' : '0%'}) + ')'">
                                                        (0%)
                                                    </small>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/web/users}" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-users me-2"></i>View All Users
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/web/users/create}" class="btn btn-outline-success w-100">
                                    <i class="fas fa-user-plus me-2"></i>Add New User
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/api/v1/users/statistics}" target="_blank" class="btn btn-outline-info w-100">
                                    <i class="fas fa-download me-2"></i>Export Data
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button onclick="window.location.reload()" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
