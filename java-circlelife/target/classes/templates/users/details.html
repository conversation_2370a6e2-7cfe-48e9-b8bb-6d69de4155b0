<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title th:text="'User Details - ' + ${user.fullName} + ' - CircleLife'">User Details - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-user me-2"></i>User Details</h2>
                <p class="text-muted mb-0">Complete information about the user</p>
            </div>
            <div class="btn-group" role="group">
                <a th:href="@{/web/users}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
                <a th:href="@{/web/users/{id}/edit(id=${user.id})}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit User
                </a>
                <a th:href="@{/web/users/{id}/profiles(id=${user.id})}" class="btn btn-info">
                    <i class="fas fa-id-card me-2"></i>View Profiles
                </a>
            </div>
        </div>

        <div class="row">
            <!-- User Information Card -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>User Information</h5>
                        <span class="badge" 
                              th:class="${user.status == T(com.circlelife.model.User.UserStatus).ACTIVE ? 'bg-success' : 
                                         user.status == T(com.circlelife.model.User.UserStatus).INACTIVE ? 'bg-warning' : 
                                         user.status == T(com.circlelife.model.User.UserStatus).SUSPENDED ? 'bg-danger' : 'bg-secondary'}"
                              th:text="${user.status}">ACTIVE</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center mb-4">
                                <!-- User Avatar -->
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                     style="width: 100px; height: 100px; font-size: 2rem; font-weight: bold;">
                                    <span th:text="${#strings.substring(user.fullName, 0, 1)}">U</span>
                                </div>
                                <h5 th:text="${user.fullName}">Full Name</h5>
                                <p class="text-muted mb-0" th:text="'@' + ${user.username}">@username</p>
                            </div>
                            
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">User ID</label>
                                        <p class="fw-bold" th:text="${user.id}">1</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Username</label>
                                        <p class="fw-bold" th:text="${user.username}">username</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Email</label>
                                        <p class="fw-bold">
                                            <a th:href="'mailto:' + ${user.email}" th:text="${user.email}"><EMAIL></a>
                                        </p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Age</label>
                                        <p class="fw-bold" th:text="${user.age != null ? user.age + ' years old' : 'Not specified'}">25 years old</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Status</label>
                                        <p>
                                            <span class="badge" 
                                                  th:class="${user.status == T(com.circlelife.model.User.UserStatus).ACTIVE ? 'bg-success' : 
                                                             user.status == T(com.circlelife.model.User.UserStatus).INACTIVE ? 'bg-warning' : 
                                                             user.status == T(com.circlelife.model.User.UserStatus).SUSPENDED ? 'bg-danger' : 'bg-secondary'}"
                                                  th:text="${user.status}">ACTIVE</span>
                                        </p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Member Since</label>
                                        <p class="fw-bold" th:text="${#temporals.format(user.createdAt, 'MMMM dd, yyyy')}">January 01, 2024</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information Card -->
                <div class="card mb-4" th:if="${user.address != null && user.address.isComplete()}">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Address Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label text-muted">Full Address</label>
                                <p class="fw-bold" th:text="${user.address.fullAddress}">Complete address</p>
                            </div>
                            <div class="col-md-6 mb-3" th:if="${user.address.streetAddress != null && !#strings.isEmpty(user.address.streetAddress)}">
                                <label class="form-label text-muted">Street Address</label>
                                <p th:text="${user.address.streetAddress}">Street address</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">City</label>
                                <p th:text="${user.address.city}">City</p>
                            </div>
                            <div class="col-md-4 mb-3" th:if="${user.address.state != null && !#strings.isEmpty(user.address.state)}">
                                <label class="form-label text-muted">State/Province</label>
                                <p th:text="${user.address.state}">State</p>
                            </div>
                            <div class="col-md-4 mb-3" th:if="${user.address.postalCode != null && !#strings.isEmpty(user.address.postalCode)}">
                                <label class="form-label text-muted">Postal Code</label>
                                <p th:text="${user.address.postalCode}">Postal code</p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label text-muted">Country</label>
                                <p th:text="${user.address.country}">Country</p>
                            </div>
                            <div class="col-md-12" th:if="${user.address.additionalInfo != null && !#strings.isEmpty(user.address.additionalInfo)}">
                                <label class="form-label text-muted">Additional Information</label>
                                <p th:text="${user.address.additionalInfo}">Additional info</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Profiles Card -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-id-card me-2"></i>User Profiles 
                            <span class="badge bg-primary ms-2" th:text="${#lists.size(profiles)}">0</span>
                        </h5>
                        <a th:href="@{/web/users/{id}/profiles/create(id=${user.id})}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i>Add Profile
                        </a>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(profiles)}" class="text-center py-4">
                            <i class="fas fa-id-card fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No profiles found</h6>
                            <p class="text-muted mb-3">This user doesn't have any profiles yet.</p>
                            <a th:href="@{/web/users/{id}/profiles/create(id=${user.id})}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create First Profile
                            </a>
                        </div>
                        
                        <div th:if="${!#lists.isEmpty(profiles)}" class="row">
                            <div th:each="profile : ${profiles}" class="col-md-6 mb-3">
                                <div class="card border-start border-4" 
                                     th:class="'card border-start border-4 border-' + 
                                              (${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'primary' :
                                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'info' :
                                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'success' :
                                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'warning' :
                                               ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'secondary' : 'dark')">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge" 
                                                  th:class="'badge bg-' + 
                                                           (${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PERSONAL} ? 'primary' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).CONTACT} ? 'info' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).SOCIAL} ? 'success' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PROFESSIONAL} ? 'warning' :
                                                            ${profile.profileType == T(com.circlelife.model.UserProfile.ProfileType).PREFERENCE} ? 'secondary' : 'dark')"
                                                  th:text="${profile.profileType}">TYPE</span>
                                            <span class="badge" th:class="${profile.isActive ? 'bg-success' : 'bg-secondary'}"
                                                  th:text="${profile.isActive ? 'Active' : 'Inactive'}">Active</span>
                                        </div>
                                        <h6 class="card-title mb-2" th:text="${profile.profileKey}">Profile Key</h6>
                                        <p class="card-text small text-muted mb-2" 
                                           th:text="${#strings.length(profile.profileValue) > 100 ? 
                                                     #strings.substring(profile.profileValue, 0, 100) + '...' : 
                                                     profile.profileValue}">Profile value</p>
                                        <small class="text-muted" th:text="'Created: ' + ${#temporals.format(profile.createdAt, 'MMM dd, yyyy')}">Created date</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div th:if="${!#lists.isEmpty(profiles)}" class="text-center mt-3">
                            <a th:href="@{/web/users/{id}/profiles(id=${user.id})}" class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>View All Profiles
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a th:href="@{/web/users/{id}/edit(id=${user.id})}" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit User
                            </a>
                            <a th:href="@{/web/users/{id}/profiles(id=${user.id})}" class="btn btn-info">
                                <i class="fas fa-id-card me-2"></i>Manage Profiles
                            </a>
                            <a th:href="@{/web/users/{id}/profiles/create(id=${user.id})}" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Add Profile
                            </a>
                            <hr>
                            <form th:action="@{/web/users/{id}/delete(id=${user.id})}" method="post" 
                                  onsubmit="return confirmDelete('Are you sure you want to delete this user? This action cannot be undone.')">
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash me-2"></i>Delete User
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- User Statistics -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>User Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border-end">
                                    <h4 class="text-primary" th:text="${#lists.size(profiles)}">0</h4>
                                    <small class="text-muted">Total Profiles</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <h4 class="text-success" th:text="${#lists.size(#lists.select(profiles, profile.isActive))}">0</h4>
                                <small class="text-muted">Active Profiles</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-12">
                                <small class="text-muted">Member for</small>
                                <p class="fw-bold" th:text="${#temporals.formatDiff(user.createdAt, #temporals.createNow(), 'en')}">Duration</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info me-2"></i>System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-2">
                                <small class="text-muted">Created At</small>
                                <p class="mb-1" th:text="${#temporals.format(user.createdAt, 'MMMM dd, yyyy HH:mm:ss')}">Creation date</p>
                            </div>
                            <div class="col-12 mb-2">
                                <small class="text-muted">Last Updated</small>
                                <p class="mb-1" th:text="${#temporals.format(user.updatedAt, 'MMMM dd, yyyy HH:mm:ss')}">Update date</p>
                            </div>
                            <div class="col-12">
                                <small class="text-muted">Version</small>
                                <p class="mb-0" th:text="${user.version}">0</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
