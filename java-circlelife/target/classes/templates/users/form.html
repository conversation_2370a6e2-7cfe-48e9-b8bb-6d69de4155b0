<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title th:text="${isEdit ? 'Edit User' : 'Create User'} + ' - CircleLife'">User Form - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>
                    <i th:class="${isEdit ? 'fas fa-edit' : 'fas fa-plus'}" class="me-2"></i>
                    <span th:text="${isEdit ? 'Edit User' : 'Create New User'}">User Form</span>
                </h2>
                <p class="text-muted mb-0" th:text="${isEdit ? 'Update user information' : 'Add a new user to the system'}">Form description</p>
            </div>
            <a th:href="@{/web/users}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        </div>

        <!-- User Form -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="${isEdit ? '/web/users/' + user.id + '/edit' : '/web/users/create'}" 
                              th:object="${user}" method="post" novalidate>
                            
                            <!-- Basic Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-info-circle me-2"></i>Basic Information
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" th:field="*{username}" 
                                           th:class="${#fields.hasErrors('username')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter username" required>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('username')}" th:errors="*{username}">
                                        Username error
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" th:field="*{email}" 
                                           th:class="${#fields.hasErrors('email')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter email address" required>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('email')}" th:errors="*{email}">
                                        Email error
                                    </div>
                                </div>
                                
                                <div class="col-md-12 mb-3">
                                    <label for="fullName" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="fullName" th:field="*{fullName}" 
                                           th:class="${#fields.hasErrors('fullName')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter full name" required>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('fullName')}" th:errors="*{fullName}">
                                        Full name error
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        Password <span class="text-danger" th:if="${!isEdit}">*</span>
                                        <small class="text-muted" th:if="${isEdit}">(leave blank to keep current)</small>
                                    </label>
                                    <input type="password" class="form-control" id="password" th:field="*{password}" 
                                           th:class="${#fields.hasErrors('password')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter password" th:required="${!isEdit}">
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('password')}" th:errors="*{password}">
                                        Password error
                                    </div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="age" class="form-label">Age</label>
                                    <input type="number" class="form-control" id="age" th:field="*{age}" 
                                           th:class="${#fields.hasErrors('age')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Age" min="18" max="120">
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('age')}" th:errors="*{age}">
                                        Age error
                                    </div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" th:field="*{status}" 
                                            th:class="${#fields.hasErrors('status')} ? 'form-select is-invalid' : 'form-select'">
                                        <option th:each="status : ${userStatuses}" 
                                                th:value="${status}" 
                                                th:text="${status}">Status</option>
                                    </select>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('status')}" th:errors="*{status}">
                                        Status error
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-map-marker-alt me-2"></i>Address Information
                                    </h6>
                                </div>
                                
                                <div class="col-md-12 mb-3">
                                    <label for="address.streetAddress" class="form-label">Street Address</label>
                                    <input type="text" class="form-control" id="address.streetAddress" th:field="*{address.streetAddress}" 
                                           placeholder="Enter street address">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address.city" class="form-label">City <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="address.city" th:field="*{address.city}" 
                                           th:class="${#fields.hasErrors('address.city')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter city" required>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('address.city')}" th:errors="*{address.city}">
                                        City error
                                    </div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="address.state" class="form-label">State/Province</label>
                                    <input type="text" class="form-control" id="address.state" th:field="*{address.state}" 
                                           placeholder="Enter state">
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="address.postalCode" class="form-label">Postal Code</label>
                                    <input type="text" class="form-control" id="address.postalCode" th:field="*{address.postalCode}" 
                                           th:class="${#fields.hasErrors('address.postalCode')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter postal code">
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('address.postalCode')}" th:errors="*{address.postalCode}">
                                        Postal code error
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address.country" class="form-label">Country <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="address.country" th:field="*{address.country}" 
                                           th:class="${#fields.hasErrors('address.country')} ? 'form-control is-invalid' : 'form-control'"
                                           placeholder="Enter country" required>
                                    <div class="invalid-feedback" th:if="${#fields.hasErrors('address.country')}" th:errors="*{address.country}">
                                        Country error
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address.additionalInfo" class="form-label">Additional Info</label>
                                    <input type="text" class="form-control" id="address.additionalInfo" th:field="*{address.additionalInfo}" 
                                           placeholder="Apartment, suite, etc.">
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="row">
                                <div class="col-12">
                                    <hr class="my-4">
                                    <div class="d-flex justify-content-between">
                                        <a th:href="@{/web/users}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Cancel
                                        </a>
                                        <div>
                                            <button type="reset" class="btn btn-outline-warning me-2">
                                                <i class="fas fa-undo me-2"></i>Reset
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i th:class="${isEdit ? 'fas fa-save' : 'fas fa-plus'}" class="me-2"></i>
                                                <span th:text="${isEdit ? 'Update User' : 'Create User'}">Submit</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Text -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info me-2"></i>Form Guidelines
                        </h6>
                        <ul class="mb-0 small">
                            <li>Fields marked with <span class="text-danger">*</span> are required</li>
                            <li>Username must be unique and between 3-50 characters</li>
                            <li>Email must be a valid email address and unique</li>
                            <li>Password must be at least 6 characters long</li>
                            <li>Age must be between 18 and 120 years</li>
                            <li>Postal code should follow the format: 12345 or 12345-6789</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript for form validation -->
    <th:block layout:fragment="scripts">
        <script>
            // Client-side validation
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();

            // Real-time validation feedback
            document.addEventListener('DOMContentLoaded', function() {
                const inputs = document.querySelectorAll('input, select');
                inputs.forEach(input => {
                    input.addEventListener('blur', function() {
                        if (this.checkValidity()) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                        } else {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                        }
                    });
                });
            });
        </script>
    </th:block>
</body>
</html>
