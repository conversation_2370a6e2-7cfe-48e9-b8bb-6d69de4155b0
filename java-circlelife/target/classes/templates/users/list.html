<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" 
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">

<head>
    <title>Users Management - CircleLife</title>
</head>

<body>
    <div layout:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-users me-2"></i>Users Management</h2>
                <p class="text-muted mb-0">Manage all users in the system</p>
            </div>
            <a th:href="@{/web/users/create}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add New User
            </a>
        </div>

        <!-- Filters and Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form th:action="@{/web/users}" method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               th:value="${search}" placeholder="Search by name or email...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option th:each="status : ${userStatuses}" 
                                    th:value="${status}" 
                                    th:text="${status}"
                                    th:selected="${selectedStatus == status}"></option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="pageSize" class="form-label">Page Size</label>
                        <select class="form-select" id="pageSize" name="size">
                            <option th:value="5" th:selected="${pageSize == 5}">5</option>
                            <option th:value="10" th:selected="${pageSize == 10}">10</option>
                            <option th:value="25" th:selected="${pageSize == 25}">25</option>
                            <option th:value="50" th:selected="${pageSize == 50}">50</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sortBy" class="form-label">Sort By</label>
                        <select class="form-select" id="sortBy" name="sortBy">
                            <option th:value="id" th:selected="${sortBy == 'id'}">ID</option>
                            <option th:value="username" th:selected="${sortBy == 'username'}">Username</option>
                            <option th:value="fullName" th:selected="${sortBy == 'fullName'}">Full Name</option>
                            <option th:value="email" th:selected="${sortBy == 'email'}">Email</option>
                            <option th:value="createdAt" th:selected="${sortBy == 'createdAt'}">Created Date</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sortDir" class="form-label">Direction</label>
                        <select class="form-select" id="sortDir" name="sortDir">
                            <option th:value="asc" th:selected="${sortDir == 'asc'}">Ascending</option>
                            <option th:value="desc" th:selected="${sortDir == 'desc'}">Descending</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <a th:href="@{/web/users}" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Users List 
                    <span class="badge bg-primary ms-2" th:text="${users.totalElements}">0</span>
                </h5>
                <small class="text-muted">
                    Showing <span th:text="${users.numberOfElements}">0</span> of 
                    <span th:text="${users.totalElements}">0</span> users
                </small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <a th:href="@{/web/users(page=${currentPage}, size=${pageSize}, sortBy='id', sortDir=${sortBy == 'id' ? reverseSortDir : 'asc'}, status=${selectedStatus}, search=${search})}"
                                       class="text-decoration-none text-dark">
                                        ID <i class="fas fa-sort" th:class="${sortBy == 'id' ? (sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down') : 'fas fa-sort'}"></i>
                                    </a>
                                </th>
                                <th>Avatar</th>
                                <th>
                                    <a th:href="@{/web/users(page=${currentPage}, size=${pageSize}, sortBy='username', sortDir=${sortBy == 'username' ? reverseSortDir : 'asc'}, status=${selectedStatus}, search=${search})}"
                                       class="text-decoration-none text-dark">
                                        Username <i class="fas fa-sort" th:class="${sortBy == 'username' ? (sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down') : 'fas fa-sort'}"></i>
                                    </a>
                                </th>
                                <th>
                                    <a th:href="@{/web/users(page=${currentPage}, size=${pageSize}, sortBy='fullName', sortDir=${sortBy == 'fullName' ? reverseSortDir : 'asc'}, status=${selectedStatus}, search=${search})}"
                                       class="text-decoration-none text-dark">
                                        Full Name <i class="fas fa-sort" th:class="${sortBy == 'fullName' ? (sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down') : 'fas fa-sort'}"></i>
                                    </a>
                                </th>
                                <th>
                                    <a th:href="@{/web/users(page=${currentPage}, size=${pageSize}, sortBy='email', sortDir=${sortBy == 'email' ? reverseSortDir : 'asc'}, status=${selectedStatus}, search=${search})}"
                                       class="text-decoration-none text-dark">
                                        Email <i class="fas fa-sort" th:class="${sortBy == 'email' ? (sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down') : 'fas fa-sort'}"></i>
                                    </a>
                                </th>
                                <th>Age</th>
                                <th>Status</th>
                                <th>
                                    <a th:href="@{/web/users(page=${currentPage}, size=${pageSize}, sortBy='createdAt', sortDir=${sortBy == 'createdAt' ? reverseSortDir : 'asc'}, status=${selectedStatus}, search=${search})}"
                                       class="text-decoration-none text-dark">
                                        Created <i class="fas fa-sort" th:class="${sortBy == 'createdAt' ? (sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down') : 'fas fa-sort'}"></i>
                                    </a>
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="user : ${users.content}">
                                <td th:text="${user.id}">1</td>
                                <td>
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px; font-weight: bold;">
                                        <span th:text="${#strings.substring(user.fullName, 0, 1)}">U</span>
                                    </div>
                                </td>
                                <td>
                                    <strong th:text="${user.username}">username</strong>
                                </td>
                                <td th:text="${user.fullName}">Full Name</td>
                                <td>
                                    <a th:href="'mailto:' + ${user.email}" th:text="${user.email}"><EMAIL></a>
                                </td>
                                <td th:text="${user.age}">25</td>
                                <td>
                                    <span class="badge" 
                                          th:class="${user.status == T(com.circlelife.model.User.UserStatus).ACTIVE ? 'bg-success' : 
                                                     user.status == T(com.circlelife.model.User.UserStatus).INACTIVE ? 'bg-warning' : 
                                                     user.status == T(com.circlelife.model.User.UserStatus).SUSPENDED ? 'bg-danger' : 'bg-secondary'}"
                                          th:text="${user.status}">ACTIVE</span>
                                </td>
                                <td>
                                    <small class="text-muted" th:text="${#temporals.format(user.createdAt, 'MMM dd, yyyy')}">Jan 01, 2024</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a th:href="@{/web/users/{id}(id=${user.id})}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a th:href="@{/web/users/{id}/edit(id=${user.id})}" 
                                           class="btn btn-sm btn-outline-warning" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a th:href="@{/web/users/{id}/profiles(id=${user.id})}" 
                                           class="btn btn-sm btn-outline-info" title="View Profiles">
                                            <i class="fas fa-id-card"></i>
                                        </a>
                                        <form th:action="@{/web/users/{id}/delete(id=${user.id})}" method="post" 
                                              style="display: inline;" onsubmit="return confirmDelete('Are you sure you want to delete this user?')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete User">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <nav th:if="${users.totalPages > 1}" class="mt-4">
            <ul class="pagination justify-content-center">
                <!-- First Page -->
                <li class="page-item" th:class="${users.first} ? 'disabled'">
                    <a class="page-link" 
                       th:href="@{/web/users(page=0, size=${pageSize}, sortBy=${sortBy}, sortDir=${sortDir}, status=${selectedStatus}, search=${search})}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                
                <!-- Previous Page -->
                <li class="page-item" th:class="${users.first} ? 'disabled'">
                    <a class="page-link" 
                       th:href="@{/web/users(page=${currentPage - 1}, size=${pageSize}, sortBy=${sortBy}, sortDir=${sortDir}, status=${selectedStatus}, search=${search})}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
                
                <!-- Page Numbers -->
                <li th:each="pageNum : ${#numbers.sequence(0, users.totalPages - 1)}" 
                    th:if="${pageNum >= currentPage - 2 && pageNum <= currentPage + 2}"
                    class="page-item" th:class="${pageNum == currentPage} ? 'active'">
                    <a class="page-link" 
                       th:href="@{/web/users(page=${pageNum}, size=${pageSize}, sortBy=${sortBy}, sortDir=${sortDir}, status=${selectedStatus}, search=${search})}"
                       th:text="${pageNum + 1}">1</a>
                </li>
                
                <!-- Next Page -->
                <li class="page-item" th:class="${users.last} ? 'disabled'">
                    <a class="page-link" 
                       th:href="@{/web/users(page=${currentPage + 1}, size=${pageSize}, sortBy=${sortBy}, sortDir=${sortDir}, status=${selectedStatus}, search=${search})}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                
                <!-- Last Page -->
                <li class="page-item" th:class="${users.last} ? 'disabled'">
                    <a class="page-link" 
                       th:href="@{/web/users(page=${users.totalPages - 1}, size=${pageSize}, sortBy=${sortBy}, sortDir=${sortDir}, status=${selectedStatus}, search=${search})}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Empty State -->
        <div th:if="${users.totalElements == 0}" class="text-center py-5">
            <i class="fas fa-users fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">No users found</h4>
            <p class="text-muted">Try adjusting your search criteria or add a new user.</p>
            <a th:href="@{/web/users/create}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First User
            </a>
        </div>
    </div>
</body>
</html>
