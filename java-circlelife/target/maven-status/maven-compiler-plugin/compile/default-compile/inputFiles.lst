/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/repository/UserProfileRepository.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/model/User.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/controller/WebController.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/controller/UserProfileController.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/model/UserProfile.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/config/AppConfig.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/service/UserProfileService.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/repository/UserRepository.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/model/Address.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/JavaCircleLifeApplication.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/controller/UserController.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/service/UserService.java
/Users/<USER>/Documents/demo/java-circlelife/src/main/java/com/circlelife/component/ScheduledTasks.java
