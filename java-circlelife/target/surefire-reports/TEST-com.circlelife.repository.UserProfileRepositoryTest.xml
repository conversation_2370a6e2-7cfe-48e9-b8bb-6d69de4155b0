<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.circlelife.repository.UserProfileRepositoryTest" time="236.384" tests="16" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Documents/demo/java-circlelife/target/test-classes:/Users/<USER>/Documents/demo/java-circlelife/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.2.0/spring-boot-starter-thymeleaf-3.2.0.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.2.RELEASE/thymeleaf-spring6-3.1.2.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.0/spring-boot-starter-security-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.2.0/spring-boot-starter-cache-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2.0/spring-boot-starter-actuator-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.0/spring-boot-actuator-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.2.0/spring-boot-actuator-3.2.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.0/micrometer-jakarta9-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.0/micrometer-core-1.12.0.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.2.224/h2-2.2.224.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.2.0/spring-boot-starter-data-redis-3.2.0.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.3.0.RELEASE/lettuce-core-6.3.0.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.101.Final/netty-common-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.101.Final/netty-handler-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.101.Final/netty-resolver-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.101.Final/netty-buffer-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.101.Final/netty-transport-native-unix-common-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.101.Final/netty-codec-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.101.Final/netty-transport-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.0/reactor-core-3.6.0.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.0/spring-data-redis-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.2.0/spring-data-keyvalue-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.1/spring-oxm-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.1.1/spring-context-support-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar:/Users/<USER>/.m2/repository/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/3.3.0/thymeleaf-layout-dialect-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/groovy/groovy/4.0.15/groovy-4.0.15.jar:/Users/<USER>/.m2/repository/nz/net/ultraq/groovy/groovy-extensions/2.1.0/groovy-extensions-2.1.0.jar:/Users/<USER>/.m2/repository/nz/net/ultraq/thymeleaf/thymeleaf-expression-processor/3.2.0/thymeleaf-expression-processor-3.2.0.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.2.RELEASE/thymeleaf-3.1.2.RELEASE.jar:/Users/<USER>/.m2/repository/ognl/ognl/3.3.4/ognl-3.3.4.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.29.0-GA/javassist-3.29.0-GA.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/Users/<USER>/.m2/repository/org/webjars/bootstrap/5.3.0/bootstrap-5.3.0.jar:/Users/<USER>/.m2/repository/org/webjars/jquery/3.7.0/jquery-3.7.0.jar:/Users/<USER>/.m2/repository/org/webjars/font-awesome/6.4.0/font-awesome-6.4.0.jar:/Users/<USER>/.m2/repository/org/webjars/webjars-locator-core/0.55/webjars-locator-core-0.55.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.149/classgraph-4.8.149.jar:"/>
    <property name="java.vm.vendor" value="Azul Systems, Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="http://www.azul.com/"/>
    <property name="user.timezone" value="Asia/Ho_Chi_Minh"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="user.country" value="VN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Documents/demo/java-circlelife/target/surefire/surefirebooter-20250712144511785_3.jar /Users/<USER>/Documents/demo/java-circlelife/target/surefire 2025-07-12T14-45-11_556-jvmRun1 surefire-20250712144511785_1tmp surefire_0-20250712144511785_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Documents/demo/java-circlelife/target/test-classes:/Users/<USER>/Documents/demo/java-circlelife/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.2.0/spring-boot-starter-thymeleaf-3.2.0.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.2.RELEASE/thymeleaf-spring6-3.1.2.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.0/spring-boot-starter-security-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.2.0/spring-boot-starter-cache-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2.0/spring-boot-starter-actuator-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.0/spring-boot-actuator-autoconfigure-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.2.0/spring-boot-actuator-3.2.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.0/micrometer-jakarta9-1.12.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.0/micrometer-core-1.12.0.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.12/HdrHistogram-2.1.12.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.2.224/h2-2.2.224.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.2.0/spring-boot-starter-data-redis-3.2.0.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.3.0.RELEASE/lettuce-core-6.3.0.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.101.Final/netty-common-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.101.Final/netty-handler-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.101.Final/netty-resolver-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.101.Final/netty-buffer-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.101.Final/netty-transport-native-unix-common-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.101.Final/netty-codec-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.101.Final/netty-transport-4.1.101.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.0/reactor-core-3.6.0.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.0/spring-data-redis-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.2.0/spring-data-keyvalue-3.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.1/spring-oxm-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.1.1/spring-context-support-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar:/Users/<USER>/.m2/repository/nz/net/ultraq/thymeleaf/thymeleaf-layout-dialect/3.3.0/thymeleaf-layout-dialect-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/groovy/groovy/4.0.15/groovy-4.0.15.jar:/Users/<USER>/.m2/repository/nz/net/ultraq/groovy/groovy-extensions/2.1.0/groovy-extensions-2.1.0.jar:/Users/<USER>/.m2/repository/nz/net/ultraq/thymeleaf/thymeleaf-expression-processor/3.2.0/thymeleaf-expression-processor-3.2.0.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.2.RELEASE/thymeleaf-3.1.2.RELEASE.jar:/Users/<USER>/.m2/repository/ognl/ognl/3.3.4/ognl-3.3.4.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.29.0-GA/javassist-3.29.0-GA.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/Users/<USER>/.m2/repository/org/webjars/bootstrap/5.3.0/bootstrap-5.3.0.jar:/Users/<USER>/.m2/repository/org/webjars/jquery/3.7.0/jquery-3.7.0.jar:/Users/<USER>/.m2/repository/org/webjars/font-awesome/6.4.0/font-awesome-6.4.0.jar:/Users/<USER>/.m2/repository/org/webjars/webjars-locator-core/0.55/webjars-locator-core-0.55.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.149/classgraph-4.8.149.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2024-04-16"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Documents/demo/java-circlelife"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Documents/demo/java-circlelife/target/surefire/surefirebooter-20250712144511785_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.11+9-LTS"/>
    <property name="user.name" value="feliz"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="14.5"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Zulu17.50+19-CA"/>
    <property name="LOG_FILE" value="logs/circlelife.log"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="http://www.azul.com/support/"/>
    <property name="java.io.tmpdir" value="/var/folders/s4/sf75tln92h794z_8xqv7r73c0000gn/T/"/>
    <property name="java.version" value="17.0.11"/>
    <property name="user.dir" value="/Users/<USER>/Documents/demo/java-circlelife"/>
    <property name="os.arch" value="x86_64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="37241"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Azul Systems, Inc."/>
    <property name="java.vm.version" value="17.0.11+9-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[java-circlelife] "/>
  </properties>
  <testcase name="existsByUserAndProfileKey_ShouldReturnFalse_WhenNotExists" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.862">
    <system-out><![CDATA[14:45:13.191 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.circlelife.repository.UserProfileRepositoryTest]: UserProfileRepositoryTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
14:45:13.409 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.circlelife.JavaCircleLifeApplication for test class com.circlelife.repository.UserProfileRepositoryTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:14 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:14 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:14 - Finished Spring Data repository scanning in 162 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:14 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:14 - Starting embedded database: url='jdbc:h2:mem:6efb29c4-4ef7-48f1-b3bc-9fa354749644;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:15 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:15 - HHH000412: Hibernate ORM core version 6.3.1.Final
2025-07-12 14:45:15 - HHH000026: Second-level cache disabled
2025-07-12 14:45:15 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:15 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:16 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:16 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:16 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:16 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:16 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:16 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:16 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:16 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:16 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:16 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:16 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:16 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:16 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:16 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:16 - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-12 14:45:18 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:18 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:18 - 
    select
        up1_0.profile_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=? 
    fetch
        first ? rows only
Hibernate: 
    select
        up1_0.profile_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=? 
    fetch
        first ? rows only
2025-07-12 14:45:18 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:18 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:18 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUserAndProfileKey_ShouldReturnProfile_WhenExists" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.071">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:18 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:18 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:18 - Finished Spring Data repository scanning in 10 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:18 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:18 - Starting embedded database: url='jdbc:h2:mem:702c5346-aaf1-4714-8d63-3847757db943;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:18 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:18 - HHH000026: Second-level cache disabled
2025-07-12 14:45:18 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:18 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:19 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:19 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:19 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:19 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:19 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:19 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:19 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:19 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:19 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:19 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:19 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:19 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:19 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:19 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:19 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:19 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:19 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=?
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=?
2025-07-12 14:45:19 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:19 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:19 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUserAndIsActiveTrue_ShouldReturnActiveProfiles" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.071">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:19 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:19 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:19 - Finished Spring Data repository scanning in 9 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:19 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:19 - Starting embedded database: url='jdbc:h2:mem:f3c53e2d-68b5-4f96-b6c2-96bef68f9770;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:19 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:19 - HHH000026: Second-level cache disabled
2025-07-12 14:45:19 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:19 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:19 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:19 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:19 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:19 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:19 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:19 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:19 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:19 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:19 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:19 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:19 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:19 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:19 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:19 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:20 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:20 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:20 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:20 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.is_active
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.is_active
2025-07-12 14:45:20 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:20 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:20 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUser_WithPageable_ShouldReturnPagedResults" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.103">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:20 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:20 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:20 - Finished Spring Data repository scanning in 8 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:20 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:20 - Starting embedded database: url='jdbc:h2:mem:6e94d21f-369a-4711-aeb7-bdf17bbe1bf5;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:20 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:20 - HHH000026: Second-level cache disabled
2025-07-12 14:45:20 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:20 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:20 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:20 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:20 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:20 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:20 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:20 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:20 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:20 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:20 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:20 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:20 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:20 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:20 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:20 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:21 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
    offset
        ? rows 
    fetch
        first ? rows only
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-12 14:45:21 - 
    select
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
Hibernate: 
    select
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
2025-07-12 14:45:21 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:21 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:21 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="countByProfileType_ShouldReturnCorrectCount" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.068">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:21 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:21 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:21 - Finished Spring Data repository scanning in 11 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:21 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:21 - Starting embedded database: url='jdbc:h2:mem:f01372c5-9924-4a2e-a2b6-d920f5eb91b7;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:21 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:21 - HHH000026: Second-level cache disabled
2025-07-12 14:45:21 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:21 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:21 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:21 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:21 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:21 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:21 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:21 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:21 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:21 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:21 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:21 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:21 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:21 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:21 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:21 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:21 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:45:21 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:21 - 
    select
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.profile_type=?
Hibernate: 
    select
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.profile_type=?
2025-07-12 14:45:21 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:21 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:21 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUser_ShouldReturnProfiles_WhenUserExists" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.019">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:21 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:21 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:21 - Finished Spring Data repository scanning in 5 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:21 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:22 - Starting embedded database: url='jdbc:h2:mem:4295a362-309b-44d7-b752-838d403b471f;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:22 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:22 - HHH000026: Second-level cache disabled
2025-07-12 14:45:22 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:22 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:22 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:22 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:22 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:22 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:22 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:22 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:22 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:22 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:22 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:22 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:22 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:22 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:22 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:22 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:22 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:22 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:22 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
2025-07-12 14:45:22 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:22 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:22 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findActiveProfilesByUserId_ShouldReturnActiveProfiles" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.028">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:22 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:22 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:22 - Finished Spring Data repository scanning in 5 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:22 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:22 - Starting embedded database: url='jdbc:h2:mem:459f0f81-d511-447f-bbc7-f21bead5bc9f;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:45:22 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:45:22 - HHH000026: Second-level cache disabled
2025-07-12 14:45:22 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:45:22 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:45:22 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:45:22 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:22 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:45:22 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:45:22 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:45:22 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:45:22 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:45:22 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:45:22 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:45:22 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:45:22 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:45:22 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:45:22 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:45:22 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:22 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:22 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:22 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:45:22 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.is_active=true 
    order by
        up1_0.created_at desc
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.is_active=true 
    order by
        up1_0.created_at desc
2025-07-12 14:45:22 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:45:22 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:45:22 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByProfileType_ShouldReturnProfiles_WhenTypeMatches" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.036">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:45:22 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:45:22 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:45:22 - Finished Spring Data repository scanning in 6 ms. Found 2 JPA repository interfaces.
2025-07-12 14:45:22 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:45:22 - Starting embedded database: url='jdbc:h2:mem:6c1ae5fd-a609-4762-a082-86af528b213a;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:01 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:01 - HHH000026: Second-level cache disabled
2025-07-12 14:49:01 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:01 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:01 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:01 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:01 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:01 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:01 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:01 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:01 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:01 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:01 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:01 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:01 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:01 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:01 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:01 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:01 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:01 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:01 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    delete 
    from
        user_profiles 
    where
        profile_id=?
Hibernate: 
    delete 
    from
        user_profiles 
    where
        profile_id=?
2025-07-12 14:49:01 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:01 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:01 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.profile_type=?
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.profile_type=?
2025-07-12 14:49:01 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:01 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:01 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="existsByUserAndProfileKey_ShouldReturnTrue_WhenExists" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.082">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:02 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:02 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:02 - Finished Spring Data repository scanning in 4 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:02 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:02 - Starting embedded database: url='jdbc:h2:mem:1aa670d5-164f-4c79-8232-13989feab3ae;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:02 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:02 - HHH000026: Second-level cache disabled
2025-07-12 14:49:02 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:02 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:02 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:02 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:02 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:02 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:02 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:02 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:02 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:02 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:02 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:02 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:02 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:02 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:02 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:02 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:04 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:04 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:04 - 
    select
        up1_0.profile_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=? 
    fetch
        first ? rows only
Hibernate: 
    select
        up1_0.profile_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=? 
    fetch
        first ? rows only
2025-07-12 14:49:04 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:04 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:04 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="getProfileStatisticsByType_ShouldReturnStatistics" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.033">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:04 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:04 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:04 - Finished Spring Data repository scanning in 116 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:04 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:05 - Starting embedded database: url='jdbc:h2:mem:f486ba99-dcfa-441f-9a82-94642464b21b;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:05 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:05 - HHH000026: Second-level cache disabled
2025-07-12 14:49:05 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:05 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:05 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:05 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:05 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:05 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:05 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:05 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:05 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:05 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:05 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:05 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:05 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:05 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:05 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:05 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:06 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    select
        up1_0.profile_type,
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.is_active=true 
    group by
        up1_0.profile_type
Hibernate: 
    select
        up1_0.profile_type,
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.is_active=true 
    group by
        up1_0.profile_type
2025-07-12 14:49:06 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:06 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:06 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="countByUser_ShouldReturnCorrectCount" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.041">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:06 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:06 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:06 - Finished Spring Data repository scanning in 11 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:06 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:06 - Starting embedded database: url='jdbc:h2:mem:1a066a91-5119-4d31-82e4-354c96180fe8;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:06 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:06 - HHH000026: Second-level cache disabled
2025-07-12 14:49:06 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:06 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:06 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:06 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:06 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:06 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:06 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:06 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:06 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:06 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:06 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:06 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:06 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:06 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:06 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:06 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:06 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:06 - 
    select
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
Hibernate: 
    select
        count(up1_0.profile_id) 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
2025-07-12 14:49:06 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:06 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:06 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUserAndProfileType_ShouldReturnProfiles_WhenBothMatch" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.024">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:07 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:07 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:07 - Finished Spring Data repository scanning in 9 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:07 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:07 - Starting embedded database: url='jdbc:h2:mem:adb1c678-c32f-4a68-b014-80f3ec4f9ba0;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:07 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:07 - HHH000026: Second-level cache disabled
2025-07-12 14:49:07 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:07 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:07 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:07 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:07 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:07 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:07 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:07 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:07 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:07 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:07 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:07 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:07 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:07 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:07 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:07 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:07 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:07 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:07 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_type=?
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_type=?
2025-07-12 14:49:07 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:07 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:07 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findProfilesByValueContaining_ShouldReturnMatchingProfiles" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.03">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:07 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:07 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:07 - Finished Spring Data repository scanning in 12 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:07 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:07 - Starting embedded database: url='jdbc:h2:mem:b2bc2a59-64c2-414a-b055-e674bb78ad58;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:07 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:07 - HHH000026: Second-level cache disabled
2025-07-12 14:49:07 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:07 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:07 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:07 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:07 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:07 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:07 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:07 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:07 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:07 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:07 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:07 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:07 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:07 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:07 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:07 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:08 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.profile_value like ('%'||?||'%') escape ''
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.profile_value like ('%'||?||'%') escape ''
2025-07-12 14:49:08 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:08 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:08 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="searchProfiles_ShouldReturnMatchingProfiles" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.024">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:08 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:08 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:08 - Finished Spring Data repository scanning in 10 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:08 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:08 - Starting embedded database: url='jdbc:h2:mem:3086a553-25c9-4512-b9c5-5d932ef764ae;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:08 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:08 - HHH000026: Second-level cache disabled
2025-07-12 14:49:08 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:08 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:08 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:08 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:08 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:08 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:08 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:08 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:08 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:08 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:08 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:08 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:08 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:08 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:08 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:08 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:08 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        (
            ? is null 
            or up1_0.user_id=?
        ) 
        and (
            ? is null 
            or up1_0.profile_type=?
        ) 
        and (
            ? is null 
            or up1_0.is_active=?
        ) 
    offset
        ? rows 
    fetch
        first ? rows only
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        (
            ? is null 
            or up1_0.user_id=?
        ) 
        and (
            ? is null 
            or up1_0.profile_type=?
        ) 
        and (
            ? is null 
            or up1_0.is_active=?
        ) 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-12 14:49:08 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:08 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:08 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUserAndProfileKey_ShouldReturnEmpty_WhenNotExists" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.017">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:08 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:08 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:08 - Finished Spring Data repository scanning in 11 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:08 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:08 - Starting embedded database: url='jdbc:h2:mem:e26a2934-0e4a-4ef7-b11a-bee283bd13f9;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:08 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:08 - HHH000026: Second-level cache disabled
2025-07-12 14:49:08 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:08 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:08 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:08 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:08 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:08 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:08 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:08 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:08 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:08 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:08 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:08 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:08 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:08 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:08 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:08 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:08 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:08 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=?
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=? 
        and up1_0.profile_key=?
2025-07-12 14:49:08 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:08 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:08 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
  <testcase name="findByUserId_ShouldReturnProfiles_WhenUserExists" classname="com.circlelife.repository.UserProfileRepositoryTest" time="0.025">
    <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-12 14:49:08 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 14:49:08 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 14:49:08 - Finished Spring Data repository scanning in 11 ms. Found 2 JPA repository interfaces.
2025-07-12 14:49:09 - Replacing 'dataSource' DataSource bean with embedded version
2025-07-12 14:49:09 - Starting embedded database: url='jdbc:h2:mem:01e87d67-05c6-48bf-be9d-5267864a355d;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false', username='sa'
2025-07-12 14:49:09 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 14:49:09 - HHH000026: Second-level cache disabled
2025-07-12 14:49:09 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 14:49:09 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 14:49:09 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 14:49:09 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:09 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
2025-07-12 14:49:09 - 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
Hibernate: 
    create table user_profiles (
        is_active boolean not null,
        created_at timestamp(6) not null,
        profile_id bigint generated by default as identity,
        updated_at timestamp(6),
        user_id bigint not null,
        profile_type varchar(50) not null check (profile_type in ('PERSONAL','CONTACT','PREFERENCE','SOCIAL','PROFESSIONAL','CUSTOM')),
        profile_key varchar(100) not null,
        profile_value TEXT,
        primary key (profile_id)
    )
2025-07-12 14:49:09 - 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
Hibernate: 
    create table users (
        age integer,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        user_id bigint generated by default as identity,
        version bigint,
        username varchar(50) not null,
        email varchar(100) not null unique,
        full_name varchar(100) not null,
        address_additional_info varchar(255),
        address_city varchar(255),
        address_country varchar(255),
        address_postal_code varchar(255),
        address_state varchar(255),
        address_street varchar(255),
        password varchar(255) not null,
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','DELETED')),
        primary key (user_id),
        unique (username)
    )
2025-07-12 14:49:09 - 
    create index idx_profile_user_id 
       on user_profiles (user_id)
Hibernate: 
    create index idx_profile_user_id 
       on user_profiles (user_id)
2025-07-12 14:49:09 - 
    create index idx_profile_type 
       on user_profiles (profile_type)
Hibernate: 
    create index idx_profile_type 
       on user_profiles (profile_type)
2025-07-12 14:49:09 - 
    create index idx_profile_created_at 
       on user_profiles (created_at)
Hibernate: 
    create index idx_profile_created_at 
       on user_profiles (created_at)
2025-07-12 14:49:09 - 
    create index idx_user_email 
       on users (email)
Hibernate: 
    create index idx_user_email 
       on users (email)
2025-07-12 14:49:09 - 
    create index idx_user_username 
       on users (username)
Hibernate: 
    create index idx_user_username 
       on users (username)
2025-07-12 14:49:09 - 
    create index idx_user_status 
       on users (status)
Hibernate: 
    create index idx_user_status 
       on users (status)
2025-07-12 14:49:09 - 
    create index idx_user_created_at 
       on users (created_at)
Hibernate: 
    create index idx_user_created_at 
       on users (created_at)
2025-07-12 14:49:09 - 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
Hibernate: 
    alter table if exists user_profiles 
       add constraint FKjcad5nfve11khsnpwj1mv8frj 
       foreign key (user_id) 
       references users
2025-07-12 14:49:09 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:09 - 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        users
        (address_additional_info, address_city, address_country, address_postal_code, address_state, address_street, age, created_at, email, full_name, password, status, updated_at, username, version, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:09 - 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
Hibernate: 
    insert 
    into
        user_profiles
        (created_at, is_active, profile_key, profile_type, profile_value, updated_at, user_id, profile_id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
2025-07-12 14:49:09 - 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
Hibernate: 
    select
        up1_0.profile_id,
        up1_0.created_at,
        up1_0.is_active,
        up1_0.profile_key,
        up1_0.profile_type,
        up1_0.profile_value,
        up1_0.updated_at,
        up1_0.user_id 
    from
        user_profiles up1_0 
    where
        up1_0.user_id=?
2025-07-12 14:49:09 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 14:49:09 - 
    drop table if exists user_profiles cascade 
Hibernate: 
    drop table if exists user_profiles cascade 
2025-07-12 14:49:09 - 
    drop table if exists users cascade 
Hibernate: 
    drop table if exists users cascade 
]]></system-out>
  </testcase>
</testsuite>