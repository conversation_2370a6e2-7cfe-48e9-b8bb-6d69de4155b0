<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketplace Table Management System - <PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 4px solid #667eea;
            padding-bottom: 30px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: -40px -40px 40px -40px;
            padding: 40px;
            border-radius: 15px 15px 0 0;
        }
        .header h1 {
            font-size: 2.8em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header .subtitle {
            font-size: 1.3em;
            margin-top: 15px;
            opacity: 0.9;
        }
        .header .business-model {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 1.1em;
            font-weight: bold;
        }
        .section {
            margin-bottom: 50px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #667eea;
            border-left: 6px solid #667eea;
            padding-left: 20px;
            font-size: 2em;
            margin-bottom: 25px;
            background: linear-gradient(90deg, #f8f9ff, transparent);
            padding: 15px 20px;
            border-radius: 0 10px 10px 0;
        }
        .section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .section h4 {
            color: #667eea;
            font-size: 1.3em;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h4 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.2em;
        }
        .pricing-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .pricing-card h4 {
            color: white;
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        .pricing-card .price {
            font-size: 2.5em;
            font-weight: bold;
            margin: 15px 0;
        }
        .pricing-card .period {
            opacity: 0.8;
            font-size: 0.9em;
        }
        .revenue-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .revenue-card {
            background: linear-gradient(135deg, #764ba2, #667eea);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(118, 75, 162, 0.3);
        }
        .revenue-card h4 {
            color: white;
            margin-top: 0;
            font-size: 1.1em;
        }
        .revenue-card .amount {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .tech-stack {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border: 2px solid #eee;
        }
        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .tech-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .timeline {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 30px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #667eea;
        }
        .phase {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-left: 4px solid #764ba2;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        .phase h4 {
            margin-top: 0;
            color: #764ba2;
            font-size: 1.2em;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        .comparison-table tr:hover {
            background-color: #f0f2ff;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
        }
        .highlight h4 {
            color: #856404;
            margin-top: 0;
        }
        .role-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        .role-card {
            background: linear-gradient(135deg, #f8f9ff, #fff);
            padding: 25px;
            border-radius: 12px;
            border-top: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .admin-card {
            border-top-color: #764ba2;
            background: linear-gradient(135deg, #fff8f8, #fff);
        }
        .owner-card {
            border-top-color: #28a745;
            background: linear-gradient(135deg, #f8fff8, #fff);
        }
        .customer-card {
            border-top-color: #17a2b8;
            background: linear-gradient(135deg, #f8feff, #fff);
        }
        ul {
            padding-left: 25px;
        }
        li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        .emoji {
            font-size: 1.3em;
            margin-right: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .stat-number {
            font-size: 2.2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .flow-step {
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }
        .flow-step::before {
            content: "→";
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #667eea;
        }
        .flow-step:last-child::before {
            display: none;
        }
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
            .section {
                page-break-inside: avoid;
            }
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header {
                margin: -20px -20px 30px -20px;
                padding: 30px 20px;
            }
            .header h1 {
                font-size: 2.2em;
            }
            .feature-grid,
            .revenue-grid,
            .role-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Marketplace Table Management System</h1>
            <div class="subtitle">Nền Tảng Kết Nối Cửa Hàng F&B và Khách Hàng</div>
            <div class="business-model">
                💼 Business Model: B2B2C Marketplace với Thu Phí Subscription + Commission
            </div>
        </div>

        <div class="section">
            <h2>🎯 Tổng Quan Marketplace Model</h2>
            <div class="highlight">
                <h4><span class="emoji">🔄</span>Thay Đổi Mô Hình Kinh Doanh</h4>
                <p><strong>Từ:</strong> Hệ thống đơn lẻ cho 1 cửa hàng → <strong>Thành:</strong> Marketplace kết nối nhiều cửa hàng với khách hàng</p>
                <p><strong>Vai trò Platform:</strong> Trung gian thu phí từ cửa hàng, cung cấp công nghệ và khách hàng</p>
            </div>
            
            <h3>🏪 Stakeholders Trong Hệ Thống</h3>
            <div class="role-section">
                <div class="role-card admin-card">
                    <h4>🏢 Platform Admin</h4>
                    <ul>
                        <li>Quản lý toàn bộ marketplace</li>
                        <li>Duyệt đăng ký cửa hàng mới</li>
                        <li>Theo dõi revenue và commission</li>
                        <li>Xử lý disputes và support</li>
                        <li>Cấu hình subscription plans</li>
                    </ul>
                </div>
                <div class="role-card owner-card">
                    <h4>🏪 Restaurant Owner</h4>
                    <ul>
                        <li>Đăng ký và setup cửa hàng</li>
                        <li>Quản lý subscription và billing</li>
                        <li>Theo dõi analytics và reports</li>
                        <li>Quản lý staff và permissions</li>
                        <li>Cấu hình menu và pricing</li>
                    </ul>
                </div>
                <div class="role-card">
                    <h4>👨‍💼 Restaurant Manager</h4>
                    <ul>
                        <li>Quản lý bookings hàng ngày</li>
                        <li>Cập nhật trạng thái bàn</li>
                        <li>Xử lý orders và customer service</li>
                        <li>Xem reports và analytics</li>
                        <li>Quản lý staff schedules</li>
                    </ul>
                </div>
                <div class="role-card customer-card">
                    <h4>👤 Customer</h4>
                    <ul>
                        <li>Browse và tìm kiếm restaurants</li>
                        <li>Đặt bàn và pre-order</li>
                        <li>Thanh toán qua multiple methods</li>
                        <li>Rate và review restaurants</li>
                        <li>Loyalty program participation</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💰 Revenue Streams & Pricing Model</h2>
            
            <h3>📋 Subscription Plans</h3>
            <div class="revenue-grid">
                <div class="pricing-card">
                    <h4>🥉 BASIC PLAN</h4>
                    <div class="price">$29<span class="period">/tháng</span></div>
                    <ul style="text-align: left; color: white;">
                        <li>Tối đa 5 bàn</li>
                        <li>Basic booking management</li>
                        <li>Standard payment processing</li>
                        <li>Email support</li>
                        <li>Commission: 2.5%</li>
                    </ul>
                </div>
                <div class="pricing-card">
                    <h4>🥈 PROFESSIONAL</h4>
                    <div class="price">$79<span class="period">/tháng</span></div>
                    <ul style="text-align: left; color: white;">
                        <li>Tối đa 20 bàn</li>
                        <li>Advanced analytics</li>
                        <li>Custom branding</li>
                        <li>Priority support</li>
                        <li>Commission: 2.0%</li>
                    </ul>
                </div>
                <div class="pricing-card">
                    <h4>🥇 ENTERPRISE</h4>
                    <div class="price">$199<span class="period">/tháng</span></div>
                    <ul style="text-align: left; color: white;">
                        <li>Unlimited tables</li>
                        <li>Multi-location support</li>
                        <li>API access</li>
                        <li>Dedicated support</li>
                        <li>Commission: 1.5%</li>
                    </ul>
                </div>
            </div>

            <h3>💳 Commission Structure</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🍽️ Food Orders Commission</h4>
                    <ul>
                        <li>Basic Plan: 2.5%</li>
                        <li>Professional Plan: 2.0%</li>
                        <li>Enterprise Plan: 1.5%</li>
                        <li>Minimum: $0.50/transaction</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🪑 Table Booking Commission</h4>
                    <ul>
                        <li>Booking fee: 3.0% (all plans)</li>
                        <li>Minimum commission: $0.50</li>
                        <li>No commission on free bookings</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>📊 Volume Discounts</h4>
                    <ul>
                        <li>$5K-10K/month: -0.2%</li>
                        <li>$10K-25K/month: -0.3%</li>
                        <li>$25K+/month: -0.5%</li>
                        <li>Loyalty bonus: -0.2% after 12 months</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🔧 Additional Revenue</h4>
                    <ul>
                        <li>Setup fee: $99 one-time</li>
                        <li>SMS notifications: +$10/month</li>
                        <li>POS integration: +$25/month</li>
                        <li>Loyalty program: +$15/month</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏗️ Kiến Trúc Hệ Thống Marketplace</h2>

            <h3>🔧 Technology Stack</h3>
            <div class="tech-stack">
                <div class="tech-list">
                    <div class="tech-item"><strong>Backend:</strong><br>Java Spring Boot 3<br>Microservices</div>
                    <div class="tech-item"><strong>Database:</strong><br>MySQL (Primary)<br>Redis (Cache)</div>
                    <div class="tech-item"><strong>Message Queue:</strong><br>Apache Kafka<br>Event Streaming</div>
                    <div class="tech-item"><strong>Frontend:</strong><br>React.js<br>Admin Dashboard</div>
                    <div class="tech-item"><strong>Mobile:</strong><br>React Native<br>Cross-platform</div>
                    <div class="tech-item"><strong>Gateway:</strong><br>Spring Cloud Gateway<br>Load Balancing</div>
                </div>
            </div>

            <h3>🔧 Core Services</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 Authentication Service</h4>
                    <ul>
                        <li>Multi-tenant OAuth 2.0</li>
                        <li>Role-based access control</li>
                        <li>JWT token management</li>
                        <li>Social login integration</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏪 Restaurant Management</h4>
                    <ul>
                        <li>Restaurant onboarding</li>
                        <li>Profile management</li>
                        <li>Multi-location support</li>
                        <li>Staff management</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>💳 Billing Service</h4>
                    <ul>
                        <li>Subscription management</li>
                        <li>Commission calculation</li>
                        <li>Invoice generation</li>
                        <li>Payout processing</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>📊 Analytics Service</h4>
                    <ul>
                        <li>Platform-wide analytics</li>
                        <li>Restaurant performance</li>
                        <li>Revenue tracking</li>
                        <li>Business intelligence</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🗄️ Database Schema Updates</h2>

            <h3>📋 New Tables for Marketplace</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Table Name</th>
                        <th>Purpose</th>
                        <th>Key Features</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>SUBSCRIPTION_PLANS</strong></td>
                        <td>Define subscription tiers</td>
                        <td>Pricing, features, commission rates</td>
                    </tr>
                    <tr>
                        <td><strong>RESTAURANT_SUBSCRIPTIONS</strong></td>
                        <td>Track restaurant subscriptions</td>
                        <td>Status, billing cycle, auto-renewal</td>
                    </tr>
                    <tr>
                        <td><strong>BILLING_INVOICES</strong></td>
                        <td>Invoice management</td>
                        <td>Subscription, setup fees, add-ons</td>
                    </tr>
                    <tr>
                        <td><strong>COMMISSION_TRANSACTIONS</strong></td>
                        <td>Track commission per transaction</td>
                        <td>Order amount, commission rate, settlement</td>
                    </tr>
                    <tr>
                        <td><strong>PLATFORM_PAYOUTS</strong></td>
                        <td>Restaurant payout management</td>
                        <td>Net amount, commission deducted, status</td>
                    </tr>
                    <tr>
                        <td><strong>ADD_ON_SUBSCRIPTIONS</strong></td>
                        <td>Additional services</td>
                        <td>SMS, POS integration, loyalty program</td>
                    </tr>
                    <tr>
                        <td><strong>BILLING_SETTINGS</strong></td>
                        <td>Restaurant billing preferences</td>
                        <td>Payment methods, tax settings, addresses</td>
                    </tr>
                </tbody>
            </table>

            <h3>🔄 Enhanced User Management</h3>
            <div class="highlight">
                <h4>Updated USERS Table</h4>
                <p>Thêm fields mới để support multi-tenant:</p>
                <ul>
                    <li><strong>tenant_id:</strong> Xác định restaurant owner thuộc về</li>
                    <li><strong>role:</strong> PLATFORM_ADMIN, RESTAURANT_OWNER, RESTAURANT_MANAGER, RESTAURANT_STAFF, CUSTOMER</li>
                    <li><strong>permissions:</strong> JSON field chứa quyền hạn chi tiết</li>
                    <li><strong>subscription_status:</strong> Active, suspended, cancelled</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Restaurant Onboarding Process</h2>

            <h3>📋 Onboarding Journey (7-10 ngày)</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Phase 1: Registration & Verification (2-3 ngày)</h4>
                    <div class="flow-step">
                        <strong>Step 1:</strong> Initial signup với basic info
                    </div>
                    <div class="flow-step">
                        <strong>Step 2:</strong> Email verification và account activation
                    </div>
                    <div class="flow-step">
                        <strong>Step 3:</strong> Business information và contact details
                    </div>
                    <div class="flow-step">
                        <strong>Step 4:</strong> Document upload (license, permits, tax ID)
                    </div>
                    <div class="flow-step">
                        <strong>Step 5:</strong> Admin review và approval (1-2 business days)
                    </div>
                </div>

                <div class="phase">
                    <h4>Phase 2: Subscription & Payment Setup (1-2 ngày)</h4>
                    <div class="flow-step">
                        <strong>Step 6:</strong> Choose subscription plan với feature comparison
                    </div>
                    <div class="flow-step">
                        <strong>Step 7:</strong> Payment method setup và first month billing
                    </div>
                    <div class="flow-step">
                        <strong>Step 8:</strong> Billing configuration và payout account setup
                    </div>
                </div>

                <div class="phase">
                    <h4>Phase 3: Restaurant Configuration (3-5 ngày)</h4>
                    <div class="flow-step">
                        <strong>Step 9:</strong> Restaurant profile với photos và description
                    </div>
                    <div class="flow-step">
                        <strong>Step 10:</strong> Menu setup với categories và pricing
                    </div>
                    <div class="flow-step">
                        <strong>Step 11:</strong> Table configuration và layout design
                    </div>
                    <div class="flow-step">
                        <strong>Step 12:</strong> Staff management và role assignment
                    </div>
                </div>

                <div class="phase">
                    <h4>Phase 4: Training & Go-Live (2-3 ngày)</h4>
                    <div class="flow-step">
                        <strong>Step 13:</strong> Platform training với video tutorials
                    </div>
                    <div class="flow-step">
                        <strong>Step 14:</strong> Test environment với mock transactions
                    </div>
                    <div class="flow-step">
                        <strong>Step 15:</strong> Go live với customer-facing profile
                    </div>
                    <div class="flow-step">
                        <strong>Step 16:</strong> 30-day follow-up và optimization
                    </div>
                </div>
            </div>

            <h3>🎯 Onboarding Success Metrics</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">85%</div>
                    <div class="stat-label">Registration to Approval</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">90%</div>
                    <div class="stat-label">Approval to Payment</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Payment to Go-Live</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">75%</div>
                    <div class="stat-label">Overall Completion</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Platform Admin Dashboard</h2>

            <h3>🎛️ Dashboard Components</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📈 Revenue Overview</h4>
                    <ul>
                        <li>Monthly Recurring Revenue (MRR)</li>
                        <li>Commission revenue tracking</li>
                        <li>Growth rate analysis</li>
                        <li>Revenue forecasting</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏪 Restaurant Management</h4>
                    <ul>
                        <li>Pending applications queue</li>
                        <li>Active restaurant monitoring</li>
                        <li>Performance analytics</li>
                        <li>Suspension/activation controls</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>💳 Billing Management</h4>
                    <ul>
                        <li>Subscription status overview</li>
                        <li>Failed payment handling</li>
                        <li>Commission payout processing</li>
                        <li>Dispute resolution queue</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👥 User Analytics</h4>
                    <ul>
                        <li>Customer acquisition metrics</li>
                        <li>User engagement tracking</li>
                        <li>Churn rate analysis</li>
                        <li>Lifetime value calculation</li>
                    </ul>
                </div>
            </div>

            <h3>🔧 Management Tools</h3>
            <div class="highlight">
                <h4>Key Administrative Functions</h4>
                <ul>
                    <li><strong>Restaurant Approval:</strong> Document verification, background checks, manual review process</li>
                    <li><strong>Subscription Management:</strong> Plan modifications, pricing updates, promotional offers</li>
                    <li><strong>Commission Processing:</strong> Automated calculations, manual adjustments, payout scheduling</li>
                    <li><strong>Dispute Resolution:</strong> Billing disputes, refund processing, customer support escalation</li>
                    <li><strong>Platform Configuration:</strong> System settings, feature flags, maintenance mode</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>💼 Business Model & Financial Projections</h2>

            <h3>📈 Revenue Projections</h3>
            <div class="stats-grid">
                <div class="revenue-card">
                    <h4>Target Restaurants</h4>
                    <div class="amount">100</div>
                    <div style="opacity: 0.8;">After 12 months</div>
                </div>
                <div class="revenue-card">
                    <h4>Average Subscription</h4>
                    <div class="amount">$79</div>
                    <div style="opacity: 0.8;">Per month</div>
                </div>
                <div class="revenue-card">
                    <h4>Monthly Commission</h4>
                    <div class="amount">$500</div>
                    <div style="opacity: 0.8;">Per restaurant</div>
                </div>
                <div class="revenue-card">
                    <h4>Annual Revenue</h4>
                    <div class="amount">$695K</div>
                    <div style="opacity: 0.8;">Potential</div>
                </div>
            </div>

            <h3>💰 Cost Structure</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Amount</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Development Cost</strong></td>
                        <td>$500K - $700K</td>
                        <td>10-12 months development (tăng 25%)</td>
                    </tr>
                    <tr>
                        <td><strong>Infrastructure (Monthly)</strong></td>
                        <td>$3K - $5K</td>
                        <td>AWS/Azure, scales with usage</td>
                    </tr>
                    <tr>
                        <td><strong>Team Size</strong></td>
                        <td>10-12 people</td>
                        <td>Thêm billing specialist, platform admin</td>
                    </tr>
                    <tr>
                        <td><strong>Operating Cost</strong></td>
                        <td>$4K - $7K/month</td>
                        <td>Infrastructure + third-party services</td>
                    </tr>
                    <tr>
                        <td><strong>Break-even Point</strong></td>
                        <td>50-75 restaurants</td>
                        <td>Approximately 8-10 months</td>
                    </tr>
                </tbody>
            </table>

            <h3>🚀 Go-to-Market Strategy</h3>
            <div class="timeline">
                <div class="phase">
                    <h4>Phase 1: MVP Launch (Months 1-3)</h4>
                    <ul>
                        <li>🎯 Target: 10-15 pilot restaurants</li>
                        <li>📍 Geographic focus: 1-2 thành phố lớn</li>
                        <li>💰 Pricing: 50% discount cho early adopters</li>
                        <li>🔧 Features: Core booking + payment + basic analytics</li>
                    </ul>
                </div>

                <div class="phase">
                    <h4>Phase 2: Market Expansion (Months 4-8)</h4>
                    <ul>
                        <li>🎯 Target: 50-75 restaurants</li>
                        <li>📍 Expand to 3-5 cities</li>
                        <li>💰 Full pricing model implementation</li>
                        <li>🔧 Advanced features + comprehensive analytics</li>
                    </ul>
                </div>

                <div class="phase">
                    <h4>Phase 3: Scale & Optimize (Months 9-12)</h4>
                    <ul>
                        <li>🎯 Target: 100+ restaurants</li>
                        <li>📍 National expansion</li>
                        <li>💰 Premium features và add-ons launch</li>
                        <li>🔧 AI-powered recommendations và automation</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Value Propositions</h2>

            <h3>🏪 Cho Restaurant Partners</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💻 Technology Platform</h4>
                    <p>No upfront development cost, ready-to-use solution với professional features</p>
                </div>
                <div class="feature-card">
                    <h4>📈 Customer Acquisition</h4>
                    <p>Access to platform's customer base và marketing reach</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Payment Processing</h4>
                    <p>Secure, PCI-compliant payment với multiple methods</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Business Intelligence</h4>
                    <p>Advanced analytics và insights để optimize operations</p>
                </div>
            </div>

            <h3>💼 Cho Platform Owner</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>💰 Recurring Revenue</h4>
                    <p>Predictable monthly income từ subscriptions</p>
                </div>
                <div class="feature-card">
                    <h4>📈 Scalable Model</h4>
                    <p>Commission grows với transaction volume</p>
                </div>
                <div class="feature-card">
                    <h4>🌐 Network Effects</h4>
                    <p>More restaurants attract more customers và ngược lại</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Data Monetization</h4>
                    <p>Valuable insights từ aggregated data</p>
                </div>
            </div>

            <h3>👤 Cho Customers</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔍 Discovery</h4>
                    <p>Single platform để discover và book multiple restaurants</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ Convenience</h4>
                    <p>Seamless booking experience với consistent interface</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Unified Payments</h4>
                    <p>One payment method cho all restaurants</p>
                </div>
                <div class="feature-card">
                    <h4>🎁 Loyalty Benefits</h4>
                    <p>Cross-restaurant loyalty program và rewards</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📝 Kết Luận</h2>

            <div class="highlight">
                <h4>🚀 Marketplace Table Management System</h4>
                <p>Hệ thống này đại diện cho một evolution từ single-tenant solution thành một comprehensive marketplace platform, tạo ra sustainable business model với multiple revenue streams.</p>
            </div>

            <h3>✅ Key Success Factors</h3>
            <ul>
                <li><strong>🏪 Restaurant Value:</strong> Immediate access to technology và customer base without development cost</li>
                <li><strong>👤 Customer Experience:</strong> Unified platform với consistent experience across restaurants</li>
                <li><strong>💰 Revenue Diversification:</strong> Subscription + Commission + Add-ons tạo stable income</li>
                <li><strong>📈 Scalability:</strong> Network effects và data advantages tăng theo scale</li>
                <li><strong>🔧 Technology Excellence:</strong> Modern architecture support growth và innovation</li>
            </ul>

            <h3>🎯 Next Steps</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">10-12</div>
                    <div class="stat-label">Months Development</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$500K-700K</div>
                    <div class="stat-label">Investment Required</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50-75</div>
                    <div class="stat-label">Restaurants for Break-even</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$695K</div>
                    <div class="stat-label">Annual Revenue Potential</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px;">
                <h3 style="color: white; margin-top: 0;">🌟 Ready to Transform F&B Industry</h3>
                <p style="font-size: 1.1em; margin-bottom: 0;">Marketplace Table Management System - Connecting Restaurants with Customers</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; color: #666;">
            <p><strong>📄 Tài liệu được tạo bởi Augment Agent</strong></p>
            <p>🗓️ Ngày tạo: 14/07/2025 | 📋 Version: 2.0 - Marketplace Model</p>
        </div>
    </div>
</body>
</html>
