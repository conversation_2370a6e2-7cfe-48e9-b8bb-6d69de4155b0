<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketplace Table Management - Workflow Diagrams</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 4px solid #667eea;
            padding-bottom: 30px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: -40px -40px 40px -40px;
            padding: 40px;
            border-radius: 15px 15px 0 0;
        }
        .header h1 {
            font-size: 2.8em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header .subtitle {
            font-size: 1.3em;
            margin-top: 15px;
            opacity: 0.9;
        }
        .workflow-section {
            margin-bottom: 60px;
            page-break-inside: avoid;
        }
        .workflow-section h2 {
            color: #667eea;
            border-left: 6px solid #667eea;
            padding-left: 20px;
            font-size: 2em;
            margin-bottom: 30px;
            background: linear-gradient(90deg, #f8f9ff, transparent);
            padding: 15px 20px;
            border-radius: 0 10px 10px 0;
        }
        .workflow-section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .workflow-container {
            background: #f8f9ff;
            border: 2px solid #e0e6ff;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            position: relative;
        }
        .workflow-title {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            position: absolute;
            top: -15px;
            left: 30px;
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .step {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            position: relative;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
            transition: transform 0.3s ease;
        }
        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        .step-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 15px;
        }
        .step-title {
            color: #667eea;
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        .step-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.5;
        }
        .step-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .step-details ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        .step-details li {
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .flow-arrow {
            text-align: center;
            font-size: 2em;
            color: #667eea;
            margin: 10px 0;
        }
        .parallel-flows {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .decision-point {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border: 2px solid #e17055;
            color: #2d3436;
        }
        .decision-point .step-number {
            background: linear-gradient(135deg, #e17055, #d63031);
        }
        .success-step {
            background: linear-gradient(135deg, #a8e6cf, #88d8a3);
            border: 2px solid #00b894;
            color: #2d3436;
        }
        .success-step .step-number {
            background: linear-gradient(135deg, #00b894, #00a085);
        }
        .error-step {
            background: linear-gradient(135deg, #fab1a0, #e17055);
            border: 2px solid #d63031;
            color: #2d3436;
        }
        .error-step .step-number {
            background: linear-gradient(135deg, #d63031, #c0392b);
        }
        .actor-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .actor-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .actor-card h4 {
            margin-top: 0;
            font-size: 1.2em;
        }
        .actor-card .role {
            opacity: 0.9;
            font-size: 0.9em;
        }
        .timeline-flow {
            position: relative;
            padding-left: 40px;
        }
        .timeline-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        .timeline-step {
            position: relative;
            margin-bottom: 30px;
        }
        .timeline-step::before {
            content: '';
            position: absolute;
            left: -29px;
            top: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #667eea;
        }
        .system-interaction {
            background: linear-gradient(135deg, #e8f4fd, #c3e9ff);
            border: 2px solid #0984e3;
            color: #2d3436;
        }
        .system-interaction .step-number {
            background: linear-gradient(135deg, #0984e3, #0770c4);
        }
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                padding: 0;
            }
            .workflow-section {
                page-break-inside: avoid;
            }
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header {
                margin: -20px -20px 30px -20px;
                padding: 30px 20px;
            }
            .workflow-steps {
                grid-template-columns: 1fr;
            }
            .parallel-flows {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Marketplace Workflows</h1>
            <div class="subtitle">Chi Tiết Quy Trình Hoạt Động Hệ Thống</div>
        </div>

        <div class="workflow-section">
            <h2>🏪 Restaurant Onboarding Workflow</h2>
            
            <div class="workflow-container">
                <div class="workflow-title">Restaurant Registration & Setup Process</div>
                
                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-title">Initial Registration</div>
                        <div class="step-description">Restaurant owner tạo account và điền thông tin cơ bản</div>
                        <div class="step-details">
                            <ul>
                                <li>Restaurant name & type</li>
                                <li>Owner contact info</li>
                                <li>Business address</li>
                                <li>Estimated revenue</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">Email Verification</div>
                        <div class="step-description">Xác thực email và kích hoạt account</div>
                        <div class="step-details">
                            <ul>
                                <li>Automated verification email</li>
                                <li>Click verification link</li>
                                <li>Account activation</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">Document Upload</div>
                        <div class="step-description">Upload các giấy tờ pháp lý cần thiết</div>
                        <div class="step-details">
                            <ul>
                                <li>Business license</li>
                                <li>Food service permit</li>
                                <li>Tax ID certificate</li>
                                <li>Owner identification</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step decision-point">
                        <div class="step-number">4</div>
                        <div class="step-title">Admin Review</div>
                        <div class="step-description">Platform admin xem xét và phê duyệt</div>
                        <div class="step-details">
                            <ul>
                                <li>Document verification</li>
                                <li>Background check</li>
                                <li>Manual review process</li>
                                <li>1-2 business days</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step success-step">
                        <div class="step-number">5</div>
                        <div class="step-title">Subscription Setup</div>
                        <div class="step-description">Chọn gói dịch vụ và thiết lập thanh toán</div>
                        <div class="step-details">
                            <ul>
                                <li>Compare subscription plans</li>
                                <li>Payment method setup</li>
                                <li>First month billing</li>
                                <li>Auto-renewal configuration</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">6</div>
                        <div class="step-title">Restaurant Profile</div>
                        <div class="step-description">Tạo profile công khai cho khách hàng</div>
                        <div class="step-details">
                            <ul>
                                <li>Restaurant description</li>
                                <li>Photo gallery upload</li>
                                <li>Operating hours</li>
                                <li>Contact information</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">7</div>
                        <div class="step-title">Menu Configuration</div>
                        <div class="step-description">Setup menu items và pricing</div>
                        <div class="step-details">
                            <ul>
                                <li>Menu categories</li>
                                <li>Item details & photos</li>
                                <li>Pricing configuration</li>
                                <li>Allergen information</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">8</div>
                        <div class="step-title">Table Setup</div>
                        <div class="step-description">Cấu hình bàn và layout</div>
                        <div class="step-details">
                            <ul>
                                <li>Table layout design</li>
                                <li>Capacity configuration</li>
                                <li>Booking policies</li>
                                <li>Special features</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">9</div>
                        <div class="step-title">Staff Management</div>
                        <div class="step-description">Thêm nhân viên và phân quyền</div>
                        <div class="step-details">
                            <ul>
                                <li>Add managers & staff</li>
                                <li>Role assignment</li>
                                <li>Permission configuration</li>
                                <li>Training access</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">10</div>
                        <div class="step-title">Testing & Training</div>
                        <div class="step-description">Test hệ thống và training nhân viên</div>
                        <div class="step-details">
                            <ul>
                                <li>Mock bookings & orders</li>
                                <li>Payment testing</li>
                                <li>Staff training sessions</li>
                                <li>System walkthrough</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step success-step">
                        <div class="step-number">11</div>
                        <div class="step-title">Go Live</div>
                        <div class="step-description">Kích hoạt restaurant trên platform</div>
                        <div class="step-details">
                            <ul>
                                <li>Final checklist completion</li>
                                <li>Public profile activation</li>
                                <li>Customer notifications</li>
                                <li>Welcome marketing</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">12</div>
                        <div class="step-title">Post-Launch Support</div>
                        <div class="step-description">Theo dõi và hỗ trợ sau khi go-live</div>
                        <div class="step-details">
                            <ul>
                                <li>30-day check-in calls</li>
                                <li>Performance monitoring</li>
                                <li>Optimization recommendations</li>
                                <li>Ongoing support setup</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-section">
            <h2>👤 Customer Booking Workflow</h2>
            
            <div class="workflow-container">
                <div class="workflow-title">Customer Table Booking Process</div>
                
                <div class="timeline-flow">
                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-title">Restaurant Discovery</div>
                            <div class="step-description">Customer tìm kiếm và chọn restaurant</div>
                            <div class="step-details">
                                <ul>
                                    <li>Browse nearby restaurants</li>
                                    <li>Filter by cuisine, price, rating</li>
                                    <li>View restaurant details</li>
                                    <li>Check availability</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step decision-point">
                            <div class="step-number">2</div>
                            <div class="step-title">Authentication Check</div>
                            <div class="step-description">Kiểm tra đăng nhập hoặc tạo account</div>
                            <div class="step-details">
                                <ul>
                                    <li>Check existing session</li>
                                    <li>Social login options</li>
                                    <li>Guest booking option</li>
                                    <li>Profile completion</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-title">Booking Details</div>
                            <div class="step-description">Chọn thời gian, số người và yêu cầu đặc biệt</div>
                            <div class="step-details">
                                <ul>
                                    <li>Date & time selection</li>
                                    <li>Party size specification</li>
                                    <li>Table preferences</li>
                                    <li>Special requests</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">4</div>
                            <div class="step-title">Availability Check</div>
                            <div class="step-description">Hệ thống kiểm tra bàn trống real-time</div>
                            <div class="step-details">
                                <ul>
                                    <li>Real-time table status</li>
                                    <li>Capacity matching</li>
                                    <li>Time slot validation</li>
                                    <li>Alternative suggestions</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">5</div>
                            <div class="step-title">Pre-order (Optional)</div>
                            <div class="step-description">Đặt món trước nếu muốn</div>
                            <div class="step-details">
                                <ul>
                                    <li>Browse menu items</li>
                                    <li>Add items to cart</li>
                                    <li>Customize orders</li>
                                    <li>Calculate total</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">6</div>
                            <div class="step-title">Payment Processing</div>
                            <div class="step-description">Thanh toán booking fee và order (nếu có)</div>
                            <div class="step-details">
                                <ul>
                                    <li>Price breakdown display</li>
                                    <li>Payment method selection</li>
                                    <li>Commission calculation</li>
                                    <li>Secure payment processing</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step success-step">
                            <div class="step-number">7</div>
                            <div class="step-title">Booking Confirmation</div>
                            <div class="step-description">Xác nhận booking và gửi thông báo</div>
                            <div class="step-details">
                                <ul>
                                    <li>Booking confirmation email</li>
                                    <li>QR code generation</li>
                                    <li>Calendar integration</li>
                                    <li>SMS notification</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-section">
            <h2>💳 Payment & Commission Workflow</h2>

            <div class="workflow-container">
                <div class="workflow-title">Payment Processing với Platform Commission</div>

                <div class="actor-section">
                    <div class="actor-card">
                        <h4>👤 Customer</h4>
                        <div class="role">Initiates payment</div>
                    </div>
                    <div class="actor-card">
                        <h4>💳 Payment Gateway</h4>
                        <div class="role">Processes transaction</div>
                    </div>
                    <div class="actor-card">
                        <h4>🏪 Restaurant</h4>
                        <div class="role">Receives net amount</div>
                    </div>
                    <div class="actor-card">
                        <h4>🏢 Platform</h4>
                        <div class="role">Collects commission</div>
                    </div>
                </div>

                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-title">Order Completion</div>
                        <div class="step-description">Customer hoàn tất order với total amount</div>
                        <div class="step-details">
                            <ul>
                                <li>Calculate subtotal</li>
                                <li>Add taxes & surcharges</li>
                                <li>Display final amount</li>
                                <li>Confirm order details</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">2</div>
                        <div class="step-title">Commission Calculation</div>
                        <div class="step-description">Hệ thống tính commission dựa trên subscription plan</div>
                        <div class="step-details">
                            <ul>
                                <li>Get restaurant's plan</li>
                                <li>Apply commission rate</li>
                                <li>Check volume discounts</li>
                                <li>Calculate net amount</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">Payment Gateway</div>
                        <div class="step-description">Charge customer full amount</div>
                        <div class="step-details">
                            <ul>
                                <li>Secure payment processing</li>
                                <li>Card/wallet authorization</li>
                                <li>Fraud detection</li>
                                <li>Transaction confirmation</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step decision-point">
                        <div class="step-number">4</div>
                        <div class="step-title">Payment Status</div>
                        <div class="step-description">Kiểm tra kết quả thanh toán</div>
                        <div class="step-details">
                            <ul>
                                <li>Success: Continue to split</li>
                                <li>Failed: Retry or cancel</li>
                                <li>Pending: Wait for confirmation</li>
                                <li>Update transaction status</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">5</div>
                        <div class="step-title">Amount Splitting</div>
                        <div class="step-description">Chia tiền giữa platform và restaurant</div>
                        <div class="step-details">
                            <ul>
                                <li>Platform commission: X%</li>
                                <li>Restaurant amount: (100-X)%</li>
                                <li>Record commission transaction</li>
                                <li>Update payout queue</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step success-step">
                        <div class="step-number">6</div>
                        <div class="step-title">Settlement</div>
                        <div class="step-description">Chuyển tiền cho restaurant theo schedule</div>
                        <div class="step-details">
                            <ul>
                                <li>Weekly payout schedule</li>
                                <li>Minimum payout threshold</li>
                                <li>Bank transfer processing</li>
                                <li>Payout confirmation</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">7</div>
                        <div class="step-title">Reporting</div>
                        <div class="step-description">Cập nhật reports và analytics</div>
                        <div class="step-details">
                            <ul>
                                <li>Transaction recording</li>
                                <li>Commission tracking</li>
                                <li>Revenue analytics</li>
                                <li>Monthly statements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-section">
            <h2>📊 Subscription Billing Workflow</h2>

            <div class="workflow-container">
                <div class="workflow-title">Monthly Subscription Billing Process</div>

                <div class="parallel-flows">
                    <div>
                        <h3>🔄 Automated Billing</h3>
                        <div class="workflow-steps">
                            <div class="step system-interaction">
                                <div class="step-number">1</div>
                                <div class="step-title">Billing Trigger</div>
                                <div class="step-description">Hệ thống tự động trigger billing</div>
                                <div class="step-details">
                                    <ul>
                                        <li>Monthly billing cycle</li>
                                        <li>Check subscription status</li>
                                        <li>Generate invoice</li>
                                        <li>Calculate total amount</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-title">Auto-charge</div>
                                <div class="step-description">Charge saved payment method</div>
                                <div class="step-details">
                                    <ul>
                                        <li>Use saved card</li>
                                        <li>Process payment</li>
                                        <li>Handle failures</li>
                                        <li>Update status</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="step success-step">
                                <div class="step-number">3</div>
                                <div class="step-title">Success</div>
                                <div class="step-description">Billing thành công</div>
                                <div class="step-details">
                                    <ul>
                                        <li>Mark invoice as paid</li>
                                        <li>Extend subscription</li>
                                        <li>Send receipt</li>
                                        <li>Continue service</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3>⚠️ Failed Payment Handling</h3>
                        <div class="workflow-steps">
                            <div class="step error-step">
                                <div class="step-number">1</div>
                                <div class="step-title">Payment Failed</div>
                                <div class="step-description">Auto-charge thất bại</div>
                                <div class="step-details">
                                    <ul>
                                        <li>Insufficient funds</li>
                                        <li>Expired card</li>
                                        <li>Bank decline</li>
                                        <li>Technical error</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-title">Retry Logic</div>
                                <div class="step-description">Thử lại thanh toán</div>
                                <div class="step-details">
                                    <ul>
                                        <li>3 retry attempts</li>
                                        <li>24h intervals</li>
                                        <li>Email notifications</li>
                                        <li>Grace period: 3 days</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="step error-step">
                                <div class="step-number">3</div>
                                <div class="step-title">Suspension</div>
                                <div class="step-description">Tạm ngưng dịch vụ</div>
                                <div class="step-details">
                                    <ul>
                                        <li>Suspend restaurant account</li>
                                        <li>Disable booking system</li>
                                        <li>Send suspension notice</li>
                                        <li>Provide payment link</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-section">
            <h2>🏢 Platform Admin Workflow</h2>

            <div class="workflow-container">
                <div class="workflow-title">Platform Administration & Management</div>

                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-title">Daily Dashboard Review</div>
                        <div class="step-description">Kiểm tra metrics và KPIs hàng ngày</div>
                        <div class="step-details">
                            <ul>
                                <li>Revenue metrics review</li>
                                <li>New restaurant applications</li>
                                <li>Failed payments alerts</li>
                                <li>Customer support tickets</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step decision-point">
                        <div class="step-number">2</div>
                        <div class="step-title">Restaurant Applications</div>
                        <div class="step-description">Review và approve/reject applications</div>
                        <div class="step-details">
                            <ul>
                                <li>Document verification</li>
                                <li>Background checks</li>
                                <li>Business validation</li>
                                <li>Approval decision</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step system-interaction">
                        <div class="step-number">3</div>
                        <div class="step-title">Commission Processing</div>
                        <div class="step-description">Xử lý commission và payouts</div>
                        <div class="step-details">
                            <ul>
                                <li>Weekly payout calculations</li>
                                <li>Bank transfer processing</li>
                                <li>Dispute resolution</li>
                                <li>Financial reconciliation</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">Support Management</div>
                        <div class="step-description">Handle customer và restaurant support</div>
                        <div class="step-details">
                            <ul>
                                <li>Ticket prioritization</li>
                                <li>Issue resolution</li>
                                <li>Escalation handling</li>
                                <li>Follow-up communications</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">5</div>
                        <div class="step-title">Platform Optimization</div>
                        <div class="step-description">Continuous improvement và optimization</div>
                        <div class="step-details">
                            <ul>
                                <li>Performance monitoring</li>
                                <li>Feature usage analysis</li>
                                <li>User feedback review</li>
                                <li>System updates planning</li>
                            </ul>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">6</div>
                        <div class="step-title">Reporting & Analytics</div>
                        <div class="step-description">Generate reports cho stakeholders</div>
                        <div class="step-details">
                            <ul>
                                <li>Monthly business reports</li>
                                <li>Financial statements</li>
                                <li>Growth metrics analysis</li>
                                <li>Strategic recommendations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-section">
            <h2>🔄 Real-time Table Status Workflow</h2>

            <div class="workflow-container">
                <div class="workflow-title">Table Status Management & Real-time Updates</div>

                <div class="timeline-flow">
                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">1</div>
                            <div class="step-title">Status Change Trigger</div>
                            <div class="step-description">Nhân viên hoặc system update table status</div>
                            <div class="step-details">
                                <ul>
                                    <li>Staff manual update</li>
                                    <li>Booking confirmation</li>
                                    <li>Customer check-in</li>
                                    <li>Automated time-based changes</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">2</div>
                            <div class="step-title">Database Update</div>
                            <div class="step-description">Cập nhật database với status mới</div>
                            <div class="step-details">
                                <ul>
                                    <li>Update table status</li>
                                    <li>Timestamp recording</li>
                                    <li>User tracking</li>
                                    <li>Validation checks</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">3</div>
                            <div class="step-title">Event Publishing</div>
                            <div class="step-description">Publish event qua Kafka</div>
                            <div class="step-details">
                                <ul>
                                    <li>Create status change event</li>
                                    <li>Publish to Kafka topic</li>
                                    <li>Include relevant metadata</li>
                                    <li>Ensure message delivery</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step system-interaction">
                            <div class="step-number">4</div>
                            <div class="step-title">Cache Update</div>
                            <div class="step-description">Update Redis cache cho fast access</div>
                            <div class="step-details">
                                <ul>
                                    <li>Update Redis cache</li>
                                    <li>Invalidate related keys</li>
                                    <li>Set appropriate TTL</li>
                                    <li>Ensure consistency</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">5</div>
                            <div class="step-title">Real-time Notifications</div>
                            <div class="step-description">Gửi updates đến connected clients</div>
                            <div class="step-details">
                                <ul>
                                    <li>WebSocket notifications</li>
                                    <li>Push notifications</li>
                                    <li>Email alerts (if needed)</li>
                                    <li>SMS notifications</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-step">
                        <div class="step">
                            <div class="step-number">6</div>
                            <div class="step-title">UI Updates</div>
                            <div class="step-description">Update giao diện người dùng</div>
                            <div class="step-details">
                                <ul>
                                    <li>Restaurant dashboard update</li>
                                    <li>Customer app refresh</li>
                                    <li>Admin panel updates</li>
                                    <li>Visual status indicators</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px;">
            <h3 style="color: white; margin-top: 0;">🔄 Complete Workflow Documentation</h3>
            <p style="font-size: 1.1em; margin-bottom: 0;">Comprehensive process flows for Marketplace Table Management System</p>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; color: #666;">
            <p><strong>📄 Workflow Documentation by Augment Agent</strong></p>
            <p>🗓️ Created: 14/07/2025 | 📋 Version: 1.0</p>
        </div>
    </div>
</body>
</html>
