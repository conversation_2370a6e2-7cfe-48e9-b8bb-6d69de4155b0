sequenceDiagram
    participant Client as REST Client
    participant Producer as Producer App
    participant <PERSON><PERSON><PERSON> as Kafka Broker
    participant Consumer as Consumer App
    participant DB as H2 Database
    
    Client->>Producer: POST /api/messages/user
    Producer->>Producer: Validate & Serialize to JSON
    Producer->>Kafka: Send to topic 'user-events'
    Kafka->>Kafka: Store in partition
    Kafka-->>Producer: Ack (offset)
    Producer-->>Client: Success response
    
    loop Consumer Polling
        Consumer->>Kafka: Poll for messages
        Kafka-->>Consumer: Return messages batch
        Consumer->>Consumer: Deserialize JSON to Object
        Consumer->>Consumer: Process business logic
        Consumer->>DB: Save to database
        Consumer->>Kafka: Commit offset (auto)
    end