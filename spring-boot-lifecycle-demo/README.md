# Spring Boot 3 Lifecycle Demo

Ứng dụng demo minh họa toàn bộ lifecycle và cấu trúc của Spring Boot 3, bao gồm:

## 🚀 Tính năng chính

### 1. Application Lifecycle Events
- ApplicationStartingEvent
- ApplicationEnvironmentPreparedEvent  
- ApplicationContextInitializedEvent
- ApplicationPreparedEvent
- ApplicationStartedEvent
- ApplicationReadyEvent
- ContextRefreshedEvent
- ContextClosedEvent (khi shutdown)

### 2. Bean Lifecycle
- Constructor injection
- BeanNameAware, BeanFactoryAware, ApplicationContextAware
- @PostConstruct
- InitializingBean.afterPropertiesSet()
- BeanPostProcessor (before/after initialization)
- @PreDestroy
- DisposableBean.destroy()

### 3. Component Layers
- **Controller Layer**: REST endpoints với lifecycle logging
- **Service Layer**: Business logic với dependency injection
- **Repository Layer**: Data access với JPA lifecycle
- **Configuration**: Bean configuration và property binding

### 4. Event Handling
- Built-in Spring Boot events
- Custom events và event listeners
- Event publishing và handling

## 🏗️ Cấu trúc dự án

```
spring-boot-lifecycle-demo/
├── src/main/java/com/example/lifecycle/
│   ├── SpringBootLifecycleDemoApplication.java  # Main application class
│   ├── config/
│   │   └── AppConfiguration.java                # Configuration beans
│   ├── controller/
│   │   ├── UserController.java                  # REST API endpoints
│   │   └── LifecycleController.java             # Lifecycle info endpoints
│   ├── service/
│   │   └── UserService.java                     # Business logic layer
│   ├── repository/
│   │   └── UserRepository.java                  # Data access layer
│   ├── model/
│   │   └── User.java                            # JPA entity
│   ├── event/
│   │   ├── ApplicationEventListener.java        # Event listeners
│   │   └── CustomEventPublisher.java           # Custom events
│   └── bean/
│       ├── LifecycleDemoBean.java               # Lifecycle callbacks demo
│       ├── BeanPostProcessorDemo.java           # Bean post processor
│       └── ScopedBeanDemo.java                  # Bean scopes demo
└── src/main/resources/
    ├── application.properties                   # Main configuration
    └── application-dev.properties               # Development profile
```

## 🔄 Lifecycle Order

1. **Constructor** - Tạo instance
2. **@PostConstruct** - Khởi tạo sau khi inject dependencies  
3. **ApplicationStartedEvent** - Context đã start
4. **CommandLineRunner.run()** - Chạy command line logic
5. **ApplicationRunner.run()** - Chạy application logic
6. **ApplicationReadyEvent** - Sẵn sàng serve requests
7. **ContextRefreshedEvent** - Context đã refresh
8. **Application Running...** - Ứng dụng đang chạy
9. **ContextClosedEvent** - Context đang đóng (shutdown)
10. **@PreDestroy** - Cleanup trước khi destroy

## 🚀 Chạy ứng dụng

### Prerequisites
- Java 17+
- Maven 3.6+

### Chạy với profile mặc định
```bash
cd spring-boot-lifecycle-demo
mvn spring-boot:run
```

### Chạy với development profile
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Build và chạy JAR
```bash
mvn clean package
java -jar target/spring-boot-lifecycle-demo-0.0.1-SNAPSHOT.jar
```

## 📡 API Endpoints

### User Management
- `GET /api/users` - Lấy danh sách users
- `GET /api/users/{id}` - Lấy user theo ID
- `POST /api/users` - Tạo user mới
- `PUT /api/users/{id}` - Cập nhật user
- `DELETE /api/users/{id}` - Xóa user
- `GET /api/users/health` - Health check

### Lifecycle Information  
- `GET /api/lifecycle/context` - Thông tin ApplicationContext
- `GET /api/lifecycle/demo` - Thông tin demo lifecycle

### Actuator Endpoints
- `GET /actuator/health` - Health status
- `GET /actuator/info` - Application info
- `GET /actuator/beans` - Bean information
- `GET /actuator/env` - Environment properties

## 🗄️ Database

Ứng dụng sử dụng H2 in-memory database:
- **URL**: `jdbc:h2:mem:lifecycledb`
- **Username**: `sa`
- **Password**: (empty)
- **H2 Console**: http://localhost:8080/h2-console

## 📊 Monitoring Logs

Khi chạy ứng dụng, bạn sẽ thấy các log với emoji để dễ theo dõi lifecycle:

- 🚀 Application startup
- 🔧 Configuration và Bean creation
- 👤 Service layer operations
- 🗄️ Repository operations  
- 🌐 Controller operations
- 🎧 Event handling
- 🔄 Bean lifecycle
- 📢 Custom events

## 🧪 Testing

Để test lifecycle, bạn có thể:

1. **Khởi động ứng dụng** và quan sát logs
2. **Gọi API endpoints** để thấy request lifecycle
3. **Shutdown ứng dụng** (Ctrl+C) để thấy cleanup lifecycle
4. **Thay đổi profiles** để thấy conditional beans

## 📚 Học từ Demo này

Demo này giúp hiểu:
- Thứ tự khởi tạo components trong Spring Boot
- Cách dependency injection hoạt động
- Event-driven architecture
- Bean lifecycle management
- Configuration và property binding
- Layered architecture best practices
