package com.example.lifecycle;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

/**
 * Main Spring Boot Application class demonstrating lifecycle events
 * 
 * Lifecycle order:
 * 1. Constructor
 * 2. @PostConstruct methods
 * 3. ApplicationStartedEvent
 * 4. CommandLineRunner.run()
 * 5. ApplicationRunner.run()
 * 6. ApplicationReadyEvent
 * 7. ContextRefreshedEvent
 * ... application runs ...
 * 8. ContextClosedEvent
 * 9. @PreDestroy methods
 */
@SpringBootApplication
public class SpringBootLifecycleDemoApplication 
    implements Command<PERSON>ineRunner, ApplicationRunner, ApplicationListener<ApplicationReadyEvent> {

    private static final Logger logger = LoggerFactory.getLogger(SpringBootLifecycleDemoApplication.class);

    public SpringBootLifecycleDemoApplication() {
        logger.info("🚀 [LIFECYCLE] 1. SpringBootLifecycleDemoApplication Constructor called");
    }

    public static void main(String[] args) {
        logger.info("🌟 [LIFECYCLE] 0. Starting Spring Boot Application...");
        SpringApplication.run(SpringBootLifecycleDemoApplication.class, args);
        logger.info("🎯 [LIFECYCLE] Application startup completed!");
    }

    /**
     * CommandLineRunner - runs after ApplicationContext is loaded
     * Executes before ApplicationRunner
     */
    @Override
    public void run(String... args) throws Exception {
        logger.info("🏃 [LIFECYCLE] 4. CommandLineRunner.run() - Application arguments: {}", 
                   args.length > 0 ? String.join(", ", args) : "none");
    }

    /**
     * ApplicationRunner - runs after CommandLineRunner
     * Has access to parsed ApplicationArguments
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("🏃‍♀️ [LIFECYCLE] 5. ApplicationRunner.run() - Parsed arguments: {}", 
                   args.getOptionNames());
    }

    /**
     * ApplicationListener implementation for ApplicationReadyEvent
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        logger.info("✅ [LIFECYCLE] 6. ApplicationReadyEvent - Application is ready to serve requests");
        logger.info("📊 [LIFECYCLE] Application startup time: {} ms", 
                   event.getTimeTaken().toMillis());
    }

    /**
     * Event listener for ApplicationStartedEvent
     */
    @EventListener
    public void handleApplicationStarted(ApplicationStartedEvent event) {
        logger.info("🎬 [LIFECYCLE] 3. ApplicationStartedEvent - Application context started");
    }

    /**
     * Event listener for ContextRefreshedEvent
     */
    @EventListener
    public void handleContextRefreshed(ContextRefreshedEvent event) {
        logger.info("🔄 [LIFECYCLE] 7. ContextRefreshedEvent - Application context refreshed");
        logger.info("📈 [LIFECYCLE] Bean count: {}", event.getApplicationContext().getBeanDefinitionCount());
    }

    /**
     * Event listener for ContextClosedEvent
     */
    @EventListener
    public void handleContextClosed(ContextClosedEvent event) {
        logger.info("🛑 [LIFECYCLE] 8. ContextClosedEvent - Application context is closing");
    }
}
