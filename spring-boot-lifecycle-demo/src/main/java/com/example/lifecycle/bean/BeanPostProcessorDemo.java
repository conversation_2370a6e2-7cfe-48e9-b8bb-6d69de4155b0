package com.example.lifecycle.bean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * BeanPostProcessor demonstrating bean processing lifecycle
 * This processor will be called for ALL beans in the application context
 */
@Component
public class BeanPostProcessorDemo implements BeanPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(BeanPostProcessorDemo.class);

    public BeanPostProcessorDemo() {
        logger.info("🔧 [BEAN-PROCESSOR] BeanPostProcessor Constructor called");
    }

    /**
     * Called BEFORE any initialization callbacks (@PostConstruct, InitializingBean.afterPropertiesSet())
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        // Only log for our custom beans to avoid too much noise
        if (beanName.contains("lifecycle") || beanName.contains("user")) {
            logger.info("🔧 [BEAN-PROCESSOR] postProcessBeforeInitialization - Bean: {} ({})", 
                       beanName, bean.getClass().getSimpleName());
        }
        return bean;
    }

    /**
     * Called AFTER all initialization callbacks
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // Only log for our custom beans to avoid too much noise
        if (beanName.contains("lifecycle") || beanName.contains("user")) {
            logger.info("🔧 [BEAN-PROCESSOR] postProcessAfterInitialization - Bean: {} ({})", 
                       beanName, bean.getClass().getSimpleName());
        }
        return bean;
    }
}
