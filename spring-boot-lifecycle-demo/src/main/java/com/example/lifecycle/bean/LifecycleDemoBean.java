package com.example.lifecycle.bean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.*;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * Bean demonstrating all possible lifecycle callbacks and interfaces
 */
@Component
public class LifecycleDemoBean implements 
    BeanNameAware, 
    BeanFactoryAware, 
    ApplicationContextAware, 
    InitializingBean, 
    DisposableBean {

    private static final Logger logger = LoggerFactory.getLogger(LifecycleDemoBean.class);

    private String beanName;
    private BeanFactory beanFactory;
    private ApplicationContext applicationContext;

    public LifecycleDemoBean() {
        logger.info("🔄 [LIFECYCLE-BEAN] 1. Constructor called");
    }

    /**
     * BeanNameAware interface - called after constructor
     */
    @Override
    public void setBeanName(String name) {
        this.beanName = name;
        logger.info("🔄 [LIFECYCLE-BEAN] 2. setBeanName() called - Bean name: {}", name);
    }

    /**
     * BeanFactoryAware interface - called after setBeanName
     */
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
        logger.info("🔄 [LIFECYCLE-BEAN] 3. setBeanFactory() called - BeanFactory set");
    }

    /**
     * ApplicationContextAware interface - called after setBeanFactory
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        logger.info("🔄 [LIFECYCLE-BEAN] 4. setApplicationContext() called - ApplicationContext set");
    }

    /**
     * @PostConstruct annotation - called after all setters
     */
    @PostConstruct
    public void postConstruct() {
        logger.info("🔄 [LIFECYCLE-BEAN] 5. @PostConstruct called - Custom initialization");
        logger.info("🔄 [LIFECYCLE-BEAN] Bean name from context: {}", beanName);
        logger.info("🔄 [LIFECYCLE-BEAN] Total beans in context: {}", 
                   applicationContext.getBeanDefinitionCount());
    }

    /**
     * InitializingBean interface - called after @PostConstruct
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        logger.info("🔄 [LIFECYCLE-BEAN] 6. afterPropertiesSet() called - Properties set");
    }

    /**
     * Custom business method
     */
    public void doSomething() {
        logger.info("🔄 [LIFECYCLE-BEAN] doSomething() called - Performing business logic");
    }

    /**
     * @PreDestroy annotation - called before destruction
     */
    @PreDestroy
    public void preDestroy() {
        logger.info("🔄 [LIFECYCLE-BEAN] 7. @PreDestroy called - Cleanup before destruction");
    }

    /**
     * DisposableBean interface - called after @PreDestroy
     */
    @Override
    public void destroy() throws Exception {
        logger.info("🔄 [LIFECYCLE-BEAN] 8. destroy() called - Final cleanup");
    }

    // Getters for injected dependencies
    public String getBeanName() {
        return beanName;
    }

    public BeanFactory getBeanFactory() {
        return beanFactory;
    }

    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
