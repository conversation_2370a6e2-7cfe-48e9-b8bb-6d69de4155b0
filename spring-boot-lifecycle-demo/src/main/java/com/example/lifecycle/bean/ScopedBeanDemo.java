package com.example.lifecycle.bean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Demonstration of different bean scopes and their lifecycle
 */
public class ScopedBeanDemo {

    /**
     * Singleton scope bean (default) - one instance per Spring container
     */
    @Component
    @Scope("singleton")
    public static class SingletonBean {
        private static final Logger logger = LoggerFactory.getLogger(SingletonBean.class);
        private static final AtomicInteger instanceCounter = new AtomicInteger(0);
        private final int instanceId;

        public SingletonBean() {
            instanceId = instanceCounter.incrementAndGet();
            logger.info("🔄 [SINGLETON] Constructor called - Instance #{}", instanceId);
        }

        @PostConstruct
        public void init() {
            logger.info("🔄 [SINGLETON] @PostConstruct - Instance #{} initialized", instanceId);
        }

        @PreDestroy
        public void cleanup() {
            logger.info("🔄 [SINGLETON] @PreDestroy - Instance #{} cleanup", instanceId);
        }

        public int getInstanceId() {
            return instanceId;
        }

        public void doWork() {
            logger.info("🔄 [SINGLETON] doWork() called on instance #{}", instanceId);
        }
    }

    /**
     * Prototype scope bean - new instance every time it's requested
     */
    @Component
    @Scope("prototype")
    public static class PrototypeBean {
        private static final Logger logger = LoggerFactory.getLogger(PrototypeBean.class);
        private static final AtomicInteger instanceCounter = new AtomicInteger(0);
        private final int instanceId;

        public PrototypeBean() {
            instanceId = instanceCounter.incrementAndGet();
            logger.info("🔄 [PROTOTYPE] Constructor called - Instance #{}", instanceId);
        }

        @PostConstruct
        public void init() {
            logger.info("🔄 [PROTOTYPE] @PostConstruct - Instance #{} initialized", instanceId);
        }

        @PreDestroy
        public void cleanup() {
            // Note: @PreDestroy is NOT called for prototype beans by default
            // Spring doesn't manage the complete lifecycle of prototype beans
            logger.info("🔄 [PROTOTYPE] @PreDestroy - Instance #{} cleanup", instanceId);
        }

        public int getInstanceId() {
            return instanceId;
        }

        public void doWork() {
            logger.info("🔄 [PROTOTYPE] doWork() called on instance #{}", instanceId);
        }
    }
}
