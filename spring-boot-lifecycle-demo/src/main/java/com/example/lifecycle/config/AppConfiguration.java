package com.example.lifecycle.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * Configuration class demonstrating bean lifecycle and dependency injection
 */
@Configuration
@EnableConfigurationProperties({AppConfiguration.AppProperties.class})
public class AppConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(AppConfiguration.class);

    @Value("${app.name:Spring Boot Lifecycle Demo}")
    private String appName;

    public AppConfiguration() {
        logger.info("🔧 [CONFIG] AppConfiguration Constructor called");
    }

    @PostConstruct
    public void init() {
        logger.info("🔧 [CONFIG] 2. AppConfiguration @PostConstruct - Configuration initialized");
        logger.info("🔧 [CONFIG] Application name: {}", appName);
    }

    @PreDestroy
    public void cleanup() {
        logger.info("🔧 [CONFIG] 9. AppConfiguration @PreDestroy - Configuration cleanup");
    }

    /**
     * Bean with custom initialization and destruction methods
     */
    @Bean(initMethod = "customInit", destroyMethod = "customDestroy")
    public DatabaseConnectionPool databaseConnectionPool() {
        logger.info("🔧 [CONFIG] Creating DatabaseConnectionPool bean");
        return new DatabaseConnectionPool();
    }

    /**
     * Conditional bean - only created in 'dev' profile
     */
    @Bean
    @Profile("dev")
    public DebugService debugService() {
        logger.info("🔧 [CONFIG] Creating DebugService bean (dev profile only)");
        return new DebugService();
    }

    /**
     * Configuration Properties class
     */
    @ConfigurationProperties(prefix = "app")
    public static class AppProperties {
        private String name = "Default App";
        private String version = "1.0.0";
        private Database database = new Database();

        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        
        public Database getDatabase() { return database; }
        public void setDatabase(Database database) { this.database = database; }

        public static class Database {
            private String url = "jdbc:h2:mem:testdb";
            private String username = "sa";
            private String password = "";
            private int maxConnections = 10;

            // Getters and setters
            public String getUrl() { return url; }
            public void setUrl(String url) { this.url = url; }
            
            public String getUsername() { return username; }
            public void setUsername(String username) { this.username = username; }
            
            public String getPassword() { return password; }
            public void setPassword(String password) { this.password = password; }
            
            public int getMaxConnections() { return maxConnections; }
            public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }
        }
    }

    /**
     * Example bean with lifecycle methods
     */
    public static class DatabaseConnectionPool {
        private static final Logger logger = LoggerFactory.getLogger(DatabaseConnectionPool.class);

        public DatabaseConnectionPool() {
            logger.info("💾 [BEAN] DatabaseConnectionPool Constructor");
        }

        public void customInit() {
            logger.info("💾 [BEAN] DatabaseConnectionPool customInit() - Initializing connection pool");
        }

        public void customDestroy() {
            logger.info("💾 [BEAN] DatabaseConnectionPool customDestroy() - Closing connection pool");
        }
    }

    /**
     * Debug service for development profile
     */
    public static class DebugService {
        private static final Logger logger = LoggerFactory.getLogger(DebugService.class);

        public DebugService() {
            logger.info("🐛 [DEBUG] DebugService Constructor - Debug mode enabled");
        }

        public void logDebugInfo(String message) {
            logger.debug("🐛 [DEBUG] {}", message);
        }
    }
}
