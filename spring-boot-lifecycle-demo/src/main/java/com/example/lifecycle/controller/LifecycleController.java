package com.example.lifecycle.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller to demonstrate Spring Boot lifecycle information
 */
@RestController
@RequestMapping("/api/lifecycle")
public class LifecycleController {

    private static final Logger logger = LoggerFactory.getLogger(LifecycleController.class);

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * Get application context information
     * GET /api/lifecycle/context
     */
    @GetMapping("/context")
    public ResponseEntity<Map<String, Object>> getContextInfo() {
        logger.info("🌐 [LIFECYCLE] GET /api/lifecycle/context - Getting context information");
        
        Map<String, Object> contextInfo = new HashMap<>();
        contextInfo.put("applicationName", applicationContext.getApplicationName());
        contextInfo.put("beanDefinitionCount", applicationContext.getBeanDefinitionCount());
        contextInfo.put("startupDate", applicationContext.getStartupDate());
        contextInfo.put("displayName", applicationContext.getDisplayName());
        
        // Get some bean names
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        contextInfo.put("totalBeans", beanNames.length);
        
        // Filter to show only our custom beans
        Map<String, String> customBeans = new HashMap<>();
        for (String beanName : beanNames) {
            if (beanName.contains("lifecycle") || beanName.contains("user")) {
                Object bean = applicationContext.getBean(beanName);
                customBeans.put(beanName, bean.getClass().getSimpleName());
            }
        }
        contextInfo.put("customBeans", customBeans);
        
        return ResponseEntity.ok(contextInfo);
    }

    /**
     * Get lifecycle demonstration info
     * GET /api/lifecycle/demo
     */
    @GetMapping("/demo")
    public ResponseEntity<Map<String, Object>> getLifecycleDemo() {
        logger.info("🌐 [LIFECYCLE] GET /api/lifecycle/demo - Lifecycle demonstration");
        
        Map<String, Object> lifecycleInfo = new HashMap<>();
        lifecycleInfo.put("title", "Spring Boot Lifecycle Demo");
        lifecycleInfo.put("description", "This application demonstrates the complete Spring Boot lifecycle");
        
        Map<String, String> lifecycleSteps = new HashMap<>();
        lifecycleSteps.put("1", "Application Constructor");
        lifecycleSteps.put("2", "@PostConstruct methods");
        lifecycleSteps.put("3", "ApplicationStartedEvent");
        lifecycleSteps.put("4", "CommandLineRunner.run()");
        lifecycleSteps.put("5", "ApplicationRunner.run()");
        lifecycleSteps.put("6", "ApplicationReadyEvent");
        lifecycleSteps.put("7", "ContextRefreshedEvent");
        lifecycleSteps.put("8", "Application Running...");
        lifecycleSteps.put("9", "ContextClosedEvent (on shutdown)");
        lifecycleSteps.put("10", "@PreDestroy methods (on shutdown)");
        
        lifecycleInfo.put("lifecycleSteps", lifecycleSteps);
        
        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("GET /api/users", "Get all users");
        endpoints.put("GET /api/users/{id}", "Get user by ID");
        endpoints.put("POST /api/users", "Create new user");
        endpoints.put("PUT /api/users/{id}", "Update user");
        endpoints.put("DELETE /api/users/{id}", "Delete user");
        endpoints.put("GET /api/users/health", "Health check");
        endpoints.put("GET /api/lifecycle/context", "Application context info");
        endpoints.put("GET /api/lifecycle/demo", "This endpoint");
        
        lifecycleInfo.put("availableEndpoints", endpoints);
        
        return ResponseEntity.ok(lifecycleInfo);
    }
}
