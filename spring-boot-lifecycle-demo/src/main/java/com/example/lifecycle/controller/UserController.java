package com.example.lifecycle.controller;

import com.example.lifecycle.model.User;
import com.example.lifecycle.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Optional;

/**
 * REST Controller demonstrating web layer lifecycle
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
        logger.info("🌐 [CONTROLLER] UserController Constructor - Dependencies injected");
    }

    @PostConstruct
    public void init() {
        logger.info("🌐 [CONTROLLER] UserController @PostConstruct - Controller initialized");
    }

    @PreDestroy
    public void cleanup() {
        logger.info("🌐 [CONTROLLER] UserController @PreDestroy - Controller cleanup");
    }

    /**
     * Get all users
     * GET /api/users
     */
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        logger.info("🌐 [CONTROLLER] GET /api/users - Fetching all users");
        List<User> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    /**
     * Get user by ID
     * GET /api/users/{id}
     */
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        logger.info("🌐 [CONTROLLER] GET /api/users/{} - Fetching user by ID", id);
        Optional<User> user = userService.getUserById(id);
        
        if (user.isPresent()) {
            return ResponseEntity.ok(user.get());
        } else {
            logger.warn("🌐 [CONTROLLER] User not found with ID: {}", id);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Create new user
     * POST /api/users
     */
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody CreateUserRequest request) {
        logger.info("🌐 [CONTROLLER] POST /api/users - Creating user: {}", request.getName());
        
        try {
            User user = userService.createUser(request.getName(), request.getEmail());
            return ResponseEntity.status(HttpStatus.CREATED).body(user);
        } catch (IllegalArgumentException e) {
            logger.error("🌐 [CONTROLLER] Error creating user: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Update user
     * PUT /api/users/{id}
     */
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody UpdateUserRequest request) {
        logger.info("🌐 [CONTROLLER] PUT /api/users/{} - Updating user", id);
        
        try {
            User user = userService.updateUser(id, request.getName(), request.getEmail());
            return ResponseEntity.ok(user);
        } catch (IllegalArgumentException e) {
            logger.error("🌐 [CONTROLLER] Error updating user: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Delete user
     * DELETE /api/users/{id}
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        logger.info("🌐 [CONTROLLER] DELETE /api/users/{} - Deleting user", id);
        
        try {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            logger.error("🌐 [CONTROLLER] Error deleting user: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Health check endpoint
     * GET /api/users/health
     */
    @GetMapping("/health")
    public ResponseEntity<HealthResponse> health() {
        logger.info("🌐 [CONTROLLER] GET /api/users/health - Health check");
        HealthResponse response = new HealthResponse("OK", "UserController is healthy");
        return ResponseEntity.ok(response);
    }

    // Request/Response DTOs
    public static class CreateUserRequest {
        private String name;
        private String email;

        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class UpdateUserRequest {
        private String name;
        private String email;

        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class HealthResponse {
        private String status;
        private String message;

        public HealthResponse(String status, String message) {
            this.status = status;
            this.message = message;
        }

        // Getters and setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
