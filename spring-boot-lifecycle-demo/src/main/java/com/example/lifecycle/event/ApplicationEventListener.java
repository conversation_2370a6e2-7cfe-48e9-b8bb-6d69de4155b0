package com.example.lifecycle.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.*;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * Component demonstrating various Spring Boot application events
 */
@Component
public class ApplicationEventListener {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationEventListener.class);

    public ApplicationEventListener() {
        logger.info("🎧 [EVENT] ApplicationEventListener Constructor called");
    }

    @PostConstruct
    public void init() {
        logger.info("🎧 [EVENT] ApplicationEventListener @PostConstruct - Event listener initialized");
    }

    @PreDestroy
    public void cleanup() {
        logger.info("🎧 [EVENT] ApplicationEventListener @PreDestroy - Event listener cleanup");
    }

    /**
     * Listens for ApplicationStartingEvent - very early in the lifecycle
     */
    @EventListener
    public void handleApplicationStarting(ApplicationStartingEvent event) {
        logger.info("🎧 [EVENT] ApplicationStartingEvent - Application is starting");
    }

    /**
     * Listens for ApplicationEnvironmentPreparedEvent
     */
    @EventListener
    public void handleApplicationEnvironmentPrepared(ApplicationEnvironmentPreparedEvent event) {
        logger.info("🎧 [EVENT] ApplicationEnvironmentPreparedEvent - Environment prepared");
        logger.info("🎧 [EVENT] Active profiles: {}", 
                   String.join(", ", event.getEnvironment().getActiveProfiles()));
    }

    /**
     * Listens for ApplicationContextInitializedEvent
     */
    @EventListener
    public void handleApplicationContextInitialized(ApplicationContextInitializedEvent event) {
        logger.info("🎧 [EVENT] ApplicationContextInitializedEvent - Context initialized");
    }

    /**
     * Listens for ApplicationPreparedEvent
     */
    @EventListener
    public void handleApplicationPrepared(ApplicationPreparedEvent event) {
        logger.info("🎧 [EVENT] ApplicationPreparedEvent - Application prepared");
    }

    /**
     * Listens for ApplicationStartedEvent
     */
    @EventListener
    public void handleApplicationStarted(ApplicationStartedEvent event) {
        logger.info("🎧 [EVENT] ApplicationStartedEvent - Application started");
        logger.info("🎧 [EVENT] Startup time: {} ms", event.getTimeTaken().toMillis());
    }

    /**
     * Listens for ApplicationReadyEvent
     */
    @EventListener
    public void handleApplicationReady(ApplicationReadyEvent event) {
        logger.info("🎧 [EVENT] ApplicationReadyEvent - Application ready to serve requests");
        logger.info("🎧 [EVENT] Total startup time: {} ms", event.getTimeTaken().toMillis());
    }

    /**
     * Listens for ApplicationFailedEvent
     */
    @EventListener
    public void handleApplicationFailed(ApplicationFailedEvent event) {
        logger.error("🎧 [EVENT] ApplicationFailedEvent - Application failed to start");
        logger.error("🎧 [EVENT] Exception: ", event.getException());
    }

    /**
     * Listens for ContextRefreshedEvent
     */
    @EventListener
    public void handleContextRefreshed(ContextRefreshedEvent event) {
        logger.info("🎧 [EVENT] ContextRefreshedEvent - Context refreshed");
        logger.info("🎧 [EVENT] Context: {}", event.getApplicationContext().getDisplayName());
    }

    /**
     * Listens for ContextClosedEvent
     */
    @EventListener
    public void handleContextClosed(ContextClosedEvent event) {
        logger.info("🎧 [EVENT] ContextClosedEvent - Context is closing");
        logger.info("🎧 [EVENT] Context: {}", event.getApplicationContext().getDisplayName());
    }
}

/**
 * Alternative implementation using ApplicationListener interface
 */
@Component
class AlternativeEventListener implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger logger = LoggerFactory.getLogger(AlternativeEventListener.class);

    public AlternativeEventListener() {
        logger.info("🎧 [ALT-EVENT] AlternativeEventListener Constructor called");
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        logger.info("🎧 [ALT-EVENT] ApplicationReadyEvent received via ApplicationListener interface");
        logger.info("🎧 [ALT-EVENT] Application is ready! Time taken: {} ms", 
                   event.getTimeTaken().toMillis());
    }

    @PostConstruct
    public void init() {
        logger.info("🎧 [ALT-EVENT] AlternativeEventListener @PostConstruct");
    }

    @PreDestroy
    public void cleanup() {
        logger.info("🎧 [ALT-EVENT] AlternativeEventListener @PreDestroy");
    }
}
