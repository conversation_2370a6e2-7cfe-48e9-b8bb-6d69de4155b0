package com.example.lifecycle.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * Component demonstrating custom event publishing and listening
 */
@Component
public class CustomEventPublisher {

    private static final Logger logger = LoggerFactory.getLogger(CustomEventPublisher.class);

    private final ApplicationEventPublisher eventPublisher;

    public CustomEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
        logger.info("📢 [CUSTOM-EVENT] CustomEventPublisher Constructor - Event publisher injected");
    }

    @PostConstruct
    public void init() {
        logger.info("📢 [CUSTOM-EVENT] CustomEventPublisher @PostConstruct - Publishing custom events");
        
        // Publish some custom events after initialization
        publishUserCreatedEvent("<PERSON>", "<EMAIL>");
        publishSystemReadyEvent("All systems operational");
    }

    /**
     * Publish a custom user created event
     */
    public void publishUserCreatedEvent(String userName, String userEmail) {
        logger.info("📢 [CUSTOM-EVENT] Publishing UserCreatedEvent for: {}", userName);
        UserCreatedEvent event = new UserCreatedEvent(this, userName, userEmail);
        eventPublisher.publishEvent(event);
    }

    /**
     * Publish a custom system ready event
     */
    public void publishSystemReadyEvent(String message) {
        logger.info("📢 [CUSTOM-EVENT] Publishing SystemReadyEvent: {}", message);
        SystemReadyEvent event = new SystemReadyEvent(this, message);
        eventPublisher.publishEvent(event);
    }

    /**
     * Listen for custom UserCreatedEvent
     */
    @EventListener
    public void handleUserCreated(UserCreatedEvent event) {
        logger.info("📢 [CUSTOM-EVENT] UserCreatedEvent received - User: {} ({})", 
                   event.getUserName(), event.getUserEmail());
    }

    /**
     * Listen for custom SystemReadyEvent
     */
    @EventListener
    public void handleSystemReady(SystemReadyEvent event) {
        logger.info("📢 [CUSTOM-EVENT] SystemReadyEvent received - Message: {}", event.getMessage());
    }

    /**
     * Custom event for user creation
     */
    public static class UserCreatedEvent extends ApplicationEvent {
        private final String userName;
        private final String userEmail;

        public UserCreatedEvent(Object source, String userName, String userEmail) {
            super(source);
            this.userName = userName;
            this.userEmail = userEmail;
        }

        public String getUserName() {
            return userName;
        }

        public String getUserEmail() {
            return userEmail;
        }
    }

    /**
     * Custom event for system ready notification
     */
    public static class SystemReadyEvent extends ApplicationEvent {
        private final String message;

        public SystemReadyEvent(Object source, String message) {
            super(source);
            this.message = message;
        }

        public String getMessage() {
            return message;
        }
    }
}
