package com.example.lifecycle.repository;

import com.example.lifecycle.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.Optional;

/**
 * Repository interface demonstrating Spring Data JPA lifecycle
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * Custom query method - Spring Data JPA will implement this automatically
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Check if user exists by email
     */
    boolean existsByEmail(String email);
}

/**
 * Custom repository implementation demonstrating component lifecycle
 */
@Repository
class UserRepositoryImpl {
    
    private static final Logger logger = LoggerFactory.getLogger(UserRepositoryImpl.class);
    
    public UserRepositoryImpl() {
        logger.info("🗄️ [REPOSITORY] UserRepositoryImpl Constructor called");
    }
    
    @PostConstruct
    public void init() {
        logger.info("🗄️ [REPOSITORY] UserRepositoryImpl @PostConstruct - Repository initialized");
    }
    
    @PreDestroy
    public void cleanup() {
        logger.info("🗄️ [REPOSITORY] UserRepositoryImpl @PreDestroy - Repository cleanup");
    }
    
    public void performCustomOperation() {
        logger.info("🗄️ [REPOSITORY] Performing custom repository operation");
    }
}
