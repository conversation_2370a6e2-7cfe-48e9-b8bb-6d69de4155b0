package com.example.lifecycle.service;

import com.example.lifecycle.model.User;
import com.example.lifecycle.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Optional;

/**
 * Service class demonstrating dependency injection and component lifecycle
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    private final UserRepository userRepository;

    // Constructor injection (recommended)
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
        logger.info("👤 [SERVICE] UserService Constructor - Dependencies injected");
    }

    @PostConstruct
    public void init() {
        logger.info("👤 [SERVICE] UserService @PostConstruct - Service initialized");
        // Initialize sample data
        initializeSampleData();
    }

    @PreDestroy
    public void cleanup() {
        logger.info("👤 [SERVICE] UserService @PreDestroy - Service cleanup");
    }

    /**
     * Create a new user
     */
    public User createUser(String name, String email) {
        logger.info("👤 [SERVICE] Creating user: {} - {}", name, email);
        
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("User with email " + email + " already exists");
        }
        
        User user = new User(name, email);
        User savedUser = userRepository.save(user);
        logger.info("👤 [SERVICE] User created with ID: {}", savedUser.getId());
        return savedUser;
    }

    /**
     * Get all users
     */
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        logger.info("👤 [SERVICE] Fetching all users");
        List<User> users = userRepository.findAll();
        logger.info("👤 [SERVICE] Found {} users", users.size());
        return users;
    }

    /**
     * Get user by ID
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        logger.info("👤 [SERVICE] Fetching user by ID: {}", id);
        return userRepository.findById(id);
    }

    /**
     * Get user by email
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        logger.info("👤 [SERVICE] Fetching user by email: {}", email);
        return userRepository.findByEmail(email);
    }

    /**
     * Update user
     */
    public User updateUser(Long id, String name, String email) {
        logger.info("👤 [SERVICE] Updating user ID: {}", id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + id));
        
        // Check if email is already taken by another user
        Optional<User> existingUser = userRepository.findByEmail(email);
        if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
            throw new IllegalArgumentException("Email " + email + " is already taken");
        }
        
        user.setName(name);
        user.setEmail(email);
        
        User updatedUser = userRepository.save(user);
        logger.info("👤 [SERVICE] User updated: {}", updatedUser);
        return updatedUser;
    }

    /**
     * Delete user
     */
    public void deleteUser(Long id) {
        logger.info("👤 [SERVICE] Deleting user ID: {}", id);
        
        if (!userRepository.existsById(id)) {
            throw new IllegalArgumentException("User not found with ID: " + id);
        }
        
        userRepository.deleteById(id);
        logger.info("👤 [SERVICE] User deleted with ID: {}", id);
    }

    /**
     * Initialize sample data
     */
    private void initializeSampleData() {
        logger.info("👤 [SERVICE] Initializing sample data");
        
        if (userRepository.count() == 0) {
            User user1 = new User("John Doe", "<EMAIL>");
            User user2 = new User("Jane Smith", "<EMAIL>");
            
            userRepository.save(user1);
            userRepository.save(user2);
            
            logger.info("👤 [SERVICE] Sample data initialized: 2 users created");
        } else {
            logger.info("👤 [SERVICE] Sample data already exists, skipping initialization");
        }
    }
}
