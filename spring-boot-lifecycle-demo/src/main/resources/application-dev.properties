# Development Profile Configuration

# Enable debug logging for development
logging.level.com.example.lifecycle=DEBUG
logging.level.org.springframework=DEBUG
logging.level.org.springframework.web=DEBUG

# H2 Console settings for development
spring.h2.console.enabled=true
spring.h2.console.settings.web-allow-others=true

# Show more detailed SQL logging
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Development-specific properties
demo.startup.message=Development Mode - Spring Boot Lifecycle Demo!
demo.debug.enabled=true

# Actuator - expose all endpoints in development
management.endpoints.web.exposure.include=*
