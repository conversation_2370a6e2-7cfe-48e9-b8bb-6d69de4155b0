package com.example.lifecycle;

import com.example.lifecycle.controller.UserController;
import com.example.lifecycle.service.UserService;
import com.example.lifecycle.repository.UserRepository;
import com.example.lifecycle.model.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for Spring Boot Lifecycle Demo Application
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class SpringBootLifecycleDemoApplicationTests {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserController userController;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    /**
     * Test that the application context loads successfully
     */
    @Test
    void contextLoads() {
        // Verify that all main components are loaded
        assertThat(userController).isNotNull();
        assertThat(userService).isNotNull();
        assertThat(userRepository).isNotNull();
    }

    /**
     * Test the lifecycle demo endpoint
     */
    @Test
    void testLifecycleDemoEndpoint() {
        String url = "http://localhost:" + port + "/api/lifecycle/demo";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("Spring Boot Lifecycle Demo");
        assertThat(response.getBody()).contains("lifecycleSteps");
    }

    /**
     * Test the users endpoint
     */
    @Test
    void testUsersEndpoint() {
        String url = "http://localhost:" + port + "/api/users";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("John Doe");
        assertThat(response.getBody()).contains("Jane Smith");
    }

    /**
     * Test user service functionality
     */
    @Test
    void testUserService() {
        List<User> users = userService.getAllUsers();
        assertThat(users).hasSize(2);
        
        User firstUser = users.get(0);
        assertThat(firstUser.getName()).isNotNull();
        assertThat(firstUser.getEmail()).isNotNull();
        assertThat(firstUser.getCreatedAt()).isNotNull();
    }

    /**
     * Test application context information endpoint
     */
    @Test
    void testContextInfoEndpoint() {
        String url = "http://localhost:" + port + "/api/lifecycle/context";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("beanDefinitionCount");
        assertThat(response.getBody()).contains("customBeans");
    }

    /**
     * Test health endpoint
     */
    @Test
    void testHealthEndpoint() {
        String url = "http://localhost:" + port + "/api/users/health";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("OK");
        assertThat(response.getBody()).contains("UserController is healthy");
    }
}
