{"groups": [{"name": "app", "type": "com.example.lifecycle.config.AppConfiguration$AppProperties", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties"}, {"name": "app.database", "type": "com.example.lifecycle.config.AppConfiguration$AppProperties$Database", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties", "sourceMethod": "getDatabase()"}], "properties": [{"name": "app.database.max-connections", "type": "java.lang.Integer", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties$Database", "defaultValue": 10}, {"name": "app.database.password", "type": "java.lang.String", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties$Database", "defaultValue": ""}, {"name": "app.database.url", "type": "java.lang.String", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties$Database", "defaultValue": "jdbc:h2:mem:testdb"}, {"name": "app.database.username", "type": "java.lang.String", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties$Database", "defaultValue": "sa"}, {"name": "app.name", "type": "java.lang.String", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties", "defaultValue": "<PERSON><PERSON><PERSON>"}, {"name": "app.version", "type": "java.lang.String", "sourceType": "com.example.lifecycle.config.AppConfiguration$AppProperties", "defaultValue": "1.0.0"}], "hints": []}