# Spring Boot Lifecycle Demo Application Properties

# Application Information
app.name=Spring Boot Lifecycle Demo
app.version=1.0.0
app.description=Demonstrating complete Spring Boot lifecycle

# Database Configuration (H2 in-memory database)
app.database.url=jdbc:h2:mem:lifecycledb
app.database.username=sa
app.database.password=
app.database.max-connections=20

# Spring DataSource Configuration
spring.datasource.url=${app.database.url}
spring.datasource.username=${app.database.username}
spring.datasource.password=${app.database.password}
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2 Console (for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Logging Configuration
logging.level.com.example.lifecycle=INFO
logging.level.org.springframework.boot=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,beans,env,configprops
management.endpoint.health.show-details=always
management.info.env.enabled=true

# Application Info for Actuator
info.app.name=${app.name}
info.app.version=${app.version}
info.app.description=${app.description}
info.app.java.version=17.0.11
info.app.spring-boot.version=3.2.0

# Custom Properties for demonstration
demo.startup.message=Welcome to Spring Boot Lifecycle Demo!
demo.features.user-management=true
demo.features.event-handling=true
demo.features.lifecycle-tracking=true
