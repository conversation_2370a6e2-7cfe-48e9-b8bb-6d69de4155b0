/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/config/AppConfiguration.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/bean/LifecycleDemoBean.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/bean/BeanPostProcessorDemo.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/service/UserService.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/controller/UserController.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/SpringBootLifecycleDemoApplication.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/repository/UserRepository.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/bean/ScopedBeanDemo.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/model/User.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/controller/LifecycleController.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/event/ApplicationEventListener.java
/Users/<USER>/Documents/demo/spring-boot-lifecycle-demo/src/main/java/com/example/lifecycle/event/CustomEventPublisher.java
