# Test Profile Configuration

# Use H2 in-memory database for tests
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Disable H2 console in tests
spring.h2.console.enabled=false

# Reduce logging noise in tests
logging.level.com.example.lifecycle=WARN
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN

# Test-specific properties
app.name=Spring Boot Lifecycle Demo - Test
demo.startup.message=Test Mode - Spring Boot Lifecycle Demo!
